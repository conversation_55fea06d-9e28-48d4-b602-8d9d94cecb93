import React from 'react';
import { GoPlay } from 'react-icons/go';
import { TbArrowDown, TbArrowUp } from 'react-icons/tb';
export const placeholder = '/images/placeholder.jpg';

export enum DrawAction {
  ZoomOut = 'zoomOut',
  ZoomIn = 'zoomIn',
  Move = 'move',
  Select = 'select',
  Text = 'text',
  Rectangle = 'rectangle',
  Image = 'image',
  Bubble = 'bubble',
  Circle = 'circle',
  Scribble = 'freedraw',
  Arrow = 'arrow',
  MarkArea = 'mark_area',
  Line = 'line',
  Delete = 'delete',
  Clear = 'clear',
  Undo = 'undo',
  Eraser = 'eraser',
}

export enum LayerOptions {
  SendBackward = 'sendBackward',
  SendToBack = 'sendToBack',
  SendForward = 'sendForward',
  SendToFront = 'sendToFront',
}

export const LAYER_OPTIONS = [
  {
    id: LayerOptions.SendToBack,
    label: 'Send to back',
    label_key: 'layer_options.send_to_back',
    icon: <TbArrowDown />,
  },
  // {
  //   id: LayerOptions.SendBackward,
  //   label: "Send backward",
  //   label_key: "layer_options.send_backward",
  //   icon: TbArrowDown,
  // },
  // {
  //   id: LayerOptions.SendForward,
  //   label: "Send forward",
  //   label_key: "layer_options.send_forward",
  //   icon: TbArrowUp,
  // },
  {
    id: LayerOptions.SendToFront,
    label: 'Send to front',
    label_key: 'layer_options.send_to_front',
    icon: <TbArrowUp />,
  },
];

export const ICON_FILL_COLOR = '#030064';

export enum SecondaryAction {
  Undo = 'undo',
  Redo = 'redo',
  ZoomIn = 'zoomIn',
  ZoomOut = 'zoomOut',
  ResetZoom = 'resetZoom',
}

export const TEMP_IMAGE_URL = placeholder;

export enum Plans {
  FREE = 'Free',
  STARTER = 'Starter',
  PLUS = 'Plus',
  PREMIUM = 'Premium',
}

interface ToolSet {
  title: string;
  title_key: string;
  category: string;
  entries: ToolItem[];
}

export interface ToolItem {
  id: number;
  path: string;
  video_url?: string;
  image_url?: string;
  title: string;
  title_key: string;
  content?: string;
  content_key?: string;
  recommended?: boolean;
  derivative?: boolean;
  footer?: boolean;
  model_name?: string;
}

export const aiTools: ToolSet[] = [
  {
    title: 'Assist with art, illustration and storyboarding',
    title_key: 'ai_tools.illustration.title',
    category: 'illustration',
    entries: [
      {
        id: -2,
        path: '/oc-maker',
        image_url: '/images/character_design.webp',
        video_url: undefined,
        title: 'Create Character',
        title_key: 'ai_tools.comic_generation.create_character.title',
        content:
          'Create original characters using AI and save them for future image generations.',
        content_key: 'ai_tools.comic_generation.create_character.content',
        recommended: false,
      },
      {
        id: 17,
        path: '/ai-anime-generator',
        image_url: '/images/examples/ai-anime-generator/cover.webp',
        video_url: undefined,
        title: 'Anime Art Generator',
        title_key: 'ai_tools.illustration.ai_anime_generator.title',
        content:
          'Generate anime-style images using your original characters or our character library.',
        content_key: 'ai_tools.illustration.ai_anime_generator.content',
        recommended: true,
      },
      {
        id: 2,
        path: '/playground',
        image_url: '/images/examples/photo-to-anime/cover.webp',
        video_url: undefined,
        title: 'AI Playground',
        title_key: 'ai_tools.illustration.ai_playground.title',
        content:
          'Convert photos to anime, manga, cartoon, and 30+ other art styles',
        content_key: 'ai_tools.illustration.ai_playground.content',
        recommended: true,
      },
      {
        id: 1,
        path: '/line_art_colorization',
        image_url: '/images/examples/line-art-colorize/input.jpg',
        video_url: undefined,
        title: 'Line Art Colorization',
        title_key: 'ai_tools.illustration.line_art_colorization.title',
        content: 'Add colors to your line art and sketches with AI.',
        content_key: 'ai_tools.illustration.line_art_colorization.content',
        recommended: true,
      },
      {
        id: 6,
        path: '/sketch_simplification',
        image_url: '/images/examples/sketch_simplifier/cover.png',
        video_url: undefined,
        title: 'Sketch Simplification',
        title_key: 'ai_tools.illustration.sketch_simplification.title',
        content:
          'Convert rough, messy sketches into clean, simplified line art.',
        content_key: 'ai_tools.illustration.sketch_simplification.content',
        recommended: true,
      },
      {
        id: 3,
        path: '/background-removal',
        image_url: '/images/bg-removal-cover.webp',
        video_url: undefined,
        title: 'Background Removal',
        title_key: 'ai_tools.illustration.background_removal.title',
        content: 'Remove background from images with AI.',
        content_key: 'ai_tools.illustration.background_removal.content',
        recommended: false,
      },
      {
        id: 4,
        path: '/image-upscaling',
        image_url: '/images/thumbnail/image-upscaling.jpeg',
        video_url: undefined,
        title: 'Image Upscaling',
        title_key: 'ai_tools.illustration.image_upscaling.title',
        content: 'Upscale your images with AI.',
        content_key: 'ai_tools.illustration.image_upscaling.content',
        recommended: false,
      },
      {
        id: 5,
        path: '/image-relighting',
        image_url: '/images/examples/image-relighting/head.webp',
        video_url: undefined,
        title: 'Image Relighting',
        title_key: 'ai_tools.illustration.image_relighting.title',
        content: 'Relight your images with AI.',
        content_key: 'ai_tools.illustration.image_relighting.content',
        recommended: false,
      },
      {
        id: 7,
        path: '/filter/ai-action-figure-generator',
        title: 'AI Action Figure Generator',
        title_key: 'ai_tools.illustration.ai_action_figure_generator.title',
        recommended: false,
        derivative: true,
        footer: true,
      },
      {
        id: 8,
        path: '/filter/ai-character-sheet-generator',
        title: 'AI Character Sheet Generator',
        title_key: 'ai_tools.illustration.ai_character_sheet_generator.title',
        recommended: false,
        derivative: true,
        footer: true,
      },
      {
        id: 9,
        path: '/filter/ai-doll-generator',
        title: 'AI Doll Generator',
        title_key: 'ai_tools.illustration.ai_doll_generator.title',
        recommended: false,
        derivative: true,
        footer: true,
      },
      {
        id: 10,
        path: '/filter/anime-ai-filter',
        title: 'Anime AI Filter',
        title_key: 'ai_tools.illustration.anime_ai_filter.title',
        recommended: false,
        derivative: true,
        footer: true,
      },
      {
        id: 11,
        path: '/filter/studio-ghibli-ai-generator',
        title: 'Studio Ghibli AI Generator',
        title_key: 'ai_tools.illustration.studio_ghibli_ai_generator.title',
        recommended: false,
        derivative: true,
        footer: true,
      },
      {
        id: 12,
        path: '/filter/studio-ghibli-filter',
        title: 'Studio Ghibli Filter',
        title_key: 'ai_tools.illustration.studio_ghibli_filter.title',
        recommended: false,
        derivative: true,
        footer: true,
      },
      {
        id: 19,
        path: '/ai-anime-generator/ai-waifu-generator',
        title: 'AI Waifu Generator',
        title_key: 'ai_tools.illustration.ai_waifu_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 20,
        path: '/ai-anime-generator/ai-naruto-generator',
        title: 'AI Naruto Generator',
        title_key: 'ai_tools.illustration.ai_naruto_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 21,
        path: '/ai-anime-generator/random-genshin-character-generator',
        title: 'Random Genshin Character Generator',
        title_key:
          'ai_tools.illustration.random_genshin_character_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 22,
        path: '/ai-anime-generator/dragon-ball-ai-generator',
        title: 'Dragon Ball AI Generator',
        title_key: 'ai_tools.illustration.dragon_ball_ai_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 23,
        path: '/ai-anime-generator/pokemon-ai-generator',
        title: 'Pokemon AI Generator',
        title_key: 'ai_tools.illustration.pokemon_ai_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 24,
        path: '/ai-anime-generator/ai-genshin-impact-generator',
        title: 'AI Genshin Impact Generator',
        title_key: 'ai_tools.illustration.ai_genshin_impact_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 25,
        path: '/ai-anime-generator/one-piece-ai-generator',
        title: 'One Piece AI Generator',
        title_key: 'ai_tools.illustration.one_piece_ai_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 26,
        path: '/ai-anime-generator/demon-slayer-ai-generator',
        title: 'Demon Slayer AI Generator',
        title_key: 'ai_tools.illustration.demon_slayer_ai_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 27,
        path: '/ai-anime-generator/attack-on-titan-ai-generator',
        title: 'Attack on Titan AI Generator',
        title_key: 'ai_tools.illustration.attack_on_titan_ai_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 28,
        path: '/ai-anime-generator/jujutsu-kaisen-ai-generator',
        title: 'Jujutsu Kaisen AI Generator',
        title_key: 'ai_tools.illustration.jujutsu_kaisen_ai_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 29,
        path: '/ai-anime-generator/my-hero-academia-ai-generator',
        title: 'My Hero Academia AI Generator',
        title_key: 'ai_tools.illustration.my_hero_academia_ai_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 30,
        path: '/ai-anime-generator/spy-x-family-ai-generator',
        title: 'Spy x Family AI Generator',
        title_key: 'ai_tools.illustration.spy_x_family_ai_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 31,
        path: '/ai-anime-generator/league-of-legends-ai-generator',
        title: 'League of Legends AI Generator',
        title_key: 'ai_tools.illustration.league_of_legends_ai_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 32,
        path: '/ai-anime-generator/ai-disney-pixar-generator',
        title: 'AI Disney Pixar Generator',
        title_key: 'ai_tools.illustration.ai_disney_pixar_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 33,
        path: '/ai-anime-generator/marvel-ai-generator',
        title: 'Marvel AI Generator',
        title_key: 'ai_tools.illustration.marvel_ai_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 34,
        path: '/ai-anime-generator/sonic-ai-generator',
        title: 'Sonic AI Generator',
        title_key: 'ai_tools.illustration.sonic_ai_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 35,
        path: '/ai-anime-generator/ai-spiderman-generator',
        title: 'AI Spiderman Generator',
        title_key: 'ai_tools.illustration.ai_spiderman_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 36,
        path: '/ai-anime-generator/anime-ai-generator',
        title: 'Anime AI Generator',
        title_key: 'ai_tools.illustration.anime_ai_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 37,
        path: '/ai-anime-generator/anime-art-ai-generator',
        title: 'Anime Art AI Generator',
        title_key: 'ai_tools.illustration.anime_art_ai_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 38,
        path: '/ai-anime-generator/studio-ghibli-style-ai-image-generator',
        title: 'Studio Ghibli Style AI Image Generator',
        title_key:
          'ai_tools.illustration.studio_ghibli_style_ai_image_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 39,
        path: '/ai-anime-generator/female-character-generator',
        title: 'Female Character Generator',
        title_key: 'ai_tools.illustration.female_character_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 40,
        path: '/ai-anime-generator/superhero-ai-generator',
        title: 'Superhero AI Generator',
        title_key: 'ai_tools.illustration.superhero_ai_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 41,
        path: '/ai-anime-generator/villain-maker',
        title: 'Villain Maker',
        title_key: 'ai_tools.illustration.villain_maker.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 42,
        path: '/ai-anime-generator/villain-generator',
        title: 'Villain Generator',
        title_key: 'ai_tools.illustration.villain_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 43,
        path: '/ai-anime-generator/fantasy-character-ai-generator',
        title: 'Fantasy Character AI Generator',
        title_key: 'ai_tools.illustration.fantasy_character_ai_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 44,
        path: '/ai-anime-generator/tim-burton-character-maker',
        title: 'Tim Burton Character Maker',
        title_key: 'ai_tools.illustration.tim_burton_character_maker.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 45,
        path: '/ai-anime-generator/dbz-ai-generator',
        title: 'DBZ AI Generator',
        title_key: 'ai_tools.illustration.dbz_ai_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 46,
        path: '/ai-anime-generator/ai-elf-generator',
        title: 'AI Elf Generator',
        title_key: 'ai_tools.illustration.ai_elf_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 47,
        path: '/ai-anime-generator/ai-princess-generator',
        title: 'AI Princess Generator',
        title_key: 'ai_tools.illustration.ai_princess_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 48,
        path: '/ai-anime-generator/ai-vampire-generator',
        title: 'AI Vampire Generator',
        title_key: 'ai_tools.illustration.ai_vampire_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 49,
        path: '/ai-anime-generator/ai-pirate-generator',
        title: 'AI Pirate Generator',
        title_key: 'ai_tools.illustration.ai_pirate_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 50,
        path: '/ai-anime-generator/ai-couple-generator',
        title: 'AI Couple Generator',
        title_key: 'ai_tools.illustration.ai_couple_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 51,
        path: '/ai-anime-generator/chibi-creator',
        title: 'Chibi Creator',
        title_key: 'ai_tools.illustration.chibi_creator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 52,
        path: '/ai-anime-generator/chibi-ai-generator',
        title: 'Chibi AI Generator',
        title_key: 'ai_tools.illustration.chibi_ai_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 53,
        path: '/ai-anime-generator/ai-character-generator-from-text',
        title: 'AI Character Generator From Text',
        title_key:
          'ai_tools.illustration.ai_character_generator_from_text.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 54,
        path: '/ai-anime-generator/video-game-character-generator',
        title: 'Video Game Character Generator',
        title_key: 'ai_tools.illustration.video_game_character_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 55,
        path: '/ai-anime-generator/ai-fantasy-art-generator',
        title: 'AI Fantasy Art Generator',
        title_key: 'ai_tools.illustration.ai_fantasy_art_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 56,
        path: '/ai-anime-generator/ai-world-generator',
        title: 'AI World Generator',
        title_key: 'ai_tools.illustration.ai_world_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 57,
        path: '/ai-anime-generator/ai-landscape-generator',
        title: 'AI Landscape Generator',
        title_key: 'ai_tools.illustration.ai_landscape_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 58,
        path: '/ai-anime-generator/ai-map-generator',
        title: 'AI Map Generator',
        title_key: 'ai_tools.illustration.ai_map_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 59,
        path: '/ai-anime-generator/ai-village-generator',
        title: 'AI Village Generator',
        title_key: 'ai_tools.illustration.ai_village_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 60,
        path: '/ai-anime-generator/ai-mansion-builder',
        title: 'AI Mansion Builder',
        title_key: 'ai_tools.illustration.ai_mansion_builder.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 61,
        path: '/ai-anime-generator/ai-castle-generator',
        title: 'AI Castle Generator',
        title_key: 'ai_tools.illustration.ai_castle_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 62,
        path: '/ai-anime-generator/fantasy-island-generator',
        title: 'Fantasy Island Generator',
        title_key: 'ai_tools.illustration.fantasy_island_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 63,
        path: '/ai-anime-generator/city-map-maker',
        title: 'City Map Maker',
        title_key: 'ai_tools.illustration.city_map_maker.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 64,
        path: '/ai-anime-generator/ai-skybox-generator',
        title: 'AI Skybox Generator',
        title_key: 'ai_tools.illustration.ai_skybox_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 65,
        path: '/ai-anime-generator/pop-art-creator',
        title: 'Pop Art Creator',
        title_key: 'ai_tools.illustration.pop_art_creator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 66,
        path: '/ai-anime-generator/pop-art-generator',
        title: 'Pop Art Generator',
        title_key: 'ai_tools.illustration.pop_art_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 67,
        path: '/ai-anime-generator/ai-watercolor-generator',
        title: 'AI Watercolor Generator',
        title_key: 'ai_tools.illustration.ai_watercolor_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 68,
        path: '/ai-anime-generator/abstract-ai-art-generator',
        title: 'Abstract AI Art Generator',
        title_key: 'ai_tools.illustration.abstract_ai_art_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 69,
        path: '/ai-anime-generator/ai-renaissance-painting-generator',
        title: 'AI Renaissance Painting Generator',
        title_key:
          'ai_tools.illustration.ai_renaissance_painting_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 70,
        path: '/ai-anime-generator/ai-oil-painter',
        title: 'AI Oil Painter',
        title_key: 'ai_tools.illustration.ai_oil_painter.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 71,
        path: '/ai-anime-generator/ai-dark-art-generator',
        title: 'AI Dark Art Generator',
        title_key: 'ai_tools.illustration.ai_dark_art_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 72,
        path: '/ai-anime-generator/ai-cartoon-generator-from-text-free',
        title: 'AI Cartoon Generator From Text Free',
        title_key:
          'ai_tools.illustration.ai_cartoon_generator_from_text_free.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 73,
        path: '/ai-anime-generator/ai-pencil-drawing-generator',
        title: 'AI Pencil Drawing Generator',
        title_key: 'ai_tools.illustration.ai_pencil_drawing_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 74,
        path: '/ai-anime-generator/ai-generated-coloring-pages',
        title: 'AI Generated Coloring Pages',
        title_key: 'ai_tools.illustration.ai_generated_coloring_pages.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 113,
        path: '/ai-anime-generator/ai-generated-collager',
        title: 'AI Generated Collager',
        title_key: 'ai_tools.illustration.ai_generated_collager.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 75,
        path: '/ai-anime-generator/ai-mural-generator',
        title: 'AI Mural Generator',
        title_key: 'ai_tools.illustration.ai_mural_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 76,
        path: '/ai-anime-generator/glitch-art-generator',
        title: 'Glitch Art Generator',
        title_key: 'ai_tools.illustration.glitch_art_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 77,
        path: '/ai-anime-generator/ai-vaporwave-generator',
        title: 'AI Vaporwave Generator',
        title_key: 'ai_tools.illustration.ai_vaporwave_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 78,
        path: '/ai-anime-generator/ai-funko-pops-creator',
        title: 'AI Funko Pops Creator',
        title_key: 'ai_tools.illustration.ai_funko_pops_creator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 79,
        path: '/ai-anime-generator/ai-weapon-generator',
        title: 'AI Weapon Generator',
        title_key: 'ai_tools.illustration.ai_weapon_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 80,
        path: '/ai-anime-generator/sword-generator',
        title: 'Sword Generator',
        title_key: 'ai_tools.illustration.sword_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 81,
        path: '/ai-anime-generator/ai-monster-generator',
        title: 'AI Monster Generator',
        title_key: 'ai_tools.illustration.ai_monster_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 82,
        path: '/ai-anime-generator/bratz-ai-generator',
        title: 'Bratz AI Generator',
        title_key: 'ai_tools.illustration.bratz_ai_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 83,
        path: '/ai-anime-generator/ai-creature-generator',
        title: 'AI Creature Generator',
        title_key: 'ai_tools.illustration.ai_creature_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 84,
        path: '/ai-anime-generator/ai-muppet-generator',
        title: 'AI Muppet Generator',
        title_key: 'ai_tools.illustration.ai_muppet_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 85,
        path: '/ai-anime-generator/ai-clothing-generator',
        title: 'AI Clothing Generator',
        title_key: 'ai_tools.illustration.ai_clothing_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 86,
        path: '/ai-anime-generator/ai-jersey-generator',
        title: 'AI Jersey Generator',
        title_key: 'ai_tools.illustration.ai_jersey_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 87,
        path: '/ai-anime-generator/ai-t-shirt-designer',
        title: 'AI T Shirt Designer',
        title_key: 'ai_tools.illustration.ai_t_shirt_designer.title',
        recommended: false,
        derivative: true,
      },
      // oc
      {
        id: 88,
        path: '/oc-maker/anime-character-generator',
        title: 'Anime Character Generator',
        title_key: 'ai_tools.illustration.anime_character_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 89,
        path: '/oc-maker/vampire-oc-maker',
        title: 'Vampire OC Maker',
        title_key: 'ai_tools.illustration.vampire_oc_maker.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 90,
        path: '/oc-maker/anime-oc-maker',
        title: 'Anime OC Maker',
        title_key: 'ai_tools.illustration.anime_oc_maker.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 91,
        path: '/oc-maker/perchance-ai-character-generator',
        title: 'Perchance AI Character Generator',
        title_key:
          'ai_tools.illustration.perchance_ai_character_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 92,
        path: '/oc-maker/genshin-oc-maker',
        title: 'Genshin OC Maker',
        title_key: 'ai_tools.illustration.genshin_oc_maker.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 93,
        path: '/oc-maker/genshin-oc-generator',
        title: 'Genshin OC Generator',
        title_key: 'ai_tools.illustration.genshin_oc_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 94,
        path: '/oc-maker/random-genshin-character-generator',
        title: 'Random Genshin Character Generator',
        title_key:
          'ai_tools.illustration.random_genshin_character_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 95,
        path: '/oc-maker/naruto-oc-maker',
        title: 'Naruto OC Maker',
        title_key: 'ai_tools.illustration.naruto_oc_maker.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 96,
        path: '/oc-maker/demon-slayer-oc-maker',
        title: 'Demon Slayer OC Maker',
        title_key: 'ai_tools.illustration.demon_slayer_oc_maker.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 97,
        path: '/oc-maker/pokemon-oc-maker',
        title: 'Pokemon OC Maker',
        title_key: 'ai_tools.illustration.pokemon_oc_maker.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 114,
        path: '/oc-maker/jjk-oc-maker',
        title: 'JJK OC Maker',
        title_key: 'ai_tools.illustration.jjk_oc_maker.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 98,
        path: '/oc-maker/jujutsu-kaisen-oc-maker',
        title: 'Jujutsu Kaisen OC Maker',
        title_key: 'ai_tools.illustration.jujutsu_kaisen_oc_maker.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 99,
        path: '/oc-maker/one-piece-oc-maker',
        title: 'One Piece OC Maker',
        title_key: 'ai_tools.illustration.one_piece_oc_maker.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 100,
        path: '/oc-maker/one-piece-characters-generator',
        title: 'One Piece Characters Generator',
        title_key: 'ai_tools.illustration.one_piece_characters_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 101,
        path: '/oc-maker/dragon-ball-oc-maker',
        title: 'Dragon Ball OC Maker',
        title_key: 'ai_tools.illustration.dragon_ball_oc_maker.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 102,
        path: '/oc-maker/attack-on-titan-oc-maker',
        title: 'Attack on Titan OC Maker',
        title_key: 'ai_tools.illustration.attack_on_titan_oc_maker.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 103,
        path: '/oc-maker/aot-oc-maker',
        title: 'AOT OC Maker',
        title_key: 'ai_tools.illustration.aot_oc_maker.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 104,
        path: '/oc-maker/my-hero-academia-oc-maker',
        title: 'My Hero Academia OC Maker',
        title_key: 'ai_tools.illustration.my_hero_academia_oc_maker.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 105,
        path: '/oc-maker/spy-x-family-oc-maker',
        title: 'Spy x Family OC Maker',
        title_key: 'ai_tools.illustration.spy_x_family_oc_maker.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 106,
        path: '/oc-maker/sonic-oc-maker',
        title: 'Sonic OC Maker',
        title_key: 'ai_tools.illustration.sonic_oc_maker.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 107,
        path: '/oc-maker/league-of-legends-oc-maker',
        title: 'League of Legends OC Maker',
        title_key: 'ai_tools.illustration.league_of_legends_oc_maker.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 108,
        path: '/oc-maker/nsfw-oc-maker',
        title: 'NSFW OC Maker',
        title_key: 'ai_tools.illustration.nsfw_oc_maker.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 109,
        path: '/oc-maker/nsfw-character-creator',
        title: 'NSFW Character Creator',
        title_key: 'ai_tools.illustration.nsfw_character_creator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 110,
        path: '/oc-maker/disney-oc-maker',
        title: 'Disney OC Maker',
        title_key: 'ai_tools.illustration.disney_oc_maker.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 111,
        path: '/oc-maker/marvel-oc-maker',
        title: 'Marvel OC Maker',
        title_key: 'ai_tools.illustration.marvel_oc_maker.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 112,
        path: '/oc-maker/ai-marvel-character-generator',
        title: 'AI Marvel Character Generator',
        title_key: 'ai_tools.illustration.ai_marvel_character_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 113,
        path: '/ai-anime-generator/ai-anime-porn-generator',
        title: 'AI Anime Porn Generator',
        title_key: 'ai_tools.illustration.ai_anime_porn_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 114,
        path: '/ai-anime-generator/furry-ai-generator',
        title: 'Furry AI Generator',
        title_key: 'ai_tools.illustration.furry_ai_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 115,
        path: '/ai-anime-generator/ai-art-generator-nsfw',
        title: 'AI Art Generator NSFW',
        title_key: 'ai_tools.illustration.ai_art_generator_nsfw.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 116,
        path: '/ai-anime-generator/ai-image-generator-to-create-a-webtoon-story',
        title: 'AI image generator to create a webtoon story',
        title_key:
          'ai_tools.illustration.ai_image_generator_to_create_a_webtoon_story.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 117,
        path: '/ai-anime-generator/anime-ai-generator-nsfw',
        title: 'Anime AI Generator NSFW',
        title_key: 'ai_tools.illustration.anime_ai_generator_nsfw.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 118,
        path: '/ai-anime-generator/nsfw-anime-ai-generator',
        title: 'NSFW Anime AI Generator',
        title_key: 'ai_tools.illustration.nsfw_anime_ai_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 119,
        path: '/ai-anime-generator/nsfw-ai-art-generator',
        title: 'NSFW AI Art Generator',
        title_key: 'ai_tools.illustration.nsfw_ai_art_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 120,
        path: '/ai-anime-generator/mlp-ai-generator',
        title: 'MLP AI Generator',
        title_key: 'ai_tools.illustration.mlp_ai_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 121,
        path: '/ai-anime-generator/my-little-pony-ai-generator',
        title: 'My Little Pony AI Generator',
        title_key: 'ai_tools.illustration.my_little_pony_ai_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 122,
        path: '/ai-anime-generator/anime-pfp-maker',
        title: 'Anime PFP Maker',
        title_key: 'ai_tools.illustration.anime_pfp_maker.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 123,
        path: '/ai-anime-generator/ai-game-asset-generator',
        title: 'AI Game Asset Generator',
        title_key: 'ai_tools.illustration.ai_game_asset_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 124,
        path: '/oc-maker/mlp-oc-maker',
        title: 'MLP OC Maker',
        title_key: 'ai_tools.illustration.mlp_oc_maker.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 125,
        path: '/oc-maker/my-little-pony-oc-maker',
        title: 'My Little Pony OC Maker',
        title_key: 'ai_tools.illustration.my_little_pony_oc_maker.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 126,
        path: '/oc-maker/video-game-character-generator',
        title: 'Video Game Character Generator',
        title_key: 'ai_tools.illustration.video_game_character_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 127,
        path: '/oc-maker/visual-novel-character-generator',
        title: 'Visual Novel Character Generator',
        title_key:
          'ai_tools.illustration.visual_novel_character_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 128,
        path: '/oc-maker/random-video-game-character-generator',
        title: 'Random Video Game Character Generator',
        title_key:
          'ai_tools.illustration.random_video_game_character_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 147,
        path: '/playground/lego-ai-filter',
        title: 'Lego AI Filter',
        title_key: 'ai_tools.illustration.lego_ai_filter.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 148,
        path: '/playground/photo-to-line-art',
        title: 'Photo to Line Art',
        title_key: 'ai_tools.illustration.photo_to_line_art.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 149,
        path: '/playground/photo-to-simpsons',
        title: 'Photo to Simpsons',
        title_key: 'ai_tools.illustration.photo_to_simpsons.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 150,
        path: '/playground/naruto-ai-filter',
        title: 'Naruto AI Filter',
        title_key: 'ai_tools.illustration.naruto_ai_filter.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 151,
        path: '/playground/watercolor-ai-filter',
        title: 'Watercolor AI Filter',
        title_key: 'ai_tools.illustration.watercolor_ai_filter.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 152,
        path: '/playground/cyberpunk-filter',
        title: 'Cyberpunk Filter',
        title_key: 'ai_tools.illustration.cyberpunk_filter.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 153,
        path: '/playground/emote-maker',
        title: 'Emote Maker',
        title_key: 'ai_tools.illustration.emote_maker.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 154,
        path: '/playground/ai-sprite-sheet-generator',
        title: 'AI Sprite Sheet Generator',
        title_key: 'ai_tools.illustration.ai_sprite_sheet_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 155,
        path: '/playground/ai-emoji-generator',
        title: 'AI Emoji Generator',
        title_key: 'ai_tools.illustration.ai_emoji_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 156,
        path: '/playground/ai-emoticon-generator',
        title: 'AI Emoticon Generator',
        title_key: 'ai_tools.illustration.ai_emoticon_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 157,
        path: '/playground/anime-sticker-maker',
        title: 'Anime Sticker Maker',
        title_key: 'ai_tools.illustration.anime_sticker_maker.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 158,
        path: '/playground/ai-sticker-generator',
        title: 'AI Sticker Generator',
        title_key: 'ai_tools.illustration.ai_sticker_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 159,
        path: '/playground/ai-sprite-generator',
        title: 'AI Sprite Generator',
        title_key: 'ai_tools.illustration.ai_sprite_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 160,
        path: '/playground/ai-character-sheet-generator',
        title: 'AI Character Sheet Generator',
        title_key: 'ai_tools.illustration.ai_character_sheet_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 161,
        path: '/playground/ai-plush-generator',
        title: 'AI Plush Generator',
        title_key: 'ai_tools.illustration.ai_plush_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 162,
        path: '/playground/ai-action-figure-generator',
        title: 'AI Action Figure Generator',
        title_key: 'ai_tools.illustration.ai_action_figure_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 163,
        path: '/playground/ai-badge-generator',
        title: 'AI Badge Generator',
        title_key: 'ai_tools.illustration.ai_badge_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 164,
        path: '/playground/ai-clay-filter',
        title: 'AI Clay Filter',
        title_key: 'ai_tools.illustration.ai_clay_filter.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 165,
        path: '/playground/photo-to-minecraft',
        title: 'Photo to Minecraft',
        title_key: 'ai_tools.illustration.photo_to_minecraft.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 166,
        path: '/playground/ai-style-transfer',
        title: 'AI Style Transfer',
        title_key: 'ai_tools.illustration.ai_style_transfer.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 167,
        path: '/playground/photo-to-anime',
        title: 'Photo to Anime',
        title_key: 'ai_tools.illustration.photo_to_anime.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 168,
        path: '/playground/photo-to-pixel-art',
        title: 'Photo to Pixel Art',
        title_key: 'ai_tools.illustration.photo_to_pixel_art.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 169,
        path: '/playground/ai-cosplay-generator',
        title: 'AI Cosplay Generator',
        title_key: 'ai_tools.illustration.ai_cosplay_generator.title',
        recommended: false,
        derivative: true,
      },
    ],
  },
  {
    title: 'Create animation videos',
    title_key: 'ai_tools.animation.title',
    category: 'animation',
    entries: [
      {
        id: 1,
        path: '/image-animation-generator',
        image_url: undefined,
        video_url:
          'https://dihulvhqvmoxyhkxovko.supabase.co/storage/v1/object/public/husbando-land/public/husbando-land/app_videos/output2.mp4',
        title: 'Image to Animation',
        title_key: 'ai_tools.animation.image_to_animation.title',
        content: 'Turn your images into smooth animation videos with AI.',
        content_key: 'ai_tools.animation.image_to_animation.content',
        recommended: true,
      },
      {
        id: 7,
        path: '/ai-talking-head',
        image_url: undefined,
        video_url:
          'https://dihulvhqvmoxyhkxovko.supabase.co/storage/v1/object/public/husbando-land/public/husbando-land/app_videos/ishibashi-muted.webm',
        title: 'AI Talking Head',
        title_key: 'ai_tools.animation.ai_talking_head.title',
        content: 'Generate animated talking characters from still images.',
        content_key: 'ai_tools.animation.ai_talking_head.content',
        recommended: false,
      },
      {
        id: 18,
        path: '/video-to-video',
        title: 'Video to Video',
        title_key: 'ai_tools.animation.video_to_video.title',
        recommended: true,
        video_url:
          'https://dihulvhqvmoxyhkxovko.supabase.co/storage/v1/object/public/husbando-land/public/husbando-land/app_videos/cover-v3.webm',
        content: 'Convert your videos to different styles with AI.',
        content_key: 'ai_tools.animation.video_to_video.content',
      },
      {
        id: 2,
        path: '/inbetween',
        image_url: undefined,
        video_url:
          'https://dihulvhqvmoxyhkxovko.supabase.co/storage/v1/object/public/husbando-land/public/husbando-land/app_videos/9c49fd9c-346e-46b7-8104-428f6c2d8dd2.webm',
        title: 'In-betweening',
        title_key: 'ai_tools.animation.inbetweening.title',
        content:
          'Generate inbetween frames between keyframes to create an animation.',
        content_key: 'ai_tools.animation.inbetweening.content',
        recommended: false,
      },
      {
        id: 6,
        path: '/layer_splitter',
        image_url: '/images/layer_splitter.png',
        video_url: undefined,
        title: 'Layer Splitter',
        title_key: 'ai_tools.animation.layer_splitter.title',
        content:
          'Split character image into separate components for animation.',
        content_key: 'ai_tools.animation.layer_splitter.content',
        recommended: false,
      },
      {
        id: 3,
        path: '/video_upscaling',
        image_url: '/images/thumbnail/video-upscaling.jpeg',
        video_url: undefined,
        title: 'Video Upscaling',
        title_key: 'ai_tools.animation.video_upscaling.title',
        content:
          'Increase the resolution of your anime videos for better quality.',
        content_key: 'ai_tools.animation.video_upscaling.content',
        recommended: false,
      },
      {
        id: 5,
        path: '/video_interpolation',
        image_url: undefined,
        video_url:
          'https://dihulvhqvmoxyhkxovko.supabase.co/storage/v1/object/public/husbando-land/public/husbando-land/app_videos/679983bc-952d-4509-bf03-d8b5bc1f91bc.webm',
        title: 'Frame Interpolation',
        title_key: 'ai_tools.animation.video_interpolation.title',
        content:
          'Make videos smoother by inserting extra frames to increase the frame rate.',
        content_key: 'ai_tools.animation.video_interpolation.content',
        recommended: false,
      },
      {
        id: 16,
        path: '/video/AniSora',
        title: 'AniSora AI Video Generator',
        title_key: 'ai_tools.illustration.anisora_video_generator.title',
        recommended: false,
        derivative: true,
        model_name: 'AniSora',
        footer: true,
      },
      {
        id: 17,
        path: '/video/veo-3',
        title: 'Veo3 AI Video Generator',
        title_key: 'ai_tools.illustration.veo3_video_generator.title',
        recommended: false,
        derivative: true,
        model_name: 'Veo3',
        footer: true,
      },
      {
        id: 88,
        path: '/video/midjourney',
        title: 'Midjourney AI Video Generator',
        title_key: 'ai_tools.illustration.midjourney_video_generator.title',
        recommended: false,
        derivative: true,
        model_name: 'Midjourney',
        footer: true,
      },
      {
        id: 13,
        path: '/video/framepack',
        title: 'FramePack AI Video Generator',
        title_key: 'ai_tools.illustration.frame_pack_ai_video_generator.title',
        recommended: false,
        derivative: true,
        model_name: 'FramePack',
        footer: true,
      },
      {
        id: 14,
        path: '/video/vidu-q1',
        title: 'Vidu Q1 Video Generator',
        title_key: 'ai_tools.illustration.vidu_ai_video_generator.title',
        recommended: false,
        derivative: true,
        model_name: 'Vidu Q1',
        footer: true,
      },
      {
        id: 15,
        path: '/video/magi-1',
        title: 'Magi-1 Video Generator',
        title_key: 'ai_tools.illustration.magi_1_video_generator.title',
        recommended: false,
        derivative: true,
        model_name: 'Magi-1',
        footer: true,
      },
      {
        id: 129,
        path: '/video/marey',
        title: 'Marey AI Video Generator',
        title_key: 'ai_tools.illustration.marey_ai_video_generator.title',
        recommended: false,
        derivative: true,
        model_name: 'Marey',
        footer: true,
      },
      {
        id: 130,
        path: '/video/moonvalley',
        title: 'Moonvalley AI Video Generator',
        title_key: 'ai_tools.illustration.moonvalley_ai_video_generator.title',
        recommended: false,
        derivative: true,
      },
    ],
  },
  {
    title: 'Generate comics, manga and manhwa',
    title_key: 'ai_tools.comic_generation.title',
    category: 'comic',
    entries: [
      {
        id: -1,
        path: '/create',
        image_url: '/images/canva.webp',
        video_url: undefined,
        title: 'Create Comics Comics on Canvas',
        title_key: 'ai_tools.comic_generation.create_on_canvas.title',
        content:
          'Create your comics from scratch on a blank canvas with AI image generation.',
        content_key: 'ai_tools.comic_generation.create_on_canvas.content',
        recommended: true,
      },
      {
        id: 0,
        path: '/ai-comic-generator',
        image_url: '/images/generate_comic.webp',
        video_url: undefined,
        title: 'AI Comic Generator',
        title_key: 'ai_tools.comic_generation.ai_comic_generator.title',
        content:
          'Type a story idea and generate a comic in seconds with our comic maker AI.',
        content_key: 'ai_tools.comic_generation.ai_comic_generator.content',
        recommended: false,
      },
      {
        id: 129,
        path: '/ai-comic-generator/ai-comic-factory',
        title: 'AI Comic Factory',
        title_key: 'ai_tools.comic_generation.ai_comic_factory.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 130,
        path: '/ai-comic-generator/ai-manga-maker',
        title: 'AI Manga Maker',
        title_key: 'ai_tools.comic_generation.ai_manga_maker.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 131,
        path: '/ai-comic-generator/ai-manhwa-generator',
        title: 'AI Manhwa Generator',
        title_key: 'ai_tools.comic_generation.ai_manhwa_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 132,
        path: '/ai-comic-generator/ai-manga-generator',
        title: 'AI Manga Generator',
        title_key: 'ai_tools.comic_generation.ai_manga_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 133,
        path: '/ai-comic-generator/ai-comic-strip-generator',
        title: 'AI Comic Strip Generator',
        title_key: 'ai_tools.comic_generation.ai_comic_strip_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 134,
        path: '/ai-comic-generator/ai-comic-book-generator',
        title: 'AI Comic Book Generator',
        title_key: 'ai_tools.comic_generation.ai_comic_book_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 135,
        path: '/ai-comic-generator/ai-comic-maker',
        title: 'AI Comic Maker',
        title_key: 'ai_tools.comic_generation.ai_comic_maker.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 136,
        path: '/ai-comic-generator/ai-storyboard-generator',
        title: 'AI Storyboard Generator',
        title_key: 'ai_tools.comic_generation.ai_storyboard_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 137,
        path: '/ai-comic-generator/free-ai-comic-strip-maker',
        title: 'Free AI Comic Strip Maker',
        title_key: 'ai_tools.comic_generation.free_ai_comic_strip_maker.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 138,
        path: '/ai-comic-generator/nsfw-ai-comic-generator',
        title: 'NSFW AI Comic Generator',
        title_key: 'ai_tools.comic_generation.nsfw_ai_comic_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 139,
        path: '/ai-comic-generator/ai-marvel-comic-generator',
        title: 'AI Marvel Comic Generator',
        title_key: 'ai_tools.comic_generation.ai_marvel_comic_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 140,
        path: '/ai-comic-generator/batman-comic-ai-generator',
        title: 'Batman Comic AI Generator',
        title_key: 'ai_tools.comic_generation.batman_comic_ai_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 141,
        path: '/ai-comic-generator/dc-comic-ai-generator',
        title: 'DC Comic AI Generator',
        title_key: 'ai_tools.comic_generation.dc_comic_ai_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 142,
        path: '/ai-comic-generator/ai-porn-comic-generator',
        title: 'AI Porn Comic Generator',
        title_key: 'ai_tools.comic_generation.ai_porn_comic_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 143,
        path: '/ai-comic-generator/ai-erotic-comic-generator',
        title: 'AI Erotic Comic Generator',
        title_key: 'ai_tools.comic_generation.ai_erotic_comic_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 144,
        path: '/ai-comic-generator/boomer-comic-ai-generator',
        title: 'Boomer Comic AI Generator',
        title_key: 'ai_tools.comic_generation.boomer_comic_ai_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 145,
        path: '/ai-comic-generator/anime-manga-ai-generator',
        title: 'Anime Manga AI Generator',
        title_key: 'ai_tools.comic_generation.anime_manga_ai_generator.title',
        recommended: false,
        derivative: true,
      },
      {
        id: 146,
        path: '/ai-comic-generator/ai-webtoon-generator',
        title: 'AI Webtoon Generator',
        title_key: 'ai_tools.comic_generation.ai_webtoon_generator.title',
        recommended: false,
        derivative: true,
      },
    ],
  },
  // {
  //   title: "null",
  //   title_key: "null",
  //   category: "null",
  //   entries: [
  //     {
  //       id: 1,
  //       path: "/home",
  //       image_url: "/images/plus.png",
  //       video_url: undefined,
  //       title: "AND MORE",
  // title_key: "ai_tools.more.title",
  //       content: "Explore our complete suite of AI tools for Comic, Illustration and Animation.",
  // content_key: "ai_tools.more.content",
  //       recommended: true,
  //     },
  //   ]
  // }
];

// Navigation configuration
export interface NavItem {
  title: string;
  title_key: string;
  href: string;
}

export const getNavItems = (t: (key: string) => string): NavItem[] => [
  {
    title: t('nav.professional_artists'),
    title_key: 'nav.professional',
    href: '/#professional-artists',
  },
  {
    title: t('nav.ai_anime_generator'),
    title_key: 'nav.ai_anime_generator',
    href: '/ai-anime-generator',
  },
  {
    title: t('nav.video_to_video'),
    title_key: 'nav.video_to_video',
    href: '/video-to-video',
  },
  {
    title: t('nav.pricing'),
    title_key: 'nav.pricing',
    href: '/pricing',
  },
  {
    title: t('nav.blog'),
    title_key: 'nav.blog',
    href: '/blog',
  },
];

// Helper functions for filtering tools
export const getFooterTools = (): ToolItem[] => {
  const allTools = aiTools.flatMap(toolSet => toolSet.entries);
  // Return non-derivative tools or tools with footer: true
  return allTools.filter(tool => !tool.derivative || tool.footer === true);
};

// Get footer tools for a specific category
export const getFooterToolsByCategory = (category: string): ToolItem[] => {
  const targetToolSet = aiTools.find(toolSet => toolSet.category === category);
  if (!targetToolSet) {
    return [];
  }

  return targetToolSet.entries.filter(
    tool => !tool.derivative || tool.footer === true,
  );
};
