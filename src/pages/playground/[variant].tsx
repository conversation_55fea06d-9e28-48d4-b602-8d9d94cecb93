import { GetStaticProps, GetStaticPaths } from 'next'
import Head from 'next/head'
import { TemplateWrapper } from '../../Components/ToolsPage/TemplateWrapper'
import { useEffect } from 'react'
import { useAtomValue } from 'jotai'
import { profileAtom } from '../../state'
import { trackToolPageView } from '../../utilities/analytics'
import {
  loadToolData,
  loadVariantData,
} from '../../lib/variant-loader'
import { useRouter } from 'next/router'
import { ToolsPhotoToAnimePage } from '../../Components/ToolsPage'
import {
  loadServerTranslation,
  TranslationData,
} from '../../lib/server-translations'
import { AnimeStyle } from '../../../api/tools/_constants'
import { Header } from '../../Components/Header'
import { Sidebar } from '@/components/Sidebar'
import { SiteFooter } from '@/components/site-footer'
import { Breadcrumb } from '../../Components/common/Breadcrumb';
import mixpanel from 'mixpanel-browser'

interface VariantPageProps {
  variantContent: any
  variantKey: string
  translations: TranslationData
  defaultStyle?: AnimeStyle
}

// 映射variant关键词到对应的style
const getDefaultStyleForVariant = (variantKey: string): AnimeStyle | undefined => {
  const styleMapping: Record<string, AnimeStyle> = {
    'photo-to-minecraft': AnimeStyle.PIXEL_ART,
    'emote-maker': AnimeStyle.CHIBI,
    'ai-emoji-generator': AnimeStyle.CHIBI,
    'ai-emoticon-generator': AnimeStyle.CHIBI,
    'anime-sticker-maker': AnimeStyle.CHIBI,
    'ai-sticker-generator': AnimeStyle.CHIBI,
    'ai-sprite-sheet-generator': AnimeStyle.SPRITE_SHEET,
    'ai-sprite-generator': AnimeStyle.SPRITE_SHEET,
    'ai-character-sheet-generator': AnimeStyle.CHARACTER_SHEET,
    'studio-ghibli-filter': AnimeStyle.GHIBLI_ANIME,
    'ai-action-figure-generator': AnimeStyle.ACTION_FIGURE,
    'ai-plush-generator': AnimeStyle.PLUSHIE,
    'ai-badge-generator': AnimeStyle.BADGE,
    'ai-clay-filter': AnimeStyle.CLAY,
    'photo-to-pixel-art': AnimeStyle.PIXEL_ART,
    'lego-ai-filter': AnimeStyle.LEGO,
    'photo-to-line-art': AnimeStyle.LINE_ART,
    'photo-to-simpsons': AnimeStyle.SIMPSONS,
    'naruto-ai-filter': AnimeStyle.NARUTO,
    'watercolor-ai-filter': AnimeStyle.WATERCOLOR,
    'cyberpunk-filter': AnimeStyle.CYBERPUNK,
    'ai-cosplay-generator': AnimeStyle.COSPLAY,
  };

  return styleMapping[variantKey]
}

export default function VariantPage({
  variantContent,
  variantKey,
  translations,
  defaultStyle,
}: VariantPageProps) {
  const router = useRouter()
  const profile = useAtomValue(profileAtom)

  // Track variant page view
  useEffect(() => {
    if (router.isReady) {
      try {
        trackToolPageView(`playground-${variantKey}`, profile?.id)
      } catch (error) {
        console.error('Error tracking variant page view:', error)
      }
    }
  }, [router.isReady, variantKey, profile?.id])

  // Add mixpanel tracking
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (typeof window !== 'undefined') {
        try {
          mixpanel.track('visit.page.tools.playground.variant', {
            variant: variantKey,
          });
          console.log('tracking mixpanel variant:', variantKey);
        } catch (error) {
          console.error('Mixpanel tracking failed:', error);
        }
      }
    }, 800);
    return () => clearTimeout(timeoutId);
  }, [variantKey]);

  if (!variantContent) {
    return <div>Variant not found</div>
  }

  return (
    <main className='flex flex-col h-screen caffelabs text-foreground bg-background'>
      <Head>
        <title>{variantContent.seo.title}</title>
        <meta name='description' content={variantContent.seo.description} />
        <meta name='keywords' content={variantContent.seo.keywords} />
        <meta property='og:type' content='website' />
        <meta property='og:title' content={variantContent.seo.title} />
        <meta
          property='og:description'
          content={variantContent.seo.description}
        />
        <meta
          property='og:url'
          content={`https://komiko.app/playground/${variantKey}`}
        />
        <meta
          property='og:image'
          content={
            variantContent.content.examples?.[0]?.image ||
            '/images/examples/playground/cover.webp'
          }
        />
        <meta name='twitter:card' content='summary_large_image' />
        <meta name='twitter:title' content={variantContent.seo.title} />
        <meta
          name='twitter:description'
          content={variantContent.seo.description}
        />
        <meta
          name='twitter:image'
          content={
            variantContent.content.examples?.[0]?.image ||
            '/images/examples/playground/cover.webp'
          }
        />
      </Head>

      <Header autoOpenLogin={false} />
      <div className='flex'>
        <Sidebar />
        <div className='p-2 pt-24 md:pl-56 lg:pl-[240px] w-full h-full'>
          <Breadcrumb className='w-full mb-2' />
          <TemplateWrapper
            variantData={variantContent}
            isVariant={true}
            toolName='playground'
            variantName={variantKey}>
            <ToolsPhotoToAnimePage
              translations={translations}
              defaultStyle={defaultStyle}
              variantContent={variantContent}
              variantKey={variantKey}
            />
          </TemplateWrapper>
        </div>
      </div>
      <SiteFooter className='ml-5 border-border md:pl-56 lg:pl-[240px]' />
    </main>
  );
}

export const getStaticPaths: GetStaticPaths = async ({ locales }) => {
  const paths: { params: { variant: string }; locale?: string }[] = []

  try {
    // 使用新的variant-loader加载playground数据
    const toolData = loadToolData('playground')

    if (toolData && toolData.variants) {
      Object.keys(toolData.variants).forEach(variantKey => {
        // 为每种语言生成路径
        if (locales) {
          locales.forEach(locale => {
            paths.push({
              params: { variant: variantKey },
              locale,
            })
          })
        } else {
          // 如果没有locales，使用默认路径
          paths.push({ params: { variant: variantKey } })
        }
      })
    }
  } catch (error) {
    console.error('Error loading variant paths:', error)
  }

  return {
    paths,
    fallback: false,
  }
}

export const getStaticProps: GetStaticProps = async ({ params, locale }) => {
  const variantKey = params?.variant as string

  if (!variantKey) {
    return { notFound: true }
  }

  try {
    const variantContent = loadVariantData('playground', variantKey, locale)
    const translations = await loadServerTranslation(locale)
    const defaultStyle = getDefaultStyleForVariant(variantKey)

    if (!variantContent) {
      return { notFound: true }
    }

    return {
      props: {
        variantContent,
        variantKey,
        translations,
        ...(defaultStyle && { defaultStyle }),
      },
    }
  } catch (error) {
    console.error('Error loading variant data:', error)
    return { notFound: true }
  }
}
