// [#target:tools]
import React from 'react';
import { Header } from '../Components/Header'
import { ToolsPhotoToAnimePage } from '../Components/ToolsPage';
import Head from 'next/head';
import mixpanel from 'mixpanel-browser';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Sidebar } from '@/components/Sidebar';
import { SiteFooter } from '@/components/site-footer';

export default function Tools() {
  const { t } = useTranslation('playground');

  // 创建 playground 特定的变体内容结构
  const playgroundContent = {
    seo: {
      title: t('meta.title'),
      description: t('meta.description'),
      keywords: t('meta.keywords'),
    },
    content: {
      header: {
        title: t('hero.title'),
        subtitle: t('hero.description'),
      },
      sections: {
        howToUse: {
          title: t('howItWorks.title'),
          steps: [
            {
              title: t('howItWorks.step1.title'),
              content: t('howItWorks.step1.content'),
            },
            {
              title: t('howItWorks.step2.title'),
              content: t('howItWorks.step2.content'),
            },
            {
              title: t('howItWorks.step3.title'),
              content: t('howItWorks.step3.content'),
            },
            {
              title: t('howItWorks.step4.title'),
              content: t('howItWorks.step4.content'),
            },
          ],
        },
        examples: {
          title: t('examples.title'),
        },
        whyUse: {
          title: t('benefits.title'),
          features: [
            {
              title: t('benefits.feature1.title'),
              content: t('benefits.feature1.content'),
            },
            {
              title: t('benefits.feature2.title'),
              content: t('benefits.feature2.content'),
            },
            {
              title: t('benefits.feature3.title'),
              content: t('benefits.feature3.content'),
            },
            {
              title: t('benefits.feature4.title'),
              content: t('benefits.feature4.content'),
            },
            {
              title: t('benefits.feature5.title'),
              content: t('benefits.feature5.content'),
            },
            {
              title: t('benefits.feature6.title'),
              content: t('benefits.feature6.content'),
            },
          ],
        },
        faq: {
          title: t('faqSection.title'),
          description: t('faqSection.description'),
        },
      },
      faq: [
        {
          question: t('faq.question1'),
          answer: t('faq.answer1'),
        },
        {
          question: t('faq.question2'),
          answer: t('faq.answer2'),
        },
        {
          question: t('faq.question3'),
          answer: t('faq.answer3'),
        },
        {
          question: t('faq.question4'),
          answer: t('faq.answer4'),
        },
        {
          question: t('faq.question5'),
          answer: t('faq.answer5'),
        },
      ],
    },
  };

  // add mixpanel tracking
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (typeof window !== 'undefined') {
        try {
          mixpanel.track('visit.page.tools.playground', {});
          console.log('tracking mixpanel');
        } catch (error) {
          console.error('Mixpanel tracking failed:', error);
        }
      }
    }, 800);
    return () => clearTimeout(timeoutId);
  }, []);

  return (
    <main className='flex flex-col h-screen caffelabs text-foreground bg-background'>
      <Head>
        <title>{t('meta.title')}</title>
        <meta property='og:type' content='website' />
        <meta property='og:title' content={t('meta.title')} />
        <meta property='og:description' content={t('meta.description')} />
        <meta property='og:url' content='https://komiko.app/playground' />
        <meta
          property='og:image'
          content='/images/examples/photo-to-anime/cover.webp'
        />
        <meta name='twitter:card' content='summary_large_image' />
        <meta name='twitter:title' content={t('meta.title')} />
        <meta name='twitter:description' content={t('meta.description')} />
        <meta
          name='twitter:image'
          content='/images/examples/photo-to-anime/cover.webp'
        />
        <meta name='description' content={t('meta.description')} />
        <meta name='keywords' content={t('meta.keywords')} />
      </Head>
      <Header autoOpenLogin={false} />
      <div className='flex'>
        <Sidebar />
        <div className='p-2 pt-24 lg:pl-[240px] w-full h-full'>
          <ToolsPhotoToAnimePage
            variantContent={playgroundContent}
            variantKey='playground'
          />
        </div>
      </div>
      <SiteFooter className='ml-5 border-border md:pl-56 lg:pl-[240px]' />
    </main>
  );
}
