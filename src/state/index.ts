import { atom } from 'jotai';
// import { type NodeConfig } from "konva/lib/Node"; // 移除静态导入避免被打包到主bundle
import { IDiffItem } from '../Components/InfCanva/types';
import { SubscriptionStatus } from '../../api/payment/_constant';
// 我们要保存的json对象，中的基本元素

// 使用泛型替代NodeConfig以避免静态导入Konva
interface BaseNodeConfig {
  x?: number;
  y?: number;
  width?: number;
  height?: number;
  scaleX?: number;
  scaleY?: number;
  rotation?: number;
  visible?: boolean;
  // 添加其他可能需要的通用属性
  [key: string]: any;
}

interface ExtendedNodeConfig extends BaseNodeConfig {
  width?: number;
  height?: number;
}
export interface CNode {
  cType?: CNodeType;
  attrs: Partial<ExtendedNodeConfig>;
  imageUrl?: string; // comic_image only
  children?: Array<CNode>;
  childSorting?: Array<string>;
  visible?: boolean; // 是否显示，在删除，undo/redo时候使用
  asyncImageFunc?: () => Promise<any>; // 移步获取图片的方法，因为prompt处理事件太长了，需要先加载占位图，此方法执行完成后更新CNode
}
// node时currentSelectedShape.node，它是指的一个konva shape，画布上的节点
// CNode是保存appState里定义的基本节点
export interface HistoryItem {
  versionId: number | string;
  detail: {
    appState: Array<CNode>;
    comicPageSorting: Array<string>;
  };
}

export enum CNodeType {
  COMIC_PAGE = 'comic_page',
  COMIC_IMAGE = 'comic_image',
  COMIC_TEXT = 'comic_text',
  COMIC_BUBBLE = 'comic_bubble',
  COMIC_MARK_AREA = 'comic_mark_area',
}
export const appStateAtom = atom<Array<CNode>>([]);
export const useAppStateAtom = atom<boolean>(false);
export const historyAtom = atom<Array<HistoryItem>>([]);
export const comicPageSortingAtom = atom<Array<string>>([]);

export const prevStepsAtom = atom<Array<IDiffItem>>([]);
export const redoStepsAtom = atom<Array<IDiffItem>>([]);
export const selectedImageAtom = atom<string>('');
export const characterImagePromptAtom = atom<string>('');
export const characterGenderAtom = atom<string>('male');
export const exportImageAtom = atom<string>('');
export const postGeneratedImageUrlsAtom = atom<string[]>([]);
export const postTitleAtom = atom<string>('');
export const postContentAtom = atom<string>('');
export const bgWidthAtom = atom<number>(1024);

export const authAtom = atom<boolean>(false);
export const isMobileAtom = atom<boolean>(false);
// 检查是否页面已经检查过auth
export const checkedAuthAtom = atom<boolean>(false);

interface ModalAction {
  onOpen: () => void;
  onClose: () => void;
}
export interface Modals {
  pricing?: ModalAction;
}

export const modalsAtom = atom<Modals>({});
export const modalsPayloadAtom = atom<any>(null);

export interface Profile {
  id: string;
  authUserId: string;
  user_uniqid: string;
  created_at: string;
  user_name: string;
  image: string;
  user_desc: string;
  num_followers: number;
  num_following: number;
  credit: number;
  free_credit: number;
  date_checkin: string;
  date_post: string;
  date_like: string;
  tour_completed: boolean;
  invite_code: string;
  plan: string;
  subscription_status: SubscriptionStatus;
  email: string;
  is_cpp: boolean;
  plan_codes: string[];
}

export const profileAtom = atom<Profile>({
  authUserId: '',
  user_uniqid: '',
  created_at: '',
  user_name: '--',
  image: '',
  user_desc: '',
  num_followers: 0,
  num_following: 0,
  credit: 0,
  free_credit: 0,
  subscription_status: SubscriptionStatus.NONE,
  date_checkin: '2020-01-01',
  date_post: '2020-01-01',
  date_like: '2020-01-01',
  tour_completed: false,
  invite_code: '',
  plan: 'Free',
  id: '',
  is_cpp: false,
  email: '',
  plan_codes: [],
});

export const loginModalAtom = atom<{
  onOpen: () => void;
}>({
  onOpen: () => {},
});

export interface User {
  id: number;
  authUserId: string;
  user_name: string;
  user_uniqid: string;
  image: string;
  followed: boolean;
  post_likes: number;
}

export const userListAtom = atom<User[]>([]);

export interface Character {
  id: number;
  authUserId: string;
  character_uniqid: string;
  created_at: string;
  character_name: string;
  character_description: string;
  file_uniqid: string;
  age: string;
  profession: string;
  personality: string;
  interests: string;
  intro: string;
  character_pfp: string;
  rizz: number;
  num_adopt: number;
  num_gen: number;
}

export const characterListAtom = atom<Character[]>([]);

export interface Comment {
  postId: number;
  content: string;
  votes: number;
  user_name: string;
  image: string;
  created_at: string;
}
export interface Post {
  id: number;
  uniqid: string;
  user_uniqid: string;
  authUserId: string;
  created_at: string;
  views: number;
  rizz: number;
  title: string;
  content: string;
  media: string[];
  votes: number;
  user_name: string;
  image: string;
  user_plan?: string;
  liked: boolean;
  comments: Comment[];
  followed?: boolean;
  generations: any[];
  media_type?: 'image' | 'video' | 'text';
  post_tags: {
    id: number;
    name: string;
  }[];
}

export const postListAtom = atom<Post[]>([]);
export const pageAtom = atom<number>(1);
export const tags = [
  {
    label: 'Trending',
    label_key: 'tags.trending',
    value: 'trending',
  },
  {
    label: 'Newest',
    label_key: 'tags.newest',
    value: 'newest',
  },
  // {
  //   label: 'Most Likes',
  //   label_key: 'tags.most_likes',
  //   value: 'most_likes',
  // },
  {
    label: 'Following',
    label_key: 'tags.following',
    value: 'following',
  },
];
export const feedTagAtom = atom<string>(tags[0].label);
