export interface StyleTemplate {
  id: string
  name: string
  image: string
}

export interface StyleTemplateCategory {
  category: string
  icon: string
  templates: StyleTemplate[]
}

// Function to get style templates with translations
export const getStyleTemplateCategories = (t: any): StyleTemplateCategory[] => [
  {
    category: 'Style Transfer',
    icon: '🎨',
    templates: [
      {
        id: 'anime',
        name: t('ui.styleTemplates.anime'),
        image: '/images/styles/anime.webp',
      },
      {
        id: 'ghibli-anime',
        name: t('ui.styleTemplates.ghibliAnime'),
        image: '/images/styles/studio_ghibli_anime.webp',
      },
      {
        id: 'comic',
        name: t('ui.styleTemplates.comic'),
        image: '/images/styles/comic.webp',
      },
      {
        id: 'pixar',
        name: t('ui.styleTemplates.pixar'),
        image: '/images/styles/pixar.webp',
      },
      {
        id: 'korean-manhwa',
        name: t('ui.styleTemplates.koreanManhwa'),
        image: '/images/styles/korean_manhwa.webp',
      },
      {
        id: 'cartoon',
        name: t('ui.styleTemplates.cartoon'),
        image: '/images/styles/cartoon.webp',
      },
      {
        id: 'manga',
        name: t('ui.styleTemplates.manga'),
        image: '/images/styles/manga.webp',
      },
      {
        id: 'clay',
        name: t('ui.styleTemplates.clay'),
        image: '/images/styles/clay.webp',
      },
      {
        id: 'vaporwave',
        name: t('ui.styleTemplates.vaporwave'),
        image: '/images/styles/vaporwave.webp',
      },
    ],
  },
  {
    category: 'Change Environment',
    icon: '🗺️',
    templates: [
      {
        id: 'kpop-idol',
        name: t('ui.styleTemplates.kpopIdol'),
        image: '/images/styles/kpop_idol.webp',
      },
      {
        id: 'cyberpunk',
        name: t('ui.styleTemplates.cyberpunk'),
        image: '/images/styles/cyberpunk.webp',
      },
      {
        id: 'cloud',
        name: t('ui.styleTemplates.cloud'),
        image: '/images/styles/cloud.webp',
      },
      {
        id: 'mars',
        name: t('ui.styleTemplates.mars'),
        image: '/images/styles/mars.webp',
      },
      {
        id: 'outer-space',
        name: t('ui.styleTemplates.outerSpace'),
        image: '/images/styles/outer_space.webp',
      },
      {
        id: 'apocalypse',
        name: t('ui.styleTemplates.apocalypse'),
        image: '/images/styles/apocalypse.webp',
      },
    ],
  },
  {
    category: 'Cosplay',
    icon: '👘',
    templates: [
      {
        id: 'superhero',
        name: t('ui.styleTemplates.superhero'),
        image: '/images/styles/superhero.webp',
      },
      {
        id: 'hogwarts',
        name: t('ui.styleTemplates.hogwarts'),
        image: '/images/styles/hogwarts.webp',
      },
      {
        id: 'barbie',
        name: t('ui.styleTemplates.barbie'),
        image: '/images/styles/barbie.webp',
      },
      {
        id: 'miku',
        name: t('ui.styleTemplates.miku'),
        image: '/images/styles/miku.webp',
      },
      {
        id: 'naruto',
        name: t('ui.styleTemplates.naruto'),
        image: '/images/styles/naruto1.webp',
      },
      {
        id: 'sailor-moon',
        name: t('ui.styleTemplates.sailorMoon'),
        image: '/images/styles/sailor_moon.webp',
      },
      {
        id: 'goku',
        name: t('ui.styleTemplates.goku'),
        image: '/images/styles/goku.webp',
      },
      {
        id: 'sailor-uniform',
        name: t('ui.styleTemplates.sailorUniform'),
        image: '/images/styles/sailor_uniform.webp',
      },
      {
        id: 'princess',
        name: t('ui.styleTemplates.princess'),
        image: '/images/styles/princess.webp',
      },
      {
        id: 'kimono',
        name: t('ui.styleTemplates.kimono'),
        image: '/images/styles/kimono.webp',
      },
      {
        id: 'magical-girl',
        name: t('ui.styleTemplates.magicalGirl'),
        image: '/images/styles/magical-girl.webp',
      },
      {
        id: 'cowboy',
        name: t('ui.styleTemplates.cowboy'),
        image: '/images/styles/cowboy.webp',
      },
    ],
  },
];