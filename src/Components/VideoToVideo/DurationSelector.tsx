import React from 'react';
import { useTranslation } from 'react-i18next';

interface DurationSelectorProps {
  selectedDuration: number;
  setSelectedDuration: (duration: number) => void;
  originalVideoDuration?: number;
  isClient: boolean;
  isSafari: boolean;
  className?: string;
}

export function DurationSelector({
  selectedDuration,
  setSelectedDuration,
  originalVideoDuration,
  isClient,
  isSafari,
  className = '',
}: DurationSelectorProps) {
  const { t } = useTranslation(['video-to-video']);

  return (
    <div className={`mb-4 ${className}`}>
      <label className='block mb-2 text-sm font-bold text-gray-700 md:text-base'>
        {t('ui.duration.title')}
        {originalVideoDuration && (
          <span className='ml-2 text-xs text-gray-500'>
            (
            {t('ui.duration.originalDuration', {
              duration: Math.round(originalVideoDuration * 10) / 10,
            })}
            )
          </span>
        )}
      </label>

      <div className='grid grid-cols-3 gap-2'>
        {(() => {
          // Safari用户且视频时长<=10s：只显示原始时长选项
          if (
            isClient &&
            isSafari &&
            originalVideoDuration &&
            originalVideoDuration <= 10
          ) {
            return (
              <label className='flex col-span-3 items-center px-3 py-2 text-sm rounded-lg border cursor-pointer md:text-base bg-primary-50 border-primary-300'>
                <input
                  type='radio'
                  name='duration'
                  value={originalVideoDuration}
                  checked={true}
                  readOnly
                  className='mr-1 accent-primary-600'
                />
                <span className='flex-1 text-sm'>
                  {Math.round(originalVideoDuration * 10) / 10}s (
                  {t('ui.duration.originalLength')})
                </span>
              </label>
            );
          }

          // 非Safari用户或Safari用户无视频：显示常规选项
          let durationOptions = [3, 5, 10];

          // If original video is shorter than 3 seconds, replace the first option with actual duration
          if (originalVideoDuration && originalVideoDuration < 3) {
            durationOptions = [originalVideoDuration, 5, 10];
          } else if (
            // If original video is between 5-10 seconds, show floor duration option
            originalVideoDuration &&
            originalVideoDuration >= 5 &&
            originalVideoDuration <= 10
          ) {
            const floorDuration = Math.floor(originalVideoDuration);
            if (floorDuration > 5) {
              // Show 3s, 5s, and floor duration (6s, 7s, 8s, 9s, etc.)
              durationOptions = [3, 5, floorDuration];
            } else {
              // For 5.x seconds, keep original options
              durationOptions = [3, 5, 10];
            }
          }

          return durationOptions.map((duration, index) => (
            <label
              key={index}
              className={`flex items-center py-2 px-3 text-sm md:text-base cursor-pointer border rounded-lg transition-all duration-300 hover:bg-primary-50 hover:border-primary-300 ${
                Math.abs(selectedDuration - duration) < 0.1
                  ? 'bg-primary-50 border-primary-300'
                  : originalVideoDuration &&
                      originalVideoDuration > 0 &&
                      duration > originalVideoDuration
                    ? 'bg-gray-100 border-gray-200 cursor-not-allowed opacity-50'
                    : 'bg-white border-gray-300'
              }`}>
              <input
                type='radio'
                name='duration'
                value={duration}
                checked={Math.abs(selectedDuration - duration) < 0.01}
                onChange={e => setSelectedDuration(Number(e.target.value))}
                disabled={
                  !!(
                    originalVideoDuration &&
                    originalVideoDuration > 0 &&
                    duration > originalVideoDuration
                  )
                }
                className='mr-1 accent-primary-600'
              />
              <span className='flex-1 text-sm'>
                {(duration < 3 &&
                  originalVideoDuration &&
                  originalVideoDuration < 3) ||
                (duration > 5 &&
                  duration <= 10 &&
                  originalVideoDuration &&
                  originalVideoDuration > 5 &&
                  originalVideoDuration <= 10)
                  ? `${Math.round(duration * 10) / 10}s`
                  : `${Math.round(duration)}s`}
                {/* Show "original length" label for custom duration options */}
                {originalVideoDuration &&
                  Math.abs(duration - originalVideoDuration) < 0.01 &&
                  duration !== 3 &&
                  duration !== 5 &&
                  duration !== 10 && (
                    <span className='block text-xs text-gray-500'>
                      ({t('ui.duration.originalLength')})
                    </span>
                  )}
                {originalVideoDuration &&
                  originalVideoDuration > 0 &&
                  duration > originalVideoDuration && (
                    <span className='block text-xs text-gray-400'>
                      ({t('ui.duration.tooLong')})
                    </span>
                  )}
              </span>
            </label>
          ));
        })()}
      </div>
      <p className='mt-2 text-xs text-gray-500'>
        {(() => {
          if (
            isClient &&
            isSafari &&
            originalVideoDuration &&
            originalVideoDuration <= 10
          ) {
            return null;
          }

          return originalVideoDuration && originalVideoDuration > selectedDuration
            ? t('ui.duration.willBeTrimmed', {
                original: Math.round(originalVideoDuration * 10) / 10,
                target: Math.round(selectedDuration * 10) / 10,
              })
            : t('ui.duration.description');
        })()}
      </p>
    </div>
  );
} 