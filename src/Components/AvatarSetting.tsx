import { Button, <PERSON>over, PopoverContent, Image, Tooltip, PopoverTrigger, Card, useDisclosure, Modal, ModalBody, ModalContent, Avatar, Divider } from "@nextui-org/react";
import { useAtom, useAtomValue } from "jotai";
import { useRouter } from "next/router";
import { useTranslation } from "react-i18next";
import { authAtom, profileAtom } from "state";
import { CgProfile } from "react-icons/cg";
import { FaCrown } from "react-icons/fa";
import { IoLanguage } from "react-icons/io5";
import LogoutButton from "./LogoutButton";
import { memo, useEffect, useState } from "react";
import cn from "classnames";
import { changeLocale } from "../utilities";
import i18n from "i18n";

export const languages = [
  { code: "en", name: "English" },
  { code: "es", name: "<PERSON>spa<PERSON><PERSON>" },
  { code: "ja", name: "日本語" },
  { code: "zh-C<PERSON>", name: "简体中文" },
  { code: "zh-TW", name: "繁體中文" },
  { code: "ko", name: "한국어" },
  { code: "de", name: "Deutsch" },
  { code: "fr", name: "Français" },
  { code: "pt", name: "Português" },
  { code: "id", name: "Bahasa Indonesia" },
  { code: "hi", name: "हिंदी" },
  { code: "ru", name: "Русский" },
  { code: "vi", name: "Tiếng Việt" },
  { code: "th", name: "ไทย" },
];

const AvatarSetting = memo(({className}: {className?: string}) => {
  const profile = useAtomValue(profileAtom);
  const router = useRouter();
  const { t } = useTranslation(['sidebar', 'common']);
  const isAuth = useAtomValue(authAtom);
  const [currentLanguage, setCurrentLanguage] = useState("en");

  const changeLanguage = (langCode: string) => {
    setCurrentLanguage(langCode);
    changeLocale(langCode);
    router.push(router.pathname, router.asPath.split('?')[0], { locale: langCode });
  };

  useEffect(() => {
    setTimeout(() => {
      let lang = localStorage.getItem("lang");
      if (!lang) {
        lang = router.locale || "en";
        changeLocale(lang);
      }
      const supportedLngs = Object.keys(i18n.options.resources || {}) as string[];
      if (router.locale && supportedLngs.includes(router.locale)) {
        setCurrentLanguage(router.locale);
      }
    });
  }, [router.locale]);

  return (
    <Popover placement="bottom-end">
    <PopoverTrigger>
      <Avatar
        as="button"
        className={cn("transition-transform", className)}
        color="primary"
        size="sm"
        src={profile?.image || "/images/favicon.webp"}
      />
    </PopoverTrigger>
    <PopoverContent className="p-1 w-[200px]">
      <div className="flex flex-col gap-1 w-full">
          {isAuth && (
            <>
              <Button
                className="justify-start h-10"
                variant="light"
                startContent={<CgProfile className="w-5 h-5 text-gray-500" />}
                onClick={() => router.push('/profile')}
              >
                {t('sidebar:profile')}
              </Button>
              <Button
                className="justify-start h-10"
                variant="light"
                startContent={<FaCrown className="w-5 h-5 text-gray-500" />}
                onClick={() => router.push('/pricing')}
              >
                {t('common:price.upgrade')}
              </Button>
            </>
          )}
          <Popover placement="bottom-start">
            <PopoverTrigger>
              <Button
                className="justify-start h-10"
                variant="light"
                startContent={<IoLanguage className="w-5 h-5 text-gray-500" />}
              >
                {languages.find(lang => lang.code === currentLanguage)?.name || "English"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="max-w-[200px] !justify-start p-0">
              <div className="p-2 max-h-[200px] overflow-y-auto">
                {languages.map((lang) => (
                  <Button
                    key={lang.code}
                    variant="light"
                    className="justify-start mb-1 w-full h-8"
                    onClick={() => changeLanguage(lang.code)}
                  >
                    {lang.name}
                  </Button>
                ))}
              </div>
            </PopoverContent>
          </Popover>
        {isAuth && <>
          <Divider className="my-1" />
          <LogoutButton className="justify-start" />
        </>}
      </div>
    </PopoverContent>
  </Popover>
  )
});

export default AvatarSetting;
