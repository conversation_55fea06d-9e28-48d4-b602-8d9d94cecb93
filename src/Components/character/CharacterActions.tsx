/* eslint-disable */
import { Button, ButtonGroup, Divider, Tooltip } from '@nextui-org/react';
import { useTranslation } from 'react-i18next';
import { useRouter } from 'next/router';
import { useAtomValue } from 'jotai';
import { authAtom } from '../../state';
import mixpanel from 'mixpanel-browser';
import toast from 'react-hot-toast';
import { IconEdit, IconTrash, IconUserSquare } from '@tabler/icons-react';
import { PiBookOpenBold, PiMagicWand } from 'react-icons/pi';
import { BsShareFill } from 'react-icons/bs';
import { RiScissorsCutFill } from 'react-icons/ri';
import { MdAnimation } from 'react-icons/md';
import { TbPhotoEdit } from 'react-icons/tb';
import { HiOutlineTrash, HiOutlinePlus } from 'react-icons/hi';

interface CharacterActionsProps {
  charData: any;
  isOwnCharacter: boolean;
  isCollected: boolean;
  isCollecting: boolean;
  onOpenDeleteModal: () => void;
  onCollectCharacter: () => void;
}

export const CharacterActions: React.FC<CharacterActionsProps> = ({
  charData,
  isOwnCharacter,
  isCollected,
  isCollecting,
  onOpenDeleteModal,
  onCollectCharacter,
}) => {
  const { t } = useTranslation('character');
  const router = useRouter();

  if (!charData.character_pfp || charData.character_uniqid === 'loading...') {
    return null;
  }

  return (
    <div className='flex flex-col pt-4 pb-3 w-full'>
      <div className='overflow-hidden w-full bg-white rounded-xl border border-gray-200 shadow-md'>
        {/* 工具按钮组 */}
        <div className='p-1'>
          {/* 移动端：两行布局 */}
          <div className='block md:hidden'>
            {/* 第一行：工具按钮 */}
            <div className='overflow-x-auto mb-1'>
              <ButtonGroup
                className='w-max min-w-full'
                radius='md'
                variant='light'>
                <ToolButtonsWithTooltips
                  charData={charData}
                  onOpenDeleteModal={onOpenDeleteModal}
                  isOwnCharacter={isOwnCharacter}
                />
              </ButtonGroup>
            </div>

            {/* 第二行：主要操作按钮 */}
            <ButtonGroup className='w-full' radius='md' variant='light'>
              <Tooltip content={t('generateImage')} placement='top'>
                <Button
                  onClick={() => {
                    try {
                      mixpanel.track('create.image.character', { ...charData });
                    } catch (error) {}
                    router.push({
                      pathname: '/ai-anime-generator',
                      query: {
                        prompt: `<${charData.character_uniqid}>`,
                      },
                    });
                  }}
                  className='flex gap-1 items-center justify-center px-2 h-10 text-primary flex-1'>
                  <PiMagicWand className='w-4 h-4' />
                  <span className='text-xs'>{t('generateImage')}</span>
                </Button>
              </Tooltip>

              <Tooltip content={t('createComic')} placement='top'>
                <Button
                  onClick={() => {
                    try {
                      mixpanel.track('create.story.character', { ...charData });
                    } catch (error) {}
                    router.push({
                      pathname: '/create',
                      query: {
                        prompt: `<${charData.character_uniqid}>`,
                      },
                    });
                  }}
                  className='flex gap-1 items-center justify-center px-2 h-10 text-primary flex-1'>
                  <PiBookOpenBold className='w-4 h-4' />
                  <span className='text-xs'>{t('createComic')}</span>
                </Button>
              </Tooltip>
            </ButtonGroup>
          </div>

          {/* 桌面端：单行布局 */}
          <div className='hidden md:block'>
            <ButtonGroup className='w-full' radius='md' variant='light'>
              <ToolButtonsWithTooltips
                charData={charData}
                onOpenDeleteModal={onOpenDeleteModal}
                isOwnCharacter={isOwnCharacter}
              />

              <Tooltip content={t('generateImage')} placement='top'>
                <Button
                  onClick={() => {
                    try {
                      mixpanel.track('create.image.character', { ...charData });
                    } catch (error) {}
                    router.push({
                      pathname: '/ai-anime-generator',
                      query: {
                        prompt: `<${charData.character_uniqid}>`,
                      },
                    });
                  }}
                  className='flex gap-1 items-center px-3 h-10 text-primary'>
                  <PiMagicWand className='w-4 h-4' />
                  {t('generateImage')}
                </Button>
              </Tooltip>

              <Tooltip content={t('createComic')} placement='top'>
                <Button
                  onClick={() => {
                    try {
                      mixpanel.track('create.story.character', { ...charData });
                    } catch (error) {}
                    router.push({
                      pathname: '/create',
                      query: {
                        prompt: `<${charData.character_uniqid}>`,
                      },
                    });
                  }}
                  className='flex gap-1 items-center px-3 h-10 text-primary'>
                  <PiBookOpenBold className='w-4 h-4' />
                  {t('createComic')}
                </Button>
              </Tooltip>
            </ButtonGroup>
          </div>
        </div>

        {/* 分隔线 */}
        <Divider className='w-full bg-gray-100' />

        <div className='p-2'>
          {isOwnCharacter ? (
            <PostCharacterButton charData={charData} />
          ) : (
            <CollectCharacterButton
              isCollected={isCollected}
              isCollecting={isCollecting}
              onCollect={onCollectCharacter}
            />
          )}
        </div>
      </div>
    </div>
  );
};

const ToolButtonsWithTooltips = ({
  charData,
  onOpenDeleteModal,
  isOwnCharacter,
}: {
  charData: any;
  onOpenDeleteModal: () => void;
  isOwnCharacter: boolean;
}) => {
  const { t } = useTranslation('common');
  const router = useRouter();
  const isAuth = useAtomValue(authAtom);

  const tools = [
    {
      path: '/playground',
      icon: <TbPhotoEdit className='w-4 h-4' />,
      labelKey: 'result_card.tools.restyle',
      queryParams: { style: '' },
    },
    {
      path: '/filter/ai-character-sheet-generator',
      icon: <IconUserSquare className='w-4 h-4' />,
      labelKey: 'result_card.tools.character_sheet',
      queryParams: {},
    },
    {
      path: '/background-removal',
      icon: <RiScissorsCutFill className='w-4 h-4' />,
      labelKey: 'result_card.tools.remove_bg',
    },
    {
      path: '/image-animation-generator',
      icon: <MdAnimation className='w-4 h-4' />,
      labelKey: 'result_card.tools.animate',
    },
  ];

  return (
    <>
      {tools.map((tool, idx) => (
        <Tooltip key={idx} content={t(tool.labelKey)} placement='top'>
          <Button
            isIconOnly
            variant='light'
            aria-label={t(tool.labelKey)}
            onClick={() => {
              try {
                if (charData && charData.character_pfp) {
                  const queryParams = new URLSearchParams();
                  queryParams.append('mediaType', 'character');
                  queryParams.append('source', 'character');
                  queryParams.append('prompt', charData.character_name || '');
                  queryParams.append('mediaUrl', charData.character_pfp);
                  if (tool.queryParams) {
                    for (const [key, value] of Object.entries(
                      tool.queryParams,
                    )) {
                      queryParams.append(key, value);
                    }
                  }
                  router.push(`${tool.path}?${queryParams.toString()}`);
                }
              } catch (error) {
                console.error('Failed to process image:', error);
                toast.error(t('error.imageProcessingFailed'));
              }
            }}
            className='h-10 text-gray-600 min-w-[40px] w-auto px-1 md:px-2'>
            {tool.icon}
          </Button>
        </Tooltip>
      ))}

      {isAuth && isOwnCharacter && (
        <>
          {/* 编辑按钮 - 只有当用户登录且是当前用户的角色时才显示 */}
          <Tooltip content={t('character:deleteCharacter')} placement='top'>
            <Button
              isIconOnly
              variant='light'
              onClick={onOpenDeleteModal}
              className='h-10 text-red-600 min-w-[40px] w-auto px-1 md:px-2'>
              <IconTrash className='w-4 h-4' />
            </Button>
          </Tooltip>
          {/* 删除按钮 - 只有当用户登录且是当前用户的角色时才显示 */}
          <Tooltip content={t('character:editCharacter')} placement='top'>
            <Button
              isIconOnly
              variant='light'
              onClick={() => {
                router.push(`/character/edit?id=${charData.character_uniqid}`);
              }}
              className='h-10 text-blue-600 min-w-[40px] w-auto px-1 md:px-2'>
              <IconEdit className='w-4 h-4' />
            </Button>
          </Tooltip>
        </>
      )}
    </>
  );
};

const PostCharacterButton = ({ charData }: { charData: any }) => {
  const { t } = useTranslation('character');
  const router = useRouter();

  return (
    <Button
      className='flex justify-center items-center w-full h-10 font-medium text-white bg-gradient-to-r rounded-lg shadow-sm transition-all from-primary-400 to-primary-500 hover:shadow-md'
      onClick={async () => {
        try {
          mixpanel.track('post.character', { ...charData });
        } catch (error) {}

        // 构建发布内容
        const publishContent = `${t('checkOutMyCharacter')}: ${window.origin}/character/${charData.character_uniqid}

${t('name')}: ${charData.character_name}
${t('gender')}: ${charData.gender}
${t('age')}: ${charData.age}
${t('profession')}: ${charData.profession}
${t('personality')}: ${charData.personality}
${t('interests')}: ${charData.interests}
${t('intro')}: ${charData.intro}`;

        const queryParams = new URLSearchParams();
        queryParams.append('mediaType', 'image');
        queryParams.append('source', 'character');
        queryParams.append('prompt', `My OC ${charData.character_name}`);
        queryParams.append('content', publishContent);
        queryParams.append('mediaUrl', charData.character_pfp);
        queryParams.append('tags', 'OC');

        router.push(`/publish?${queryParams.toString()}`);
      }}>
      <div className='flex justify-center items-center mr-2 w-5 h-5'>
        <BsShareFill className='w-4 h-4' />
      </div>
      <span>{t('post')}</span>
    </Button>
  );
};

const CollectCharacterButton = ({
  isCollected,
  isCollecting,
  onCollect,
}: {
  isCollected: boolean;
  isCollecting: boolean;
  onCollect: () => void;
}) => {
  const { t } = useTranslation('character');

  return (
    <Button
      className={`flex justify-center items-center w-full h-10 font-medium rounded-lg shadow-sm transition-all ${
        isCollected
          ? 'text-gray-600 bg-gray-200 hover:bg-gray-300'
          : 'text-white bg-gradient-to-r from-primary-400 to-primary-500 hover:shadow-md'
      }`}
      onClick={onCollect}
      isLoading={isCollecting}
      isDisabled={isCollecting}>
      {!isCollecting && (
        <div className='flex justify-center items-center mr-2 w-5 h-5'>
          {isCollected ? (
            <HiOutlineTrash className='w-4 h-4' />
          ) : (
            <HiOutlinePlus className='w-4 h-4' />
          )}
        </div>
      )}
      <span>
        {isCollecting
          ? t('loading')
          : isCollected
            ? t('uncollectOC')
            : t('collectOC')}
      </span>
    </Button>
  );
};
