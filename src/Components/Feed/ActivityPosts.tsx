import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from '@nextui-org/react';
import { IoClose, IoPlay, IoArrowForward } from 'react-icons/io5';
import { HiOutlineGift, HiOutlineCurrencyDollar } from 'react-icons/hi2';
import { useTranslation } from 'react-i18next';
import Link from 'next/link';
import { VipRing } from '../VipBadge';

interface ActivityPost {
  id: string;
  title: string;
  description: string;
  buttonText: string;
  href: string;
  icon: React.ReactNode;
  gradient: string;
  vipPlan: string;
  image?: string;
  newTab?: boolean;
}

interface ActivityPostsProps {
  index?: number;
}

export const ActivityPosts: React.FC<ActivityPostsProps> = ({ index }) => {
  const { t } = useTranslation('home');
  const [dismissedPosts, setDismissedPosts] = useState<string[]>([]);

  // 定义活动数据
  const activities: ActivityPost[] = [
    {
      id: 'video-to-video-ai',
      title: t('activity_posts.video_to_video.title'),
      description: t('activity_posts.video_to_video.description'),
      buttonText: t('activity_posts.video_to_video.button'),
      href: '/video-to-video',
      icon: <IoPlay className='w-5 h-5' />,
      gradient: 'from-blue-500 to-purple-600',
      vipPlan: 'Starter',
      image: '/images/banners/cover/video-to-video.webp',
    },
    {
      id: 'turn-characters-into-merch',
      title: t('activity_posts.character_merch.title'),
      description: t('activity_posts.character_merch.description'),
      buttonText: t('activity_posts.character_merch.button'),
      href: '/playground',
      icon: <HiOutlineGift className='w-5 h-5' />,
      gradient: 'from-purple-500 to-pink-600',
      vipPlan: 'Plus',
      image: '/images/banners/cover/merch.webp',
    },
    {
      id: 'komiko-ambassador',
      title: t('activity_posts.ambassador.title'),
      description: t('activity_posts.ambassador.description'),
      buttonText: t('activity_posts.ambassador.button'),
      href: 'https://komiko-app.notion.site/Click-Me-Ambassador-Link-Payment-Setup-Guide-Komiko-2334d853a19f8010abffcfdfdb3b38fd',
      newTab: true,
      icon: <HiOutlineCurrencyDollar className='w-5 h-5' />,
      gradient: 'from-green-500 to-yellow-500',
      vipPlan: 'Premium',
      image: '/images/banners/cover/ambassadors.webp',
    },
  ];

  // const handleDismiss = (postId: string) => {
  //   const newDismissed = [...dismissedPosts, postId];
  //   setDismissedPosts(newDismissed);
  // };

  // 如果指定了index，只显示对应的活动
  const visibleActivities =
    index !== undefined && index >= 0 && index < activities.length
      ? activities
          .slice(index, index + 1)
          .filter(activity => !dismissedPosts.includes(activity.id))
      : activities.filter(activity => !dismissedPosts.includes(activity.id));

  if (visibleActivities.length === 0) {
    return null;
  }

  return (
    <>
      {visibleActivities.map((activity, activityIndex) => (
        <div key={activity.id} className='caffelabs text-foreground mb-2'>
          <div className='transition-shadow duration-300 ease-in-out'>
            <VipRing plan={activity.vipPlan} intensity='light'>
              <Link
                href={activity.href}
                target={activity.newTab ? '_blank' : undefined}
                className='block cursor-pointer'>
                <div className='bg-white overflow-hidden'>
                  <Card className='bg-white shadow-none rounded-t-xl'>
                    <CardBody className='overflow-visible p-0 w-full rounded-t-xl'>
                      {activity.image ? (
                        <img
                          alt={activity.title}
                          className='w-full object-cover mx-auto rounded-none'
                          src={activity.image}
                        />
                      ) : (
                        <div
                          className={`w-full h-48 bg-gradient-to-r ${activity.gradient} flex items-center justify-center rounded-none`}>
                          <div className='text-white text-4xl'>
                            {activity.icon}
                          </div>
                        </div>
                      )}
                    </CardBody>

                    {/* 标题区域 */}
                    <CardFooter className='justify-between pt-2 px-3 pb-1 text-sm relative'>
                      <div className='flex flex-col gap-1'>
                        <b className='overflow-hidden max-h-12 whitespace-normal text-ellipsis text-sm leading-tight'>
                          {activity.title}
                        </b>
                        {/* description */}
                        <p className='text-xs md:text-sm text-default-500 leading-snug'>
                          {activity.description}
                        </p>
                      </div>
                      {/* 关闭按钮 */}
                      {/* <Button
                        isIconOnly
                        size='sm'
                        variant='light'
                        className='absolute top-1 right-1 text-gray-400 hover:text-gray-600 min-w-6 w-6 h-6 z-10'
                        onClick={e => {
                          e.preventDefault();
                          e.stopPropagation();
                          handleDismiss(activity.id);
                        }}>
                        <IoClose className='w-3 h-3' />
                      </Button> */}
                    </CardFooter>
                  </Card>

                  {/* 用户信息区域 */}
                  <div className='bg-white px-3 pb-2 pt-0 rounded-b-none'>
                    <div className='flex justify-between items-center text-small'>
                      <div className='flex items-center align-center'>
                        <div
                          className={`w-4 h-4 rounded-full bg-purple-50 flex items-center justify-center`}>
                          <img
                            src='/images/favicons/apple-icon.png'
                            alt='Komiko'
                            className='w-3 h-3'
                          />
                        </div>
                        <div className='flex items-center'>
                          <p className='ml-1 text-xs text-default-500'>
                            Komiko
                          </p>
                        </div>
                      </div>
                      <div
                        className='text-primary-700 hover:text-primary-600 text-sm flex items-center gap-1'
                        onClick={e => e.stopPropagation()} // 防止事件冒泡
                      >
                        {activity.buttonText}
                        <IoArrowForward className='w-4 h-4' />
                      </div>
                    </div>
                  </div>
                </div>
              </Link>
            </VipRing>
          </div>
        </div>
      ))}
    </>
  );
};
