/* eslint-disable */
import { useRouter } from 'next/router';
import { useState, useEffect, useCallback, useRef, memo } from 'react';
import React from 'react';
import {
  Image,
  Card,
  CardBody,
  CardFooter,
  Avatar,
  Textarea,
  Tab,
  Tabs,
  Button,
  Popover,
  PopoverTrigger,
  PopoverContent,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  Divider,
  Chip,
} from '@nextui-org/react';
import { IconSend2, IconShare3 } from '@tabler/icons-react';
import { AiOutlineHeart, AiFillHeart } from 'react-icons/ai';
import { formatDistanceToNow, parseISO } from 'date-fns';
import toast from 'react-hot-toast';
import { Post, authAtom, loginModalAtom, profileAtom } from '../../state';
import { useAtom, useAtomValue } from 'jotai';
import { useTranslation } from 'react-i18next';
import { FaVolumeUp, FaVolumeMute, FaPlay } from 'react-icons/fa';
import { shareLink } from '../../utilities';
import { HiOutlineDocumentDuplicate, HiOutlinePlus } from 'react-icons/hi2';
import { IoIosArrowBack } from 'react-icons/io';
import {
  RiFacebookCircleFill,
  RiLink,
  RiMoreFill,
  RiPinterestFill,
  RiRedditFill,
  RiTelegramFill,
  RiTwitterXFill,
  RiWhatsappFill,
  RiDeleteBin6Line,
} from 'react-icons/ri';
import { HiOutlineDownload } from 'react-icons/hi';
import Link from 'next/link';
import { VipRing, VipAvatar, VipCrownInline } from '../VipBadge';
import { cleanQualityModifiers } from '../../utilities/promptUtils';

function formatRelativeTime(timestamp: string) {
  const date = parseISO(timestamp);
  return formatDistanceToNow(date, { addSuffix: true });
}

interface PostCardProps {
  item: Post;
  handleOpen: (id: number, uniqid: string) => void;
  handleLike: (id: number) => void;
  handleFollow?: (id: number) => void;
  handleComment: (id: number) => void;
  handleCommentChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  comment: string;
  isOpen: boolean;
  handleClose: () => void;
  useAnchor?: boolean;
  showMore?: boolean;
  isFullScreen?: boolean;
  hideVipRing?: boolean; // 新增：控制是否隐藏VIP光环
}

export const PostCard: React.FC<PostCardProps> = ({
  item,
  handleOpen,
  handleLike,
  handleFollow,
  handleComment,
  handleCommentChange,
  comment,
  isOpen,
  handleClose,
  useAnchor,
  showMore,
  isFullScreen,
  hideVipRing = false,
}) => {
  const { t } = useTranslation('common');
  const router = useRouter();
  const [videoMuted, setVideoMuted] = useState(true);
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [showPlayButton, setShowPlayButton] = useState(true);
  const isAuth = useAtomValue(authAtom);
  const loginModal = useAtomValue(loginModalAtom);

  // ! HANDLE MODAL SIZE
  const adjustHeight = useCallback(() => {
    const img = document.getElementById('leftElement');
    const rightElement = document.getElementById('rightElement');
    if (img && rightElement) {
      rightElement.style.height = `${img.offsetHeight - 1}px`;
      console.log('adjusting height');
      console.log(`${img.offsetHeight}px`);
    }
  }, []);

  useEffect(() => {
    window.addEventListener('resize', adjustHeight);

    return () => {
      window.removeEventListener('resize', adjustHeight);
    };
  }, [adjustHeight]);

  useEffect(() => {
    if (isOpen) {
      // Use setTimeout to ensure the modal content has rendered
      setTimeout(adjustHeight, 10);
      setTimeout(adjustHeight, 100);
      setTimeout(adjustHeight, 250);
      setTimeout(adjustHeight, 500);
    }
  }, [isOpen, adjustHeight]);

  const onOpen = (e: React.MouseEvent) => {
    e.preventDefault();
    return (id: number, uniqid: string) => {
      handleOpen(id, uniqid);
    };
  };

  const handleVideoHover = (isHovering: boolean) => {
    if (videoRef.current) {
      if (isHovering) {
        videoRef.current.muted = videoMuted;
        videoRef.current
          .play()
          .then(() => {
            setIsPlaying(true);
            setShowPlayButton(false);
          })
          .catch(err => {
            console.error('Failed to play video:', err);
            setIsPlaying(false);
            setShowPlayButton(true);
          });
      } else {
        videoRef.current.pause();
        videoRef.current.currentTime = 0;
        // 重置为静音状态
        videoRef.current.muted = true;
        setVideoMuted(true);
        setIsPlaying(false);
        setShowPlayButton(true);
      }
    }
  };

  const toggleMute = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    if (videoRef.current) {
      const newMutedState = !videoRef.current.muted;
      videoRef.current.muted = newMutedState;
      setVideoMuted(newMutedState);
    }
  };

  const isFirstMediaVideo = item.media_type === 'video';
  // 决定是否显示VIP光环
  const shouldShowVipRing = !hideVipRing;
  const planToUse = shouldShowVipRing ? item.user_plan || 'Free' : 'Free';

  // 检查是否有标题内容来决定图片圆角
  const hasTitle = item.title && item.title.trim().length > 0;
  // 对于VIP用户，移除圆角以避免与VIP边框冲突
  const imageRoundedClass =
    shouldShowVipRing && planToUse !== 'Free'
      ? hasTitle
        ? 'rounded-t-none'
        : 'rounded-none'
      : hasTitle
        ? 'rounded-t-xl'
        : 'rounded-xl';

  // 白色内容区域（图片 + 标题）- 根据是否有标题和VIP状态决定圆角
  const whiteContentArea = (
    <Card
      className={`bg-white shadow-none ${
        shouldShowVipRing && planToUse !== 'Free'
          ? hasTitle
            ? 'rounded-t-none'
            : 'rounded-none'
          : hasTitle
            ? 'rounded-t-xl'
            : 'rounded-xl'
      }`}>
      {useAnchor ? (
        <a
          href={`/post/${item.uniqid}`}
          onClick={e => onOpen(e)(item.id, item.uniqid)}>
          <CardBody className={`overflow-visible p-0 ${imageRoundedClass}`}>
            {isFirstMediaVideo ? (
              <div
                className={`relative w-auto h-auto overflow-hidden ${imageRoundedClass}`}
                onMouseEnter={() => handleVideoHover(true)}
                onMouseLeave={() => handleVideoHover(false)}>
                <video
                  ref={videoRef}
                  className={`object-contain w-auto h-auto ${imageRoundedClass}`}
                  src={item.media[0]}
                  muted={videoMuted}
                  loop
                  playsInline
                  preload='metadata'
                  crossOrigin='anonymous'
                  onError={e => console.error('Video error:', e)}
                />
                {showPlayButton && (
                  <div className='flex absolute inset-0 justify-center items-center bg-black bg-opacity-30'>
                    <FaPlay size={40} color='white' />
                  </div>
                )}
                {isPlaying && (
                  <button
                    onClick={toggleMute}
                    className='absolute right-2 bottom-2 z-10 p-3 bg-black bg-opacity-50 rounded-full'>
                    {videoMuted ? (
                      <FaVolumeMute size={16} color='white' />
                    ) : (
                      <FaVolumeUp size={16} color='white' />
                    )}
                  </button>
                )}
              </div>
            ) : (
              <Image
                alt={item.title}
                className='w-full object-cover rounded-none'
                src={item.media[0]}
                style={{ height: 'auto' }}
              />
            )}
          </CardBody>
        </a>
      ) : (
        <CardBody
          className='overflow-visible p-0 w-full rounded-t-xl'
          onClick={e => onOpen(e)(item.id, item.uniqid)}
          onMouseEnter={() => isFirstMediaVideo && handleVideoHover(true)}
          onMouseLeave={() => isFirstMediaVideo && handleVideoHover(false)}>
          {isFirstMediaVideo ? (
            <div className='relative w-auto h-auto overflow-hidden flex justify-center'>
              <video
                ref={videoRef}
                className='object-contain w-auto h-auto rounded-t-xl'
                src={item.media[0]}
                muted={videoMuted}
                loop
                playsInline
                preload='metadata'
                crossOrigin='anonymous'
                onError={e => console.error('Video error:', e)}
              />
              {showPlayButton && (
                <div className='flex absolute inset-0 justify-center items-center bg-black bg-opacity-30'>
                  <FaPlay size={40} color='white' />
                </div>
              )}
              {isPlaying && (
                <button
                  onClick={toggleMute}
                  className='absolute right-2 bottom-2 z-10 p-3 bg-black bg-opacity-50 rounded-full'>
                  {videoMuted ? (
                    <FaVolumeMute size={16} color='white' />
                  ) : (
                    <FaVolumeUp size={16} color='white' />
                  )}
                </button>
              )}
            </div>
          ) : (
            <Image
              alt={item.title}
              className='w-full object-cover mx-auto rounded-none'
              src={item.media[0]}
              style={{ height: 'auto' }}
            />
          )}
        </CardBody>
      )}
      <CardFooter className='justify-between pt-2 px-3 pb-2 text-sm'>
        <b className='overflow-hidden max-h-16 whitespace-normal text-ellipsis'>
          {item.title}
        </b>
      </CardFooter>
    </Card>
  );

  // 用户信息区域 - 根据VIP状态决定底部圆角
  const userInfoArea = (
    <div
      className={`bg-white px-3 pb-3 pt-0 ${
        shouldShowVipRing && planToUse !== 'Free'
          ? 'rounded-b-none'
          : 'rounded-b-xl'
      }`}>
      <div className='flex justify-between items-center text-small'>
        <div className='flex items-center align-center'>
          <VipAvatar
            radius='full'
            className='w-5 h-5 text-tiny'
            src={item.image}
            name={item.user_name}
            plan={planToUse}
            crownSize='sm'
            showCrown={false}
            onClick={() => router.push(`/user/${item.user_uniqid}`)}
            style={{ cursor: 'pointer' }}
          />
          <div className='flex items-center'>
            <p
              className='ml-1 text-sm cursor-pointer text-default-500'
              onClick={() => router.push(`/user/${item.user_uniqid}`)}>
              {item.user_name}
            </p>
            <VipCrownInline plan={planToUse} size='sm' />
          </div>
        </div>
        <div className='flex flex-col gap-1 items-start align-center text-default-500'>
          <div
            className='flex justify-center items-center cursor-pointer'
            onClick={() => {
              if (!isAuth) {
                loginModal?.onOpen?.();
              } else {
                handleLike(item.id);
              }
            }}>
            {item.liked ? (
              <AiFillHeart className='text-sm text-red-500' />
            ) : (
              <AiOutlineHeart className='text-sm' />
            )}
            <span className='ml-1'>{item.votes}</span>
          </div>
        </div>
      </div>
    </div>
  );

  // 完整的卡片内容（图片 + 标题 + 用户信息）
  const fullCardContent = (
    <div className='bg-white overflow-hidden'>
      {whiteContentArea}
      {userInfoArea}
    </div>
  );

  return (
    <div key={item.id} className='caffelabs text-foreground mb-2 md:mb-3'>
      <div
        className={`transition-shadow duration-300 ease-in-out ${shouldShowVipRing && planToUse !== 'Free' ? '' : 'shadow-none hover:shadow-lg rounded-xl'}`}>
        {shouldShowVipRing && planToUse !== 'Free' ? (
          <VipRing plan={planToUse} intensity='light'>
            {fullCardContent}
          </VipRing>
        ) : (
          <div className='border border-gray-200 rounded-xl overflow-hidden shadow-sm'>
            {fullCardContent}
          </div>
        )}
      </div>

      {isOpen && (
        <Modal size='4xl' isOpen onClose={handleClose} closeButton>
          <ModalContent>
            <ModalBody className='overflow-y-auto pt-0 pr-0 pb-0 pl-0 h-full md:overflow-y-none'>
              <PostContent
                item={item}
                handleFollow={handleFollow}
                comment={comment}
                handleCommentChange={handleCommentChange}
                handleComment={handleComment}
                handleLike={handleLike}
                showMore={showMore}
                handleClose={handleClose}
                isFullScreen={isFullScreen}
              />
            </ModalBody>
          </ModalContent>
        </Modal>
      )}
    </div>
  );
};

export default PostCard;

interface PostContentProps {
  item: Post;
  handleFollow?: (id: number) => void;
  comment: string;
  handleCommentChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleComment: (id: number) => void;
  handleLike: (id: number) => void;
  showMore?: boolean;
  handleClose?: () => void;
  isFullScreen?: boolean;
}

const AuthorBar = memo(
  ({
    showMore,
    item,
    isAuth,
    router,
    handleFollow,
    setIsDeleteConfirmModalOpen,
  }: {
    showMore?: boolean;
    item: Post;
    isAuth: boolean;
    router: any;
    handleFollow?: (id: number) => void;
    setIsDeleteConfirmModalOpen: (open: boolean) => void;
  }) => {
    const { t } = useTranslation('common');
    const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

    // 使用真实的用户plan
    const userPlan = item.user_plan || 'Free';

    return (
      <div className='flex z-10 justify-between pr-6 pb-0 pl-5 mt-2 mb-2 bg-white'>
        <div>
          <div className='flex items-center align-center'>
            <div
              onClick={() => {
                if (isAuth) {
                  router.back();
                } else {
                  router.push('/');
                }
              }}
              className='block md:hidden'>
              <IoIosArrowBack className='mr-3 w-7 h-7' />
            </div>
            <VipAvatar
              radius='full'
              size='md'
              src={item.image}
              name={item.user_name}
              plan={userPlan}
              className='cursor-pointer'
              showCrown={false}
              onClick={() => router.push(`/user/${item.user_uniqid}`)}
            />
            <div className='flex items-center'>
              <p
                className='ml-3 text-base cursor-pointer text-default-500'
                onClick={() => router.push(`/user/${item.user_uniqid}`)}>
                {item.user_name}
              </p>
              <VipCrownInline plan={userPlan} size='sm' />
            </div>
          </div>
        </div>
        <div className='flex gap-2 items-center'>
          {handleFollow && (
            <Button
              variant={item.followed ? 'ghost' : 'solid'}
              radius='full'
              size='md'
              className={
                item.followed
                  ? 'bg-white text-black shadow-none w-[100px] text-[16px]'
                  : 'text-white bg-primary-500 w-[100px] text-[16px]'
              }
              onClick={async () => {
                if (isAuth === false) {
                  router.push('/login');
                } else {
                  handleFollow(item.id);
                }
              }}>
              {item.followed ? t('post_card.following') : t('post_card.follow')}
            </Button>
          )}
          {showMore && (
            <Popover
              placement='bottom-end'
              isOpen={isDeleteModalOpen}
              onOpenChange={open => {
                setIsDeleteModalOpen(open);
              }}>
              <PopoverTrigger>
                <Button isIconOnly variant='light' className='text-default-500'>
                  <RiMoreFill className='w-6 h-6' />
                </Button>
              </PopoverTrigger>
              <PopoverContent>
                <div className='px-1 py-2'>
                  <Button
                    className='justify-start w-full'
                    variant='light'
                    startContent={<RiDeleteBin6Line className='w-5 h-5' />}
                    onClick={() => {
                      setIsDeleteModalOpen(false);
                      setIsDeleteConfirmModalOpen(true);
                    }}>
                    {t('post_card.delete')}
                  </Button>
                </div>
              </PopoverContent>
            </Popover>
          )}
        </div>
      </div>
    );
  },
);
export const PostContent = ({
  item,
  showMore,
  handleFollow,
  comment,
  handleCommentChange,
  handleComment,
  handleLike,
  handleClose,
  isFullScreen,
}: PostContentProps) => {
  const { t } = useTranslation('common');
  const { t: tCharacter } = useTranslation('character');
  const router = useRouter();
  const { showSocial } = router.query;
  const [isAuth, setIsAuth] = useAtom(authAtom);
  const loginModal = useAtomValue(loginModalAtom);
  const profile = useAtomValue(profileAtom);
  const [showSocialButtons, setShowSocialButtons] = useState(
    (showSocial as string) == 'true',
  );
  const [isDeleteConfirmModalOpen, setIsDeleteConfirmModalOpen] =
    useState(false);
  const [isCollectingOC, setIsCollectingOC] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const shareText = t('post_card.share_text', { title: item.title });
  const shareUrl = {
    url: `/post/${item.uniqid}`,
    toString() {
      let origin = 'https://komiko.app';
      try {
        origin = window?.location?.origin || origin;
      } finally {
        return origin + (this as any).url;
      }
    },
  } as unknown as string;

  // 检测是否为OC post
  const isOCPost = () => {
    if (!item.post_tags || item.post_tags.length === 0) {
      return false;
    }

    // const hasOCTag = item.post_tags.some(
    //   tag =>
    //     tag.name.toLowerCase() === 'oc' ||
    //     tag.name.toLowerCase() === 'original character',
    // );

    // 或者有 <> 尖括号标签
    const hasAngleBracketTag = item.post_tags.some(
      tag => tag.name.includes('<') && tag.name.includes('>'),
    );

    return hasAngleBracketTag;
  };

  // 获取角色ID（从标签或内容中提取）
  const getCharacterIdFromTags = () => {
    // 首先从标签中查找
    if (item.post_tags) {
      const angleBracketTag = item.post_tags.find(
        tag => tag.name.includes('<') && tag.name.includes('>'),
      );

      if (angleBracketTag) {
        const match = angleBracketTag.name.match(/<([^>]+)>/);
        if (match && match[1]) return match[1];
      }
    }

    // 如果标签中没有，从内容中提取
    if (item.content) {
      const contentMatch = item.content.match(/<([^>]+)>/);
      if (contentMatch && contentMatch[1]) {
        return contentMatch[1];
      }
    }

    return null;
  };

  // 检查是否是自己的角色
  const isOwnCharacter =
    profile.authUserId &&
    item.authUserId &&
    item.authUserId === profile.authUserId;

  // 跳转到OC页面
  const handleCollectOC = async () => {
    setIsCollectingOC(true);

    const characterId = getCharacterIdFromTags();

    if (!characterId) {
      toast.error('Character not found');
      setIsCollectingOC(false);
      return;
    }

    try {
      let shouldShowCollectedToast = false;

      // 如果用户已登录，先尝试收藏
      if (isAuth) {
        if (!isOwnCharacter) {
          try {
            const response = await fetch('/api/characters', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                character_uniqid: characterId,
                action: 'collect',
              }),
            });

            const result = await response.json();
            if (result.code === 1) {
              shouldShowCollectedToast = true;
            }
          } catch (error) {
            console.error('Failed to collect character:', error);
          }
        }
      }

      // 跳转到角色页面，如果收藏成功则带上参数
      const targetUrl = shouldShowCollectedToast
        ? `/character/${characterId}?collected=true`
        : `/character/${characterId}`;
      await router.push(targetUrl);
    } catch (error) {
      console.error('Failed to navigate:', error);
      toast.error('Failed to navigate');
    } finally {
      setIsCollectingOC(false);
    }
  };

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const response = await fetch('/api/is_auth');
        const data = await response.json();
        console.log(data);
        if (data.is_auth === false) {
          setIsAuth(false);
        } else {
          setIsAuth(true);
        }
      } catch (error) {
        console.error('Failed to check authentication:', error);
        setIsAuth(false);
      }
    };
    checkAuth();
  }, []);

  const handleDelete = async () => {
    try {
      const response = await fetch('/api/post', {
        method: 'DELETE',
        body: JSON.stringify({ id: item.id }),
      });
      if (response.ok) {
        toast.success(t('post_card.delete_success'));
        // router.push('/');
        handleClose?.();
        window.location.reload();
      } else {
        toast.error(t('post_card.delete_failed'));
      }
    } catch (error) {
      console.error('Error deleting post:', error);
      toast.error(t('post_card.delete_failed'));
    }
  };

  return (
    <div
      className='flex overflow-hidden flex-col pt-0 pr-0 pb-0 pl-0 md:flex-row'
      style={isFullScreen ? { height: 'auto' } : { maxHeight: '80vh' }}>
      {/* Left section with images */}
      <div className='block md:hidden'>
        <AuthorBar
          showMore={showMore}
          item={item}
          isAuth={isAuth}
          router={router as any}
          setIsDeleteConfirmModalOpen={setIsDeleteConfirmModalOpen}></AuthorBar>
      </div>
      <div
        style={{ flex: '5 5 0%' }}
        className={` ${item.media.length === 1 ? 'overflow-hidden flex items-center justify-center' : 'overflow-y-auto'} w-full`}
        id='leftElement'>
        <div
          className={`${item.media.length === 1 ? 'flex flex-col items-center justify-center h-full w-full' : 'flex flex-col items-center w-full'}`}>
          {item.media_type === 'video' ? (
            // 对于视频类型，只渲染第一个媒体文件，使用原生控制器
            <div className='flex relative justify-center items-center w-full'>
              <video
                ref={videoRef}
                src={item.media[0]}
                className='object-contain w-full'
                id='responsiveVideo-0'
                controls
                autoPlay
                muted={false}
                loop
                style={{ maxHeight: '70vh' }}></video>
            </div>
          ) : (
            // 对于图片类型，渲染所有媒体文件
            item.media.map((mediaUrl, index) => (
              <img
                key={index}
                src={mediaUrl}
                alt={item.title}
                className={`${item.media.length === 1 ? 'object-contain max-h-full' : 'max-w-full object-contain'}`}
                style={
                  item.media.length === 1
                    ? { maxWidth: '100%', maxHeight: '100%' }
                    : {}
                }
                id={`responsiveImage-${index}`}
              />
            ))
          )}
          {!isAuth && (
            <div className='mt-2 w-full text-gray-500'>
              <div className='flex justify-center mb-1 w-full'>
                {t('post_card.the_end')}
              </div>

              <div className='flex justify-center w-full'>
                <div>
                  {t('post_card.discover')}&nbsp;
                  <Link href='/' className='text-blue-500'>
                    {t('post_card.more_stories')}
                  </Link>
                  &nbsp;{t('post_card.or_start')}&nbsp;
                  <Link href='/create' className='text-blue-500'>
                    {t('post_card.creating_your_own')}
                  </Link>
                  !
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Right section with content */}
      <div
        style={{ flex: '4 4 0%' }}
        className={`overflow-y-auto ${isFullScreen ? 'h-screen' : 'max-h-[85vh]'}`}
        id='rightElement' // Preserving the id
      >
        <Card className='shadow-none'>
          <div className='hidden mt-4 md:block'>
            <AuthorBar
              showMore={showMore}
              item={item}
              isAuth={isAuth}
              router={router as any}
              handleFollow={handleFollow}
              setIsDeleteConfirmModalOpen={
                setIsDeleteConfirmModalOpen
              }></AuthorBar>
          </div>
          <CardBody className='px-6 pt-1 pb-1'>
            <div className='overflow-y-auto'>
              <p className='mt-2 font-semibold text-large'>{item.title}</p>
              <p className='mt-2' style={{ whiteSpace: 'pre-wrap' }}>
                {item.content}
              </p>
              {item.post_tags && item.post_tags.length > 0 && (
                <div className='flex flex-wrap gap-2 mt-3'>
                  {item.post_tags.map(tag => {
                    // 如果是OC角色标签,跳转到角色页面
                    const isOcTag =
                      tag.name.startsWith('<') && tag.name.endsWith('>');
                    const href = isOcTag
                      ? `/character/${tag.name.slice(1, -1)}` // 移除<>符号
                      : `/tags/${tag.name}`;

                    return (
                      <Link
                        key={tag.id}
                        href={href}
                        onClick={() => {
                          handleClose?.();
                        }}>
                        <Chip
                          variant='flat'
                          className='text-primary-500 bg-primary-100'>
                          #{tag.name}
                        </Chip>
                      </Link>
                    );
                  })}
                </div>
              )}
              <p className='mt-2 text-sm text-default-500'>
                {formatRelativeTime(item.created_at)}
              </p>
              <Divider className='my-4' />
              <Tabs aria-label='Options' variant='underlined' color='primary'>
                <Tab key='Comments' title={t('post_card.comments')}>
                  {item.comments.map((comment, index) => (
                    <div key={index} className='flex mt-4 align-center'>
                      <Avatar
                        radius='full'
                        size='md'
                        className='mt-1'
                        src={comment.image}
                        name={comment.user_name}
                      />
                      <div className='pl-3' style={{ maxWidth: '80%' }}>
                        <p className='text-sm text-default-500'>
                          {comment.user_name}
                        </p>
                        <p className='text-sm text-black'>{comment.content}</p>
                        <p className='text-sm text-default-500'>
                          {formatRelativeTime(comment.created_at)}
                        </p>
                      </div>
                    </div>
                  ))}
                  {item.comments.length >= 0 && (
                    <div className='flex justify-center items-center mt-5 mb-5 w-full text-base text-default-500'>
                      {t('post_card.the_end_dash')}
                    </div>
                  )}
                </Tab>
                {isOCPost() && !isOwnCharacter && (
                  <Tab
                    key='CollectOC'
                    title={
                      <Button
                        size='sm'
                        onPress={() => {
                          handleCollectOC();
                        }}
                        variant='solid'
                        color='primary'
                        className='text-sm font-medium rounded-xl'
                        isLoading={isCollectingOC}>
                        <HiOutlinePlus />
                        {tCharacter('collectOCShort')}
                      </Button>
                    }
                  />
                )}
                {!isOCPost() && (
                  <Tab key='Prompts' title={t('post_card.prompts')}>
                    <div className='flex flex-col gap-3'>
                      {item.generations &&
                        item.generations.map((generation, index) => {
                          const prompt = cleanQualityModifiers(
                            generation.prompt,
                          );
                          return (
                            <Card
                              key={generation.url_path}
                              className='p-2 bg-gray-50'
                              shadow='sm'>
                              <div className='flex gap-2'>
                                <img
                                  alt={prompt}
                                  className='h-[90px] w-[90px] object-cover'
                                  src={`${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/husbando-land/image_generation/${generation.url_path}`}
                                />
                                <div
                                  className='text-[14px] max-h-[90px] overflow-hidden'
                                  style={{
                                    display: '-webkit-box',
                                    WebkitBoxOrient: 'vertical',
                                    WebkitLineClamp: 4,
                                  }}>
                                  {prompt}
                                </div>
                              </div>
                              <div className='flex justify-between py-1 pr-1 bg-white rounded-b-md'>
                                <Button
                                  size='sm'
                                  className='text-[14px] h-[28px] text-gray-700 bg-white flex items-center mr-2 gap-1 '
                                  onClick={() => {
                                    navigator.clipboard.writeText(prompt);
                                    toast.success(t('post_card.prompt_copied'));
                                  }}>
                                  <HiOutlineDocumentDuplicate className='w-[16px] h-[16px]' />
                                  {t('post_card.copy_prompt')}
                                </Button>
                                <Button
                                  size='sm'
                                  color='primary'
                                  className='text-[14px] h-[28px] rounded-full '
                                  onClick={() => {
                                    router.push(`/create?prompt=${prompt}`);
                                  }}>
                                  {t('post_card.generate_more')}
                                </Button>
                              </div>
                            </Card>
                          );
                        })}
                      {!item.generations ||
                        (item.generations.length === 0 && (
                          <div className='flex justify-center items-center text-base text-default-500'>
                            {t('post_card.no_prompts')}
                          </div>
                        ))}
                    </div>
                  </Tab>
                )}
              </Tabs>
            </div>
          </CardBody>
          <CardFooter className='justify-between pt-1 flex-[0] min-h-[56px]'>
            <Textarea
              className='flex rounded-full'
              radius='full'
              variant='flat'
              placeholder={t('post_card.join_discussion')}
              minRows={1}
              maxRows={4}
              style={{ resize: 'none' }}
              value={comment}
              onChange={handleCommentChange}
              onKeyDown={e => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleComment(item.id);
                }
              }}
            />
            <div className='flex justify-center items-center mr-1 cursor-pointer'>
              <IconSend2
                stroke={1.75}
                className={'w-8 h-8 text-lg'}
                onClick={() => {
                  if (!isAuth) {
                    loginModal?.onOpen?.();
                  } else {
                    handleComment(item.id);
                  }
                }}
              />
              <span className='ml-1'> </span>
            </div>
            <div
              className='flex justify-center items-center mr-2 cursor-pointer'
              onClick={() => {
                if (!isAuth) {
                  loginModal?.onOpen?.();
                } else {
                  handleLike(item.id);
                }
              }}>
              {item.liked ? (
                <AiFillHeart className='w-8 h-8 text-lg text-red-500' />
              ) : (
                <AiOutlineHeart className='w-8 h-8 text-lg' />
              )}
              <span className='ml-1'> {item.votes}</span>
            </div>
            <div className='flex justify-center items-center cursor-pointer'>
              <Popover
                placement='top-end'
                showArrow={true}
                isOpen={showSocialButtons}>
                <PopoverTrigger>
                  <IconShare3
                    stroke={1.75}
                    className={'w-8 h-8 text-lg'}
                    onMouseEnter={() => {
                      setShowSocialButtons(true);
                    }}
                    onClick={() => {
                      setShowSocialButtons(true);
                      navigator.clipboard.writeText(shareUrl);
                      toast.success(t('post_card.sharing_link_copied'));
                    }}
                  />
                </PopoverTrigger>
                <PopoverContent
                  className='px-2 py-2 mb-1 bg-gray-50 rounded-lg'
                  onMouseLeave={() => {
                    setShowSocialButtons(false);
                  }}>
                  <div className='flex flex-row gap-3 justify-between w-full'>
                    <Button
                      isIconOnly
                      className='bg-transparent'
                      onClick={() => {
                        const hashtags = 'comic,webtoon,Komiko';
                        const via = 'KomikoAI';
                        const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareText)}&url=${encodeURIComponent(shareUrl)}`;

                        window.open(twitterUrl, '_blank');
                      }}>
                      <RiTwitterXFill className='w-7 h-7 text-gray-700' />
                    </Button>
                    <Button
                      isIconOnly
                      className='bg-transparent'
                      onClick={() => {
                        const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`;
                        window.open(facebookUrl, '_blank');
                      }}>
                      <RiFacebookCircleFill className='w-8 h-8 text-[#0766FF]' />
                    </Button>
                    <Button
                      isIconOnly
                      className='bg-transparent'
                      onClick={() => {
                        const redditUrl = `https://www.reddit.com/submit?url=${encodeURIComponent(shareUrl)}&title=${encodeURIComponent(shareText)}`;
                        window.open(redditUrl, '_blank');
                      }}>
                      <RiRedditFill className='w-8 h-8 text-[#FF4500]' />
                    </Button>
                    <Button
                      isIconOnly
                      className='bg-transparent'
                      onClick={() => {
                        const redditUrl = `https://pinterest.com/pin/create/button/?url=${encodeURIComponent(shareUrl)}&media=${encodeURIComponent(item.media[0])}&description=${encodeURIComponent(shareText)}`;
                        window.open(redditUrl, '_blank');
                      }}>
                      <RiPinterestFill className='w-8 h-8 text-[#EC0023]' />
                    </Button>
                    <Button
                      isIconOnly
                      className='bg-transparent'
                      onClick={() => {
                        const redditUrl = `https://api.whatsapp.com/send?text=${encodeURIComponent(shareText)}%20${encodeURIComponent(shareUrl)}`;
                        window.open(redditUrl, '_blank');
                      }}>
                      <RiWhatsappFill className='w-8 h-8 text-[#24D366]' />
                    </Button>
                    <Button
                      isIconOnly
                      className='bg-transparent'
                      onClick={() => {
                        const redditUrl = `https://t.me/share/url?url=${encodeURIComponent(shareUrl)}&text=${encodeURIComponent(shareText)}`;
                        window.open(redditUrl, '_blank');
                      }}>
                      <RiTelegramFill className='w-8 h-8 text-[#247EC8]' />
                    </Button>
                  </div>
                  <div className='flex flex-row gap-1 justify-between w-full'>
                    <Button
                      className='gap-1 p-1 pl-2 bg-transparent'
                      onClick={async () => {
                        try {
                          // 创建一个新的canvas元素
                          const canvas = document.createElement('canvas');
                          const ctx = canvas.getContext('2d');
                          if (!ctx) {
                            console.error('Failed to get canvas context');
                            return;
                          }

                          // 加载所有图片
                          const images = await Promise.all(
                            item.media.map(
                              url =>
                                new Promise((resolve, reject) => {
                                  const img = new window.Image();
                                  img.crossOrigin = 'anonymous';
                                  img.onload = () => resolve(img);
                                  img.onerror = reject;
                                  img.src = url;
                                }),
                            ),
                          );

                          // 计算总高度和最大宽度
                          let totalHeight = 0;
                          let maxWidth = 0;
                          images.forEach((img: any) => {
                            totalHeight += img.height;
                            maxWidth = Math.max(maxWidth, img.width);
                          });

                          // 设置canvas尺寸
                          canvas.width = maxWidth;
                          canvas.height = totalHeight;

                          // 在canvas上绘制图片
                          let y = 0;
                          images.forEach((img: any) => {
                            ctx.drawImage(img, 0, y);
                            y += img.height;
                          });

                          // 转换canvas为Blob
                          const blob = await new Promise(resolve =>
                            canvas.toBlob(resolve, 'image/png'),
                          );

                          // 创建下载链接并触发下载
                          const url = URL.createObjectURL(blob as Blob);
                          const a = document.createElement('a');
                          a.href = url;
                          a.download = 'combined_image.png';
                          document.body.appendChild(a);
                          a.click();
                          document.body.removeChild(a);
                          URL.revokeObjectURL(url);

                          toast.success(t('post_card.image_downloaded'));
                        } catch (error) {
                          console.error('下载图片时出错：', error);
                          toast.error(t('post_card.download_failed'));
                        }
                      }}>
                      <HiOutlineDownload className='w-7 h-7 text-gray-900' />
                      {t('post_card.download_image')}
                    </Button>
                    <Button
                      className='gap-1 p-1 bg-transparent'
                      onClick={() => {
                        navigator.clipboard.writeText(shareUrl);
                        toast.success(t('post_card.sharing_link_copied'));
                      }}>
                      <RiLink className='w-7 h-7 text-gray-900' />
                      {t('post_card.copy_link')}
                    </Button>
                    <Button
                      className='gap-1 pr-0 pl-1 bg-transparent'
                      onClick={() => {
                        shareLink(
                          `${shareUrl}`,
                          `${item.title} | Komiko App`,
                          `${item.title} | Komiko App`,
                        );
                      }}>
                      <RiMoreFill className='w-7 h-7 text-gray-900' />
                      {t('post_card.more')}
                    </Button>
                  </div>
                </PopoverContent>
              </Popover>
            </div>
          </CardFooter>
        </Card>
      </div>
      <Modal
        isOpen={isDeleteConfirmModalOpen}
        onClose={() => setIsDeleteConfirmModalOpen(false)}>
        <ModalContent>
          <ModalHeader>{t('post_card.confirm_delete')}</ModalHeader>
          <ModalBody>
            <p>{t('post_card.confirm_delete_message')}</p>
          </ModalBody>
          <ModalFooter>
            <Button
              color='danger'
              variant='light'
              onPress={() => setIsDeleteConfirmModalOpen(false)}>
              {t('post_card.cancel')}
            </Button>
            <Button
              color='danger'
              onPress={() => {
                setIsDeleteConfirmModalOpen(false);
                handleDelete();
              }}>
              {t('post_card.delete')}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};
