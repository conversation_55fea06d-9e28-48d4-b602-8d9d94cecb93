/* eslint-disable */
import React, { useState, useRef, useEffect } from 'react';
import {
  Button,
  Card,
  Progress,
  Tabs,
  Tab,
  Textarea,
  Popover,
  PopoverTrigger,
  PopoverContent,
  Tooltip,
  Chip,
} from '@nextui-org/react';
import { FaDownload, FaCloudUploadAlt } from 'react-icons/fa';
import { BsInfoCircleFill, BsExclamationTriangle } from 'react-icons/bs';
import {
  MdOutlineAnimation,
  MdDashboard,
  MdEdit,
  MdImage,
  MdSync,
  MdVideoLibrary,
  MdPalette,
} from 'react-icons/md';
import { BiErrorCircle, BiSolidZap } from 'react-icons/bi';
import toast from 'react-hot-toast';
import { useAtom, useAtomValue } from 'jotai';
import { authAtom, profileAtom } from 'state';
import UploadFile from '../UploadFile';
import { ResultCard } from './ResultCard';
import {
  VideoData,
  deleteMediaData,
  filterValidVideo,
  cropReferenceToMatchFrame,
  getVideoDuration,
  uploadVideo,
} from './utils';
import { trimVideo } from '../../utilities/video';
import { useTranslation } from 'react-i18next';
import {
  calculateVideoToVideoStyleTransferCost,
  calculateVideoToVideoGenerationCost,
} from '../../../api/tools/_zaps';
import { useProcessInitialImage } from '../../hooks/useProcessInitialImage';
import { useFrameExtraction } from '../../hooks/useFrameExtraction';
import { FrameExtractionResult } from '../../utilities/video/firstFrameExtractor';
import { useBrowserDetection } from '../../hooks/useBrowserDetection';
import { VideoToolsTabs } from './VideoToolsTabs';
import { useVideos } from 'hooks/useVideos';
import { ModelIds } from '../../../api/_constants';
import { isNil } from 'lodash-es';
import { FramePreview } from '../VideoToVideo/FramePreview';
import { getStyleTemplateCategories } from '../VideoToVideo/constants';
import { generateHash } from '../../utilities/hashUtils';
import { DurationSelector } from '../VideoToVideo/DurationSelector';

type VideoApiParams = {
  method: 'getVideos' | 'generateVideo' | 'deleteVideo';
  video_url?: string;
  id?: number;
  prompt?: string;
};

interface VideosResponse {
  data: VideoData[];
}

const videosAPI = async (params: VideoApiParams): Promise<VideosResponse> => {
  const response = await fetch('/api/tools/video-to-video', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(params),
    credentials: 'include',
  });
  if (!response.ok) {
    throw new Error(`API request failed: ${response.status}`);
  }
  const data = await response.json();
  return data;
};

interface VideoToVideoState {
  uploadedVideo: File | null;
  styledFrame: string | null;
  finalVideo: string | null;
  isProcessing: boolean;
  progress: number;
  error: string | null;
  currentStep: 'upload' | 'extract' | 'style' | 'generate';
}

export default function VideoToVideoConvert() {
  const { t } = useTranslation(['video-to-video', 'common', 'toast']);
  const videoRefs = useRef<any>({});
  const videoGenerationSectionRef = useRef<HTMLDivElement>(null);
  const styleSelectionSectionRef = useRef<HTMLDivElement>(null);
  const generatedResultSectionRef = useRef<HTMLDivElement>(null);

  const [profile, setProfile] = useAtom(profileAtom);
  const [isClientMounted, setIsClientMounted] = useState(false);

  const { isSafari, isClient } = useBrowserDetection();

  const {
    inputImage: videoInput,
    setInputImage: setVideoInput,
    hasMedia,
    mediaItem,
    isFromTool,
  } = useProcessInitialImage();

  const formatPrompt = (task: any) => {
    try {
      // console.log('resultVideos', task.meta_data);
      const meta_data = JSON.parse(task.meta_data || '{}');
      return meta_data.prompt_will_show ?? task.prompt;
    } catch (e) {
      return task.prompt;
    }
  };

  const { resultVideos, setResultVideos, addTaskId, submitTask } = useVideos(
    'video-to-video',
    '/images/examples/video-to-video/multi-style.webm',
    { formatPrompt },
  );

  // 使用新的帧提取hook
  const {
    extractedFrame,
    extractedFrameDimensions,
    originalVideoDuration,
    extractionProgress,
    extractFrame,
    resetState: resetFrameState,
  } = useFrameExtraction({ quality: 0.9, showToasts: true });

  const [state, setState] = useState<VideoToVideoState>({
    uploadedVideo: null,
    styledFrame: null,
    finalVideo: null,
    isProcessing: false,
    progress: 0,
    error: null,
    currentStep: 'upload',
  });

  const [styleMode, setStyleMode] = useState<'template' | 'prompt' | 'upload'>(
    'template',
  );
  const [customPrompt, setCustomPrompt] = useState('');
  const [selectedStyle, setSelectedStyle] = useState('');
  const [referenceImage, setReferenceImage] = useState<File | null>(null);
  const [selectedDuration, setSelectedDuration] = useState<number>(3); // Default to 3 seconds

  // Add video generation mode and prompt states
  const [videoMode, setVideoMode] = useState<'human' | 'general'>('human'); // Default to human mode
  const [videoPrompt, setVideoPrompt] = useState(''); // Optional prompt for video generation

  // Add cost calculation states
  const [styleTransferCost, setStyleTransferCost] = useState(0);
  const [videoGenerationCost, setVideoGenerationCost] = useState(
    calculateVideoToVideoGenerationCost(3),
  );
  const [totalCost, setTotalCost] = useState(0);

  // Cache for uploaded frame URL to avoid repeated uploads
  const [uploadedFrameUrl, setUploadedFrameUrl] = useState<string | null>(null);
  const [frameHash, setFrameHash] = useState<string | null>(null);

  // Cache for uploaded video URL to avoid repeated uploads
  const [uploadedVideoUrl, setUploadedVideoUrl] = useState<string | null>(null);
  const [videoHash, setVideoHash] = useState<string | null>(null);

  useEffect(() => {
    if (extractedFrame) {
      const costMode = styleMode === 'prompt' ? 'custom' : styleMode;
      const cost = calculateVideoToVideoStyleTransferCost(costMode);
      setStyleTransferCost(cost);
    } else {
      setStyleTransferCost(0);
    }
  }, [styleMode, extractedFrame]);

  useEffect(() => {
    const total = styleTransferCost + videoGenerationCost;
    setTotalCost(total);
  }, [styleTransferCost, videoGenerationCost]);

  useEffect(() => {
    // Calculate cost based on actual processing duration
    // Only use original duration for cost when showing custom duration options (6s, 7s, 8s, 9s)
    // For standard options (3s, 5s, 10s), use selected duration for cost
    let actualDuration = selectedDuration;

    if (
      originalVideoDuration &&
      originalVideoDuration >= 5 &&
      originalVideoDuration <= 10
    ) {
      const floorDuration = Math.floor(originalVideoDuration);
      // If showing custom duration option (6s, 7s, 8s, 9s) and it's selected, use original duration
      if (floorDuration > 5 && selectedDuration === floorDuration) {
        actualDuration = originalVideoDuration;
      }
      // For standard options or 5.x seconds videos, use selected duration
    }

    setVideoGenerationCost(calculateVideoToVideoGenerationCost(actualDuration));
  }, [selectedDuration, originalVideoDuration]);

  useEffect(() => {
    // Safari用户跳过自动设置，因为会通过后端API获取
    if (isSafari) return;

    if (originalVideoDuration && originalVideoDuration > 0) {
      if (originalVideoDuration < 3) {
        setSelectedDuration(originalVideoDuration);
      } else if (originalVideoDuration >= 5 && originalVideoDuration <= 10) {
        // For videos between 5-10 seconds, use floor value (5.1s -> 5s, 6.8s -> 6s, etc.)
        const floorDuration = Math.floor(originalVideoDuration);
        setSelectedDuration(floorDuration);
      } else {
        const availableOptions = [3, 5, 10];
        const suitableOption =
          availableOptions.find(
            option =>
              originalVideoDuration !== undefined &&
              option <= originalVideoDuration,
          ) || availableOptions[0];
        setSelectedDuration(suitableOption);
      }
    }
  }, [originalVideoDuration, isSafari]);

  // 确保只在客户端执行
  useEffect(() => {
    setIsClientMounted(true);
  }, []);

  // Auto-handle video input when it changes (similar to VideoUpscale)
  useEffect(() => {
    if (videoInput && isClientMounted) {
      handleVideoUpload(videoInput);
    }
  }, [videoInput, isClientMounted]);

  const handleVideoUpload = async (url: string) => {
    try {
      // Convert URL to File object for compatibility
      const response = await fetch(url);

      // 检查响应是否成功
      if (!response.ok) {
        throw new Error(
          `Failed to fetch video: ${response.status} ${response.statusText}`,
        );
      }

      const blob = await response.blob();

      // 验证blob是否为视频格式
      if (!blob.type.startsWith('video/')) {
        throw new Error(
          `Invalid file type: ${blob.type}. Expected video format.`,
        );
      }

      const originalFile = new File([blob], 'uploaded-video.mp4', {
        type: blob.type || 'video/mp4',
      });

      // 立即清除之前的状态并设置处理中状态
      setState(prev => ({
        ...prev,
        uploadedVideo: null,
        styledFrame: null,
        finalVideo: null,
        currentStep: 'extract',
        error: null,
        isProcessing: true,
        progress: 10,
      }));

      // 重置帧提取状态
      resetFrameState();

      // 清除所有缓存（新视频上传时）
      setUploadedFrameUrl(null);
      setFrameHash(null);
      setUploadedVideoUrl(null);
      setVideoHash(null);

      setState(prev => ({
        ...prev,
        uploadedVideo: originalFile,
        progress: 25,
      }));

      // Safari特殊处理：给视频文件一些时间来完全加载到内存，并提前检查时长
      if (isSafari) {
        await new Promise(resolve => setTimeout(resolve, 500));

        let duration = 0;
        try {
          duration = await getVideoDuration(originalFile);

          // 检查视频时长是否超过10秒
          if (duration > 10) {
            toast.error(
              t('toast:video.styleTransfer.safariVideoTooLong', {
                duration: Math.round(duration * 10) / 10,
              }),
              {
                position: 'top-center',
                duration: 6000,
              },
            );

            // 重置状态
            setState(prev => ({
              ...prev,
              uploadedVideo: null,
              isProcessing: false,
              currentStep: 'upload',
              error: t('toast:video.styleTransfer.safariVideoTooLong', {
                duration: Math.round(duration * 10) / 10,
              }),
            }));
            resetFrameState();
            return;
          }
        } catch (durationError) {
          toast.error(
            t('toast:video.styleTransfer.safariDurationCheckFailed'),
            {
              position: 'top-center',
              duration: 4000,
            },
          );
        }

        // 更新选中的时长，价格会通过useEffect自动更新
        if (duration && duration <= 10) {
          setSelectedDuration(duration);
        }
      }

      setState(prev => ({
        ...prev,
        progress: 50,
      }));

      // 开始提取首帧，增加重试机制
      let extractResult: FrameExtractionResult | null = null;
      let retryCount = 0;
      const maxRetries = isSafari ? 3 : 1;

      while (!extractResult && retryCount < maxRetries) {
        try {
          extractResult = await extractFrame(originalFile);

          if (!extractResult) {
            throw new Error(
              t('toast:video.styleTransfer.frameExtractionEmpty'),
            );
          }

          break;
        } catch (error) {
          retryCount++;
          console.warn(`Frame extraction attempt ${retryCount} failed:`, error);

          if (retryCount < maxRetries) {
            // Safari waits longer before retrying
            const waitTime = isSafari ? 1000 * retryCount : 500;
            await new Promise(resolve => setTimeout(resolve, waitTime));

            toast.error(
              t('toast:video.styleTransfer.retryingFrameExtraction', {
                attempt: retryCount + 1,
              }),
              {
                position: 'top-center',
                duration: 2000,
              },
            );
          } else {
            throw error;
          }
        }
      }

      if (extractResult) {
        // Update video generation cost based on actual duration for Safari, or selected duration for other browsers
        const actualDuration = isSafari
          ? extractResult.videoDuration
          : Math.min(extractResult.videoDuration, selectedDuration);
        setVideoGenerationCost(
          calculateVideoToVideoGenerationCost(actualDuration),
        );

        // 对于Safari用户，设置duration为实际视频长度
        if (isSafari && extractResult.videoDuration <= 10) {
          setSelectedDuration(extractResult.videoDuration);
        }

        setState(prev => ({
          ...prev,
          isProcessing: false,
          progress: 50,
          currentStep: 'style',
        }));

        // Auto-scroll to style selection after successful frame extraction
        setTimeout(() => {
          if (styleSelectionSectionRef.current) {
            const elementTop =
              styleSelectionSectionRef.current.getBoundingClientRect().top;
            const offset = 80;
            window.scrollTo({
              top: window.pageYOffset + elementTop - offset,
              behavior: 'smooth',
            });
          }
        }, 800);
      } else {
        throw new Error(
          t('toast:video.styleTransfer.frameExtractionAllRetries'),
        );
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : t('toast:common.processingFailed');

      setState(prev => ({
        ...prev,
        error: errorMessage,
        isProcessing: false,
        currentStep: 'upload',
      }));

      // Special error tips for Safari
      if (isSafari && errorMessage.includes('timeout')) {
        toast.error(t('toast:video.styleTransfer.safariTimeoutAdvice'), {
          position: 'top-center',
          duration: 6000,
        });
      } else {
        toast.error(errorMessage, {
          position: 'top-center',
          duration: 4000,
        });
      }
    }
  };

  const handleVideoRemove = () => {
    // 重置所有相关状态
    setState(prev => ({
      ...prev,
      uploadedVideo: null,
      styledFrame: null,
      finalVideo: null,
      isProcessing: false,
      progress: 0,
      error: null,
      currentStep: 'upload',
    }));

    // 重置帧提取状态
    resetFrameState();

    // 清除缓存的帧URL和哈希
    setUploadedFrameUrl(null);
    setFrameHash(null);

    // 清除缓存的视频URL和哈希
    setUploadedVideoUrl(null);
    setVideoHash(null);

    setVideoGenerationCost(
      calculateVideoToVideoGenerationCost(selectedDuration),
    );
  };

  const handleStyleModeChange = (key: any) => {
    setStyleMode(key as any);
  };

  const handleStyleSelect = (styleId: string) => {
    setSelectedStyle(styleId);
  };

  const handleCustomPromptChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCustomPrompt(e.target.value);
  };

  const handleReferenceImageUpload = (url: string) => {
    // Convert URL to File object for compatibility
    fetch(url)
      .then(response => response.blob())
      .then(blob => {
        const file = new File([blob], 'reference-image.jpg', {
          type: 'image/jpeg',
        });

        // 如果有提取帧的尺寸，自动裁剪参考图像以匹配
        if (extractedFrameDimensions) {
          cropReferenceToMatchFrame(file, extractedFrameDimensions)
            .then(croppedFile => {
              setReferenceImage(croppedFile);
              toast.success(
                t('toast:video.styleTransfer.referenceImageCropped'),
                {
                  position: 'top-center',
                  duration: 3000,
                },
              );
            })
            .catch(error => {
              console.warn(
                'Auto-cropping failed, using original image:',
                error,
              );
              setReferenceImage(file);
              toast.error(t('toast:video.styleTransfer.autoCropFailed'), {
                position: 'top-center',
                duration: 3000,
              });
            });
        } else {
          setReferenceImage(file);
        }
      })
      .catch(error => {
        setState(prev => ({
          ...prev,
          error: 'Failed to process reference image',
        }));
      });
  };

  const handleReferenceImageRemove = () => {
    setReferenceImage(null);
  };

  const generateStyledFrame = async () => {
    if (styleMode !== 'upload' && profile.credit < styleTransferCost) {
      toast.error(t('toast:imageToVideo.purchaseMoreZaps'), {
        position: 'top-center',
        duration: 4000,
      });
      return;
    }

    // For upload mode, directly use the uploaded reference image as styled frame
    if (styleMode === 'upload' && referenceImage) {
      setState(prev => ({
        ...prev,
        isProcessing: true,
        currentStep: 'style',
        progress: 75,
        error: null,
      }));

      try {
        // Convert reference image to URL
        const referenceImageUrl = URL.createObjectURL(referenceImage);

        setState(prev => ({
          ...prev,
          styledFrame: referenceImageUrl,
          currentStep: 'generate',
          isProcessing: false,
          progress: 100,
        }));

        toast.success(t('toast:video.styleTransfer.referenceApplied'), {
          position: 'top-center',
          duration: 3000,
        });

        // Auto-scroll to video generation section after successful style application
        setTimeout(() => {
          if (videoGenerationSectionRef.current) {
            const elementTop = videoGenerationSectionRef.current.offsetTop;
            window.scrollTo({
              top: elementTop - 64, // Account for sticky navigation
              behavior: 'smooth',
            });
          }
        }, 500);

        return;
      } catch (error) {
        setState(prev => ({
          ...prev,
          error: t('toast:video.styleTransfer.useReferenceFailed'),
          isProcessing: false,
          currentStep: 'style',
        }));
        return;
      }
    }

    setState(prev => ({
      ...prev,
      isProcessing: true,
      currentStep: 'style',
      progress: 75,
      error: null, // Reset error state when starting generation
    }));

    // Clear other mode values when applying - ensure only current mode's settings are used
    if (styleMode === 'template') {
      setCustomPrompt('');
      setReferenceImage(null);
    } else if (styleMode === 'prompt') {
      setSelectedStyle('');
      setReferenceImage(null);
    } else if (styleMode === 'upload') {
      setCustomPrompt('');
      setSelectedStyle('');
    }

    try {
      // Convert data URL to blob and upload if needed
      let imageUrl = extractedFrame;

      if (imageUrl && imageUrl.startsWith('data:')) {
        const currentFrameHash = await generateHash(imageUrl);

        // Check if we have a cached upload for this frame
        if (frameHash === currentFrameHash && uploadedFrameUrl) {
          imageUrl = uploadedFrameUrl;
        } else {
          // Convert data URL to blob
          const response = await fetch(imageUrl);
          const blob = await response.blob();
          const file = new File([blob], 'extracted-frame.jpg', {
            type: 'image/jpeg',
          });

          // Upload the extracted frame
          const formData = new FormData();
          formData.append('file', file);
          formData.append(
            'imagePath',
            `video-to-video/frames/${Date.now()}-extracted-frame.jpg`,
          );

          const uploadResponse = await fetch('/api/uploadImage', {
            method: 'POST',
            body: formData,
          });

          if (!uploadResponse.ok) {
            throw new Error(
              t('toast:video.styleTransfer.uploadExtractedFrameFailed'),
            );
          }

          const uploadResult = await uploadResponse.json();
          imageUrl = uploadResult.data;

          // Cache the uploaded URL and hash
          setUploadedFrameUrl(imageUrl);
          setFrameHash(currentFrameHash);
        }
      }

      // Prepare API parameters
      let apiParams: any = {
        image_url: imageUrl,
        mode: styleMode === 'prompt' ? 'custom' : styleMode,
      };

      // Set parameters based on style mode
      switch (styleMode) {
        case 'template':
          apiParams.user_prompt = selectedStyle;
          break;
        case 'prompt':
          apiParams.user_prompt = customPrompt;
          break;
        default:
          throw new Error('Invalid style mode');
      }

      // Call the style transfer API
      const response = await fetch('/api/tools/video-to-video-style-transfer', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(apiParams),
      });

      if (!response.ok) {
        const errorData = await response.json();

        // 如果后端返回详细错误信息，使用它
        if (errorData.error) {
          const detailedError = new Error(errorData.error);
          // 将额外信息附加到错误对象上
          (detailedError as any).errorCode = errorData.errorCode;
          (detailedError as any).category = errorData.category;
          (detailedError as any).requestId = errorData.requestId;
          throw detailedError;
        }

        throw new Error(t('toast:video.styleTransfer.styleTransferFailed'));
      }

      const result = await response.json();

      if (!result.output) {
        throw new Error(t('toast:video.styleTransfer.invalidStyleResponse'));
      }

      setState(prev => ({
        ...prev,
        styledFrame: result.output,
        currentStep: 'generate',
        isProcessing: false,
        progress: 100,
      }));

      // Deduct credits for style transfer (template and prompt modes only reach this point)
      setProfile(prev => ({
        ...prev,
        credit: prev.credit - styleTransferCost,
      }));

      toast.success(t('toast:video.styleTransfer.styleApplied'), {
        position: 'top-center',
        duration: 3000,
      });

      // Auto-scroll to video generation section after successful style application
      setTimeout(() => {
        if (videoGenerationSectionRef.current) {
          const elementTop = videoGenerationSectionRef.current.offsetTop;
          window.scrollTo({
            top: elementTop - 64, // Account for sticky navigation
            behavior: 'smooth',
          });
        }
      }, 500);
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : t('toast:video.styleTransfer.styleTransferFailed');

      // 获取详细错误信息
      const errorCode = (error as any)?.errorCode;
      const category = (error as any)?.category;
      const requestId = (error as any)?.requestId;

      // 根据错误类型显示不同的toast持续时间
      const toastDuration = category === 'CONTENT_FILTER' ? 6000 : 4000;

      toast.error(errorMessage, {
        position: 'top-center',
        duration: toastDuration,
      });

      // 构建详细的错误信息用于UI显示
      let detailedErrorMessage = errorMessage;
      if (errorCode || requestId) {
        const errorDetails: string[] = [];
        if (errorCode)
          errorDetails.push(t('common:error.code', { code: errorCode }));
        if (requestId)
          errorDetails.push(t('common:error.requestId', { id: requestId }));
        detailedErrorMessage = `${errorMessage}\n${errorDetails.join(' | ')}`;
      }

      setState(prev => ({
        ...prev,
        error: detailedErrorMessage,
        isProcessing: false,
        currentStep: 'style',
      }));
    }
  };

  const canGenerateStyle = () => {
    // 根据当前选中的模式判断是否可以生成
    switch (styleMode) {
      case 'template':
        return selectedStyle !== '';
      case 'prompt':
        return customPrompt.trim() !== '';
      case 'upload':
        return referenceImage !== null;
      default:
        return false;
    }
  };

  const generateFinalVideo = async () => {
    if (!state.uploadedVideo || !state.styledFrame) {
      toast.error(t('toast:video.styleTransfer.missingVideoOrFrame'), {
        position: 'top-center',
        duration: 4000,
      });
      return;
    }

    // Check credits for video generation
    if (profile.credit < videoGenerationCost) {
      toast.error(t('toast:imageToVideo.purchaseMoreZaps'), {
        position: 'top-center',
        duration: 4000,
      });
      return;
    }

    setState(prev => ({
      ...prev,
      isProcessing: true,
      progress: 0,
      currentStep: 'generate',
      error: null,
    }));

    // 创建一个新的视频记录，先显示生成中状态
    const newVideoId = Date.now();
    const newVideo: VideoData = {
      id: newVideoId,
      video_url: '',
      prompt:
        styleMode === 'template'
          ? selectedStyle
          : styleMode === 'prompt'
            ? customPrompt
            : 'Video style transfer',
      status: 1, // 生成中状态
    };

    // 添加到结果列表顶部
    setResultVideos(prev => [
      newVideo,
      ...prev.filter(video => video.id !== -1),
    ]);

    // 显示开始生成的提示
    toast.success(t('toast:video.styleTransfer.videoGenerationStarted'), {
      position: 'top-center',
      duration: 3000,
    });
    // 电脑版自动滚动到顶部（结果区域）
    if (typeof window !== 'undefined' && window.innerWidth >= 768) {
      setTimeout(() => {
        if (generatedResultSectionRef.current) {
          const elementTop =
            generatedResultSectionRef.current.getBoundingClientRect().top;
          const offset = 80;
          window.scrollTo({
            top: window.pageYOffset + elementTop - offset,
            behavior: 'smooth',
          });
        }
      }, 800);
    }

    try {
      // 1. 处理视频截取（Safari特殊处理）
      let processedVideo = state.uploadedVideo;
      const originalDuration = originalVideoDuration || 0;

      // Safari用户：直接使用原视频，不进行剪切
      if (isSafari) {
        setState(prev => ({ ...prev, progress: 20 }));
        console.log('Safari: 使用原视频，跳过剪切');
      } else if (originalDuration > selectedDuration) {
        // 非Safari用户：检查是否需要剪切
        // 如果选择的是向下取整的时长，不进行剪切
        const floorDuration = Math.floor(originalDuration);
        const shouldSkipTrimming =
          selectedDuration === floorDuration && originalDuration <= 10;

        if (shouldSkipTrimming) {
          console.log(
            `跳过剪切：选择${selectedDuration}秒为${originalDuration}秒视频的向下取整，保留原始长度`,
          );
          setState(prev => ({ ...prev, progress: 20 }));
        } else {
          // 需要剪切的情况
          setState(prev => ({ ...prev, progress: 10 }));

          try {
            // 检查是否在客户端环境
            if (!isClientMounted) {
              throw new Error(
                'Video trimming can only be performed after client mount',
              );
            }

            processedVideo = await trimVideo(
              state.uploadedVideo,
              selectedDuration,
            );
          } catch (trimError) {
            console.warn(
              'Video trimming failed, using original video:',
              trimError,
            );
            processedVideo = state.uploadedVideo;

            const errorMessage =
              trimError instanceof Error
                ? trimError.message
                : t('toast:video.styleTransfer.trimFailedUsingOriginal');
            toast.error(errorMessage, {
              position: 'top-center',
              duration: 4000,
            });
          }

          setState(prev => ({ ...prev, progress: 20 }));
        }
      } else {
        setState(prev => ({ ...prev, progress: 20 }));
      }

      // 2. Upload processed video to get URL
      let videoUrl = '';
      if (processedVideo) {
        try {
          // Generate hash for current video
          const currentVideoHash = await generateHash(processedVideo);

          // Check if we have a cached upload for this video
          if (videoHash === currentVideoHash && uploadedVideoUrl) {
            videoUrl = uploadedVideoUrl;
          } else {
            // Create a temporary blob URL for the processed video
            const tempUrl = URL.createObjectURL(processedVideo);

            // Use utils uploadVideo function instead of API
            videoUrl = await uploadVideo(tempUrl);

            // Clean up the temporary URL
            URL.revokeObjectURL(tempUrl);

            if (!videoUrl) {
              throw new Error('Failed to upload video');
            }
            if (
              !videoUrl.startsWith('http://') &&
              !videoUrl.startsWith('https://')
            ) {
              throw new Error('Invalid video URL format');
            }

            // Cache the uploaded URL and hash
            setUploadedVideoUrl(videoUrl);
            setVideoHash(currentVideoHash);
          }
        } catch (uploadError) {
          throw new Error(t('toast:video.styleTransfer.uploadVideoFailed'));
        }
      }

      setState(prev => ({ ...prev, progress: 30 }));

      // 3. Handle reference image upload for upload mode
      let styledFrameUrl = state.styledFrame;
      if (styleMode === 'upload' && referenceImage) {
        const formData = new FormData();
        formData.append('file', referenceImage);
        formData.append(
          'imagePath',
          `video-to-video/reference/${profile.id}-${crypto.randomUUID()}-${Date.now()}-${referenceImage.name}`,
        );

        const uploadResponse = await fetch('/api/uploadImage', {
          method: 'POST',
          body: formData,
        });

        if (!uploadResponse.ok) {
          throw new Error(t('toast:video.styleTransfer.uploadReferenceFailed'));
        }

        const uploadResult = await uploadResponse.json();
        styledFrameUrl = uploadResult.data;
      }

      setState(prev => ({ ...prev, progress: 40 }));

      // 4. Get actual video duration from uploaded video URL for accurate cost calculation
      let actualVideoDuration = selectedDuration; // fallback to selected duration
      try {
        const rawDuration = await getVideoDuration(videoUrl);
        console.log(`Raw video duration from uploaded URL: ${rawDuration}s`);

        // 向下取整计算价格，对用户更友好
        actualVideoDuration = Math.floor(rawDuration);

        // 确保最小值为1秒，最大值为15秒
        actualVideoDuration = Math.max(1, Math.min(actualVideoDuration, 15));

        // Validate duration (should be reasonable)
        if (actualVideoDuration <= 0 || actualVideoDuration > 15) {
          console.warn(
            `Invalid duration ${actualVideoDuration}s, using selected duration ${selectedDuration}s`,
          );
          actualVideoDuration = Math.floor(selectedDuration);
        }
      } catch (error) {
        console.warn(
          'Failed to get duration from uploaded video, using selected duration:',
          error,
        );
        actualVideoDuration = Math.floor(selectedDuration);
      }

      // 5. Prepare API parameters
      const defaultPrompt =
        videoMode === 'human' && !videoPrompt
          ? 'Attractive human in aesthetic environment.'
          : '';
      const apiParams = {
        image: styledFrameUrl,
        video: videoUrl,
        prompt: videoPrompt || defaultPrompt, // Use the video prompt if provided, or default for human mode
        mode: videoMode, // Add video mode parameter
        target_model: videoMode === 'human' ? ModelIds.SE_COG : ModelIds.SE_WAN,
        selectedDuration: selectedDuration, // User selected duration for billing
        actualDuration: actualVideoDuration, // Actual video duration for backend validation
        tool: 'video-to-video',
        meta_data: {
          prompt_will_show:
            styleMode === 'template'
              ? selectedStyle
              : styleMode === 'prompt'
                ? customPrompt
                : 'Video style transfer',
        },
        should_delete_media: true,
      };

      const taskId = await submitTask(apiParams).catch();

      // Enhanced debugging for missing prediction ID
      if (isNil(taskId)) {
        console.error('API returned result without prediction ID:', {
          // result,
          // hasId: !!result.id,
          // resultKeys: Object.keys(result),
          apiParams,
        });
        setResultVideos(resultVideos =>
          resultVideos.filter(video => video.id !== newVideoId),
        );
        return;
      }

      setResultVideos(prev => {
        const index = prev.findIndex(video => video.id === newVideoId);
        if (index !== -1) {
          prev[index].id = taskId;
          return [...prev];
        }
        return prev;
      });

      setState(prev => ({ ...prev, progress: 50 }));

      addTaskId(taskId);
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : t('toast:video.styleTransfer.videoGenerationFailed');

      setResultVideos(prev => prev.filter(video => video.id !== newVideoId));

      toast.error(errorMessage, {
        position: 'top-center',
        duration: 4000,
      });

      setState(prev => ({
        ...prev,
        error: errorMessage,
        isProcessing: false,
        currentStep: 'generate',
      }));
    }
  };

  const handleDownload = async (url: string) => {
    try {
      const response = await fetch(url);
      const blob = await response.blob();
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = 'styled-video.mp4';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);
    } catch (error) {
      toast.error(t('toast:video.styleTransfer.downloadFailed'), {
        position: 'top-center',
        duration: 3000,
      });
    }
  };

  const handleDelete = async (id: number) => {
    if (typeof id === 'number' && id < 0) {
      setResultVideos(resultVideos => resultVideos.filter(d => d.id !== id));
      return;
    }
    try {
      const response = await videosAPI({ method: 'deleteVideo', id: id });
      if (response.data && response.data.length > 0) {
        deleteMediaData(setResultVideos, id);
        toast.success(t('toast:video.styleTransfer.videoDeleted'), {
          position: 'top-center',
          duration: 2000,
        });
      } else {
        toast.error(t('toast:video.styleTransfer.deleteFailed'), {
          position: 'top-center',
          duration: 3000,
        });
      }
    } catch (error) {
      toast.error(t('toast:video.styleTransfer.deleteFailed'), {
        position: 'top-center',
        duration: 3000,
      });
    }
  };

  // 保存生成的视频到数据库
  const saveVideoToDatabase = async (videoUrl: string, prompt: string) => {
    try {
      const response = await videosAPI({
        method: 'generateVideo',
        video_url: videoUrl,
        prompt: prompt,
      });
      if (response.data && response.data.length > 0) {
        return response.data[0];
      }
    } catch (error) {
      // Silent fail for database save
    }
    return null;
  };

  return (
    <div className='grid grid-cols-12 gap-6 mt-4'>
      {/* 左侧输入区域 */}
      <div className='flex flex-col h-full col-span-12 md:col-span-6 lg:col-span-5'>
        <Card className='p-6 flex flex-col h-full transition-all duration-300 shadow-2xl border-1.5 border-primary-200'>
          <div className='flex justify-between items-center mb-4 md:mb-6'>
            <VideoToolsTabs activeTab='video-to-video' />
            <Tooltip
              content={t('ui.tooltip')}
              color='primary'
              className='hidden md:block'>
              <span>
                <BsInfoCircleFill className='hidden text-primary-500 md:block' />
              </span>
            </Tooltip>
          </div>

          <div className='flex overflow-y-auto flex-col flex-1'>
            {/* 1. Video Upload */}
            <div className='mb-6'>
              <UploadFile
                type='video'
                accept='.mp4,.mov,.avi'
                removable
                initialImage={videoInput}
                onRemove={handleVideoRemove}
                onChange={handleVideoUpload}>
                <div className='text-center'>
                  <FaCloudUploadAlt className='mx-auto mb-2 w-12 h-12 text-gray-400' />
                  <p className='mb-2 text-sm text-gray-500 md:text-base'>
                    {t('ui.upload.dragText')}
                  </p>
                </div>
              </UploadFile>

              {/* Best results tip */}
              {/* <div className='p-3 mt-3 bg-amber-50 rounded-lg border border-amber-200'>
                <div className='flex gap-2 items-start'>
                  <BsExclamationTriangle className='text-amber-600 w-4 h-4 mt-0.5 flex-shrink-0' />
                  <p className='text-sm text-amber-700'>
                    {t('ui.upload.bestResultsTip1')}
                    <br />
                    {t('ui.upload.bestResultsTip2')}
                  </p>
                </div>
              </div> */}

              {/* Safari warning for long videos */}
              {isClient && isSafari && (
                <div className='p-3 mt-2 bg-blue-50 rounded-lg border border-blue-200'>
                  <div className='flex gap-2 items-start'>
                    <BsInfoCircleFill className='text-blue-600 w-4 h-4 mt-0.5 flex-shrink-0' />
                    <div>
                      <p className='text-sm font-medium text-blue-700'>
                        {t('ui.upload.safariNotice')}
                      </p>
                      <p className='mt-1 text-xs text-blue-600'>
                        {t('ui.upload.safariLimitWarning')}
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Auto extract frame when video is uploaded */}
            {state.uploadedVideo && !extractedFrame && !state.isProcessing && (
              <div className='mb-6'>
                <div className='p-3 bg-blue-50 rounded-lg border border-blue-200'>
                  <Button
                    size='sm'
                    color='primary'
                    variant='flat'
                    onClick={() => {
                      if (state.uploadedVideo) {
                        extractFrame(state.uploadedVideo);
                      }
                    }}
                    className='w-full cursor-pointer'>
                    {t('ui.upload.extractFrameManually')}
                  </Button>
                </div>
              </div>
            )}

            {/* 2. Style Selection */}
            <div ref={styleSelectionSectionRef}>
              <div className='flex gap-2 items-center mb-3'>
                <label className='block text-sm font-bold text-gray-700 md:text-base'>
                  {t('ui.steps.styleSelection')}
                </label>
                <Popover placement='right'>
                  <PopoverTrigger>
                    <span>
                      <BsInfoCircleFill className='w-3 h-3 transition-colors cursor-help md:w-4 md:h-4 text-primary-500 hover:text-primary-600' />
                    </span>
                  </PopoverTrigger>
                  <PopoverContent className='p-3 max-w-xs'>
                    <p className='mb-1'>{t('ui.framePreview.styleTooltip')}</p>
                  </PopoverContent>
                </Popover>
              </div>

              {/* Style selection tabs and content */}
              <>
                <Tabs
                  selectedKey={styleMode}
                  onSelectionChange={handleStyleModeChange}
                  fullWidth
                  classNames={{
                    tabList: 'gap-0',
                    panel: 'focus:outline-none focus:ring-0',
                  }}
                  size='sm'>
                  <Tab
                    key='template'
                    title={
                      <div className='flex gap-2 items-center'>
                        <MdDashboard className='w-4 h-4' />
                        <span>{t('ui.styleModes.templates')}</span>
                      </div>
                    }>
                    <div className='max-h-[400px] overflow-y-auto p-1'>
                      {getStyleTemplateCategories(t).map(
                        (category, categoryIndex) => (
                          <div
                            key={category.category}
                            className={categoryIndex > 0 ? 'mt-6' : ''}>
                            <div className='flex items-center gap-2 mb-3'>
                              <span className='text-lg'>{category.icon}</span>
                              <h3 className='text-sm font-semibold text-gray-700'>
                                {category.category}
                              </h3>
                            </div>
                            <div className='grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3'>
                              {category.templates.map(style => (
                                <div
                                  key={style.id}
                                  onClick={() => handleStyleSelect(style.id)}
                                  className={`cursor-pointer rounded-lg p-1.5 transition-all duration-200 ${
                                    selectedStyle === style.id
                                      ? 'border-2 border-primary-600 bg-primary-50 shadow-md'
                                      : 'border border-gray-200 hover:border-primary-300 hover:bg-primary-50/30'
                                  }
                                `}>
                                  <div className='overflow-hidden mb-1 rounded-md aspect-square'>
                                    <img
                                      src={style.image}
                                      alt={style.name}
                                      className='object-cover object-top w-full h-full'
                                      draggable={false}
                                    />
                                  </div>
                                  <p className='text-[10px] font-medium text-center truncate leading-tight'>
                                    {style.name}
                                  </p>
                                </div>
                              ))}
                            </div>
                          </div>
                        ),
                      )}
                    </div>
                  </Tab>

                  <Tab
                    key='prompt'
                    title={
                      <div className='flex gap-2 items-center'>
                        <MdEdit className='w-4 h-4' />
                        {t('ui.styleModes.prompt')}
                      </div>
                    }>
                    <div>
                      <Textarea
                        placeholder={t('ui.prompt.placeholder')}
                        value={customPrompt}
                        onChange={handleCustomPromptChange}
                        minRows={3}
                        className='mb-2'
                      />
                      <p className='text-xs text-gray-500'>
                        {t('ui.prompt.example')}
                      </p>
                    </div>
                  </Tab>

                  <Tab
                    key='upload'
                    title={
                      <div className='flex gap-2 items-center'>
                        <MdImage className='w-4 h-4' />
                        {t('ui.styleModes.reference')}
                      </div>
                    }
                    data-focus-visible='false'>
                    <div>
                      <UploadFile
                        type='image'
                        accept='.png,.jpg,.jpeg,.webp'
                        limit={10}
                        removable
                        onRemove={handleReferenceImageRemove}
                        onChange={handleReferenceImageUpload}
                        className='border-gray-300 hover:border-primary-400'>
                        <div className='text-center'>
                          <MdImage className='mx-auto mb-2 w-12 h-12 text-gray-400' />
                          <p className='mb-2 text-sm text-gray-600 md:text-base'>
                            {t('ui.reference.uploadText')}
                          </p>
                          <p className='mt-1 text-xs text-gray-500 md:text-sm'>
                            {t('ui.reference.formatInfo')}
                          </p>
                        </div>
                      </UploadFile>
                    </div>
                  </Tab>
                </Tabs>

                <Button
                  color='primary'
                  variant='flat'
                  onClick={generateStyledFrame}
                  disabled={
                    (state.isProcessing && state.currentStep === 'style') ||
                    !canGenerateStyle() ||
                    !extractedFrame
                  }
                  isLoading={
                    state.isProcessing && state.currentStep === 'style'
                  }
                  className='w-full cursor-pointer disabled:cursor-not-allowed mb-4 md:mb-6'
                  startContent={
                    state.isProcessing &&
                    state.currentStep ===
                      'style' ? undefined : state.styledFrame ? (
                      <MdSync className='w-4 h-4 flex-shrink-0·' />
                    ) : (
                      <MdPalette className='flex-shrink-0 w-4 h-4' />
                    )
                  }
                  endContent={
                    styleMode !== 'upload' && styleTransferCost > 0 ? (
                      <Chip
                        startContent={
                          <BiSolidZap className='mr-0 w-4 h-4 text-orange-400' />
                        }
                        variant='flat'
                        color='primary'
                        size='sm'
                        className='bg-white'>
                        -{styleTransferCost}/{profile.credit}
                      </Chip>
                    ) : undefined
                  }>
                  {state.isProcessing && state.currentStep === 'style'
                    ? t('ui.buttons.applying')
                    : state.styledFrame
                      ? styleMode === 'upload'
                        ? t('ui.buttons.useNewReference')
                        : t('ui.buttons.applyNewStyle')
                      : styleMode === 'upload'
                        ? t('ui.buttons.useReference')
                        : t('ui.buttons.applyStyle')}
                </Button>

                {/* Show upload prompt when no video */}
                {!extractedFrame && !state.isProcessing && (
                  <div className='p-2 bg-gray-50 rounded-lg md:p-4'>
                    <div className='text-center'>
                      <MdVideoLibrary className='flex-shrink-0 mx-auto mb-2 w-6 h-6 text-gray-600 md:w-8 md:h-8' />
                      <p className='text-sm font-medium text-gray-700'>
                        {t('toast:video.styleTransfer.uploadVideoFirst')}
                      </p>
                    </div>
                  </div>
                )}

                {/* Warning for reference mode */}
                {styleMode === 'upload' && extractedFrame && (
                  <div className='p-3 mb-3 bg-amber-50 rounded-lg border border-amber-200'>
                    <div className='flex gap-2 items-start'>
                      <BsExclamationTriangle className='text-amber-600 w-4 h-4 mt-0.5 flex-shrink-0' />
                      <p className='text-sm text-amber-700'>
                        {t('ui.reference.compositionWarning')}
                      </p>
                    </div>
                  </div>
                )}

                {/* Show loading state when extracting */}
                {!extractedFrame &&
                  state.isProcessing &&
                  state.currentStep === 'extract' && (
                    <div className='p-3 mb-4 bg-blue-50 rounded-lg border border-blue-200'>
                      <div className='flex justify-center items-center space-x-2'>
                        <div className='w-4 h-4 rounded-full border-b-2 border-blue-600 animate-spin'></div>
                        <p className='text-sm text-blue-700'>
                          {t('toast:video.styleTransfer.extractingFrame')}
                        </p>
                      </div>
                      {extractionProgress > 0 && (
                        <div className='overflow-hidden mt-2 w-full'>
                          <div className='w-full max-w-full'>
                            <Progress
                              value={extractionProgress}
                              color='primary'
                              size='sm'
                              className='w-full'
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                {/* Before and after comparison - shown after button */}
                <FramePreview
                  extractedFrame={extractedFrame}
                  styledFrame={state.styledFrame}
                  isProcessing={state.isProcessing}
                  currentStep={state.currentStep}
                />
              </>
            </div>

            {/* 3. Generate Video */}
            {state.styledFrame && (
              <>
                {/* Visual separator */}
                <div className='flex items-center my-6'>
                  <div className='flex-1 border-t border-gray-200'></div>
                  <div className='px-4 text-xs text-gray-500 bg-white'>
                    {t('ui.separators.readyToGenerate')}
                  </div>
                  <div className='flex-1 border-t border-gray-200'></div>
                </div>

                {/* Video Generation Mode Selection */}
                <div className='mb-6' ref={videoGenerationSectionRef}>
                  <label className='block mb-2 text-sm font-semibold text-gray-700 md:text-base'>
                    {t('ui.videoMode.title')}
                  </label>
                  <div className='grid grid-cols-2 gap-2'>
                    <label
                      className={`flex items-center py-2 px-3 cursor-pointer border rounded-lg transition-all duration-300 hover:bg-primary-50 hover:border-primary-300 ${
                        videoMode === 'human'
                          ? 'bg-primary-50 border-primary-300'
                          : 'bg-white border-gray-300'
                      }`}>
                      <input
                        type='radio'
                        name='videoMode'
                        value='human'
                        checked={videoMode === 'human'}
                        onChange={e =>
                          setVideoMode(e.target.value as 'human' | 'general')
                        }
                        className='mr-2 accent-primary-600'
                      />
                      <div className='flex-1'>
                        <div className='text-sm font-medium text-gray-800'>
                          {t('ui.videoMode.human')}
                        </div>
                      </div>
                    </label>

                    <label
                      className={`flex items-center py-2 px-3 cursor-pointer border rounded-lg transition-all duration-300 hover:bg-primary-50 hover:border-primary-300 ${
                        videoMode === 'general'
                          ? 'bg-primary-50 border-primary-300'
                          : 'bg-white border-gray-300'
                      }`}>
                      <input
                        type='radio'
                        name='videoMode'
                        value='general'
                        checked={videoMode === 'general'}
                        onChange={e =>
                          setVideoMode(e.target.value as 'human' | 'general')
                        }
                        className='mr-2 accent-primary-600'
                      />
                      <div className='flex-1'>
                        <div className='text-sm font-medium text-gray-800'>
                          {t('ui.videoMode.general')}
                        </div>
                      </div>
                    </label>
                  </div>
                </div>

                {/* Optional Video Prompt */}
                <div className='mb-4'>
                  <label className='block mb-2 text-sm font-bold text-gray-700 md:text-base'>
                    {t('ui.videoPrompt.title')}
                  </label>
                  <Textarea
                    placeholder={t('ui.videoPrompt.placeholder')}
                    value={videoPrompt}
                    onChange={e => setVideoPrompt(e.target.value)}
                    minRows={2}
                    className='mb-1'
                  />
                  <p className='text-xs text-gray-500'>
                    {t('ui.videoPrompt.description')}
                  </p>
                </div>

                {/* Duration Selection */}
                {originalVideoDuration && (
                  <DurationSelector
                    selectedDuration={selectedDuration}
                    setSelectedDuration={setSelectedDuration}
                    originalVideoDuration={originalVideoDuration}
                    isClient={isClient}
                    isSafari={isSafari}
                  />
                )}

                <Button
                  color='primary'
                  onClick={generateFinalVideo}
                  disabled={false}
                  isLoading={false}
                  size='lg'
                  className='w-full cursor-pointer'
                  endContent={
                    <Chip
                      startContent={
                        <BiSolidZap className='mr-0 w-4 h-4 text-orange-400' />
                      }
                      variant='bordered'
                      color='primary'
                      size='sm'
                      className='bg-white'>
                      -{videoGenerationCost}/{profile.credit}
                    </Chip>
                  }>
                  {state.finalVideo
                    ? t('ui.buttons.generateMore')
                    : t('ui.buttons.generateVideo')}
                </Button>
              </>
            )}

            {/* Error Display */}
            {state.error && (
              <div className='p-3 mt-4 bg-red-50 rounded-lg border border-red-200'>
                <div className='flex gap-2 items-start'>
                  <BiErrorCircle className='text-red-600 w-4 h-4 mt-0.5 flex-shrink-0' />
                  <div className='flex-1'>
                    <p className='text-sm text-red-600 whitespace-pre-line'>
                      {state.error}
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </Card>
      </div>

      {/* 右侧结果区域 */}
      <div className='flex flex-col col-span-12 h-full md:col-span-6 lg:col-span-7'>
        <Card className='p-4 md:pb-6 md:px-6 md:pt-4 h-full shadow-md md:shadow-2xl border-1.5 border-primary-50'>
          <h2
            className='flex items-center mb-4 text-base font-bold md:mb-6 md:text-2xl text-primary-800'
            ref={generatedResultSectionRef}>
            <FaDownload className='mr-2 text-primary-600' />{' '}
            {t('ui.generatedVideos')}
          </h2>
          <div className='flex overflow-hidden flex-col flex-1 w-full'>
            <div className='overflow-y-auto flex-1 rounded-lg'>
              {resultVideos.length > 0 ? (
                <div className='grid grid-cols-1 gap-6'>
                  {resultVideos.map((video, index) => (
                    <ResultCard
                      key={video.id}
                      type='video'
                      data={video}
                      handleDownload={handleDownload}
                      handleDelete={handleDelete}
                      videoRefs={videoRefs}
                      handleLoadedMetadata={() => {}}
                      index={index}
                      showPrompt={true}
                      maxGenerationMinutes={10}
                    />
                  ))}
                </div>
              ) : (
                <div className='flex flex-col justify-center items-center h-64 text-gray-400 rounded-lg border-2 border-gray-200 border-dashed'>
                  <MdOutlineAnimation
                    size={48}
                    className='mb-4 text-primary-300'
                  />
                  <p className='text-center'>{t('ui.emptyState')}</p>
                </div>
              )}
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}
