import React, { useState, useRef, useEffect, memo } from 'react';
import { useAtom, useAtomValue } from 'jotai';
import { authAtom, profileAtom } from 'state';
import { AnimeStyle, VipAnimeStyles } from '../../../api/tools/_constants';
import {
  filterValidImage,
  mergeMediaData,
  deleteMediaData,
  dispatchGenerated,
  GenerationStatus,
  genId,
  ImageData,
} from './utils';
import { getImageSize, toastWarn, urlToBase64 } from '@/utils/index';
import toast from 'react-hot-toast';
import {
  Button,
  Card,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  Chip,
  Select,
  SelectItem,
  Tooltip,
} from '@nextui-org/react';
import UploadFile from '../UploadFile';
import { MdOutlineAnimation } from 'react-icons/md';
import { ResultCard } from './ResultCard';
import { FaDownload, FaCrown } from 'react-icons/fa6';
import {
  ToolsModel,
  ToolsModelType,
  calculatePhotoToAnimeCost,
} from '../../../api/tools/_zaps';
import { BsInfoCircleFill } from 'react-icons/bs';
import { BiSolidZap } from 'react-icons/bi';
import { useTranslation } from 'react-i18next';
import { useProcessInitialImage } from 'hooks/useProcessInitialImage';
import { ERROR_CODES } from '../../../api/_constants';
import { useOpenModal } from 'hooks/useOpenModal';
import cn from 'classnames';

type ImageApiParams = {
  method: 'getImages' | 'generateImage' | 'deleteImage';
  tool: string;
  [key: string]: any; // 允许其他可选参数
};

interface ImagesResponse {
  data: ImageData[];
}

const styleData = [
  {
    id: AnimeStyle.ANIME,
    name: 'Anime',
    key: 'styles.anime',
    image: '/images/styles/anime.webp',
    description:
      'Classic Japanese animation style with vibrant colors and expressive features',
  },
  {
    id: AnimeStyle.GHIBLI_ANIME,
    name: 'Ghibli Anime',
    key: 'styles.ghibliAnime',
    image: '/images/styles/studio_ghibli_anime.webp',
    description:
      'Whimsical and detailed animation style inspired by Studio Ghibli films',
  },
  {
    id: AnimeStyle.KOREAN_MANHWA,
    name: 'Korean Manhwa',
    key: 'styles.koreanManhwa',
    image: '/images/styles/korean_manhwa.webp',
    description:
      'Colorful webtoon style with flowing panels and detailed character designs',
  },
  {
    id: AnimeStyle.CARTOON,
    name: 'Cartoon',
    key: 'styles.cartoon',
    image: '/images/styles/cartoon.webp',
    description:
      'Simplified, exaggerated style with bold outlines and flat colors',
  },
  {
    id: AnimeStyle.PLUSHIE,
    name: 'Plushie',
    key: 'styles.plushie',
    image: '/images/styles/plushie.webp',
    description: 'Plushie style with soft, fluffy texture',
  },
  {
    id: AnimeStyle.BADGE,
    name: 'Badge',
    key: 'styles.badge',
    image: '/images/styles/badge.jpeg',
    description: 'Badge style with glossy finish and metal backing',
  },
  {
    id: AnimeStyle.STANDEE,
    name: 'Standee',
    key: 'styles.standee',
    image: '/images/styles/standee.jpg',
    description: 'Standee style with clear acrylic edges and glossy finish',
  },
  {
    id: AnimeStyle.BODY_PILLOW,
    name: 'Body Pillow',
    key: 'styles.bodyPillow',
    image: '/images/styles/body_pillow.webp',
    description: 'Body Pillow style with soft, fluffy texture',
  },
  {
    id: AnimeStyle.COSPLAY,
    name: 'Cosplay',
    key: 'styles.cosplay',
    image: '/images/styles/cosplay.jpeg',
    description: 'Cosplay style with realistic human body and clothing',
  },
  {
    id: AnimeStyle.MANGA,
    name: 'Manga',
    key: 'styles.manga',
    image: '/images/styles/manga.webp',
    description:
      'Black and white Japanese comic style with distinctive panel layouts and expressive line work',
  },
  {
    id: AnimeStyle.INK_WASH,
    name: 'Ink Wash Painting',
    key: 'styles.inkWash',
    image: '/images/styles/ink.webp',
    description:
      'Traditional East Asian painting style with flowing ink gradients',
  },
  {
    id: AnimeStyle.STICKER,
    name: 'Sticker',
    key: 'styles.sticker',
    image: '/images/styles/sticker.webp',
    description:
      'Flat, vibrant art style with bold outlines perfect for stickers and decals',
  },
  {
    id: AnimeStyle.CHIBI,
    name: 'Chibi Stickers',
    key: 'styles.chibi',
    image: '/images/styles/chibi.webp',
    description: 'Small, chibi character in different moods',
  },
  {
    id: AnimeStyle.CHARACTER_SHEET,
    name: 'Character Sheet',
    key: 'styles.characterSheet',
    image: '/images/styles/character_sheet.webp',
    description: 'Character sheet style with bold outlines and flat colors',
  },
  {
    id: AnimeStyle.SPRITE_SHEET,
    name: 'Sprite Sheet',
    key: 'styles.spriteSheet',
    image: '/images/styles/sprite_sheet.webp',
    description: 'Sprite sheet in pixel art style',
  },
  {
    id: AnimeStyle.SIMPSONS,
    name: 'Simpsons',
    key: 'styles.simpsons',
    image: '/images/styles/simpsons.webp',
    description: 'The Simpsons style with bold outlines and flat colors',
  },
  {
    id: AnimeStyle.RICK_AND_MORTY,
    name: 'Rick and Morty',
    key: 'styles.rickAndMorty',
    image: '/images/styles/rick_and_morty.webp',
    description:
      'Distinctive style from the popular adult animated sci-fi series',
  },
  {
    id: AnimeStyle.SOUTH_PARK,
    name: 'South Park',
    key: 'styles.southPark',
    image: '/images/styles/south_park.webp',
    description:
      'Simple, cut-out paper style animation with basic shapes and bold colors',
  },
  {
    id: AnimeStyle.NARUTO,
    name: 'Naruto',
    key: 'styles.naruto',
    image: '/images/styles/naruto.webp',
    description: 'Distinctive style from the popular anime series Naruto',
  },
  {
    id: AnimeStyle.ONE_PIECE,
    name: 'One Piece',
    key: 'styles.onePiece',
    image: '/images/styles/one_piece.webp',
    description: 'Distinctive style from the popular anime series One Piece',
  },
  {
    id: AnimeStyle.MY_LITTLE_PONY,
    name: 'My Little Pony',
    key: 'styles.myLittlePony',
    image: '/images/styles/my_little_pony.webp',
    description:
      'Colorful and magical style inspired by the My Little Pony series, featuring vibrant characters and whimsical settings',
  },
  {
    id: AnimeStyle.WATERCOLOR,
    name: 'Watercolor',
    key: 'styles.watercolor',
    image: '/images/styles/watercolor.webp',
    description:
      'Soft, translucent colors with gentle blending and artistic brush strokes',
  },
  {
    id: AnimeStyle.ACTION_FIGURE,
    name: 'Action Figure',
    key: 'styles.actionFigure',
    image: '/images/styles/action_figure.webp',
    description:
      'Stylized 3D toy-like appearance with glossy finish and articulated features',
  },
  {
    id: AnimeStyle.FIGURE_IN_BOX,
    name: 'Figure Box',
    key: 'styles.figureInBox',
    image: '/images/styles/figure_in_box.webp',
    description: 'Collectible figure style presented in packaging display box',
  },
  {
    id: AnimeStyle.DOLL_BOX,
    name: 'Figure Box 2',
    key: 'styles.dollBox',
    image: '/images/styles/action_box.webp',
    description:
      'Stylized 3D toy-like appearance with glossy finish and articulated features',
  },
  {
    id: AnimeStyle.BARBIE_DOLL,
    name: 'Barbie Doll',
    key: 'styles.barbieDoll',
    image: '/images/styles/barbie_doll.webp',
    description: 'Barbie doll style with pastel colors and retro aesthetics',
  },
  {
    id: AnimeStyle.LINE_ART,
    name: 'Line Art',
    key: 'styles.lineArt',
    image: '/images/styles/lineart.webp',
    description: 'Clean, monochromatic outlines with minimal or no fill colors',
  },
  {
    id: AnimeStyle.ORIGAMI_PAPER_ART,
    name: 'Origami Paper Art',
    key: 'styles.origamiPaperArt',
    image: '/images/styles/origami_paper_art.webp',
    description: 'Folded paper aesthetic with geometric shapes and clean edges',
  },
  {
    id: AnimeStyle.LEGO,
    name: 'Lego',
    key: 'styles.lego',
    image: '/images/styles/lego.webp',
    description:
      'Blocky, plastic brick aesthetic with signature Lego minifigure styling',
  },
  {
    id: AnimeStyle.LOW_POLY,
    name: 'Low Poly',
    key: 'styles.lowPoly',
    image: '/images/styles/low_poly.webp',
    description: 'Low-poly, blocky style with geometric shapes and clean edges',
  },
  {
    id: AnimeStyle.CLAY,
    name: 'Claymation',
    key: 'styles.clay',
    image: '/images/styles/clay.webp',
    description:
      'Textured, handcrafted appearance similar to claymation or sculpted figures',
  },
  {
    id: AnimeStyle.PIXEL_ART,
    name: 'Pixel Art',
    key: 'styles.pixelArt',
    image: '/images/styles/minecraft.webp',
    description:
      'Retro digital art style with visible pixels and limited color palettes',
  },
  {
    id: AnimeStyle.VAPORWAVE,
    name: 'Vaporwave',
    key: 'styles.vaporwave',
    image: '/images/styles/vaporwave.webp',
    description: 'Vaporwave style with pastel colors and retro aesthetics',
  },
  {
    id: AnimeStyle.CYBERPUNK,
    name: 'Cyberpunk',
    key: 'styles.cyberpunk',
    image: '/images/styles/cyberpunk-photo.webp',
    description:
      'Futuristic cyberpunk style with neon-lit environments and dark atmospheric mood',
  },
];

const imagesAPI = async (params: ImageApiParams): Promise<ImagesResponse> => {
  const response = await fetch('/api/tools/image-generation', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(params),
  });

  if (!response.ok) {
    throw new Error(`API request failed: ${response.status}`);
  }

  const data = await response.json();
  return data;
};

const MAX_WIDTH = 4096;
const MAX_HEIGHT = 4096;

const photoToAnimeAPI = async ({
  inputImage,
  style,
  model,
}: {
  inputImage: string;
  style?: AnimeStyle;
  model?: ToolsModelType;
}): Promise<{ output: string; error?: string; error_code?: number }> => {
  const imageSize = await getImageSize(inputImage);
  const aspectRatio = imageSize.width / (imageSize.height || 1);
  let width = imageSize.width;
  let height = imageSize.height;
  if (width > MAX_WIDTH) {
    width = MAX_WIDTH;
    height = width / aspectRatio;
  }
  if (height > MAX_HEIGHT) {
    height = MAX_HEIGHT;
    width = height * aspectRatio;
  }

  const params: { [key: string]: any } = {
    image: inputImage,
    style,
    width,
    height,
    model,
  };

  const response = await fetch('/api/tools/photo-to-anime', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(params),
  });

  if (!response.ok) {
    return { error: 'Generate image failed', output: '' };
  }

  const data = await response.json();
  if (data.error === '429') {
    return { error: 'Resource has been exhausted', output: '' };
  }
  if (data.output) {
    return { output: data.output, error: '' };
  }
  return data;
};

const getId = genId();

function PhotoToAnimeConvert({
  animeStyle: animeStylePreset,
  exampleImageUrl = '/images/examples/photo-to-anime/girl_anime.webp',
}: {
  animeStyle?: AnimeStyle;
  exampleImageUrl?: string;
}) {
  const { t } = useTranslation('photo-to-anime');

  const { inputImage, setInputImage } = useProcessInitialImage();

  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [imageToDelete, setImageToDelete] = useState<number | null>(null);

  const videoRefs = useRef<(HTMLVideoElement | null)[]>([]);

  const [loading, setLoading] = useState(false);
  const [profile, setProfile] = useAtom(profileAtom);
  const [isVip, setIsVip] = useState(false);
  const [animeStyle, setAnimeStyle] = useState<AnimeStyle>(
    animeStylePreset || AnimeStyle.ANIME,
  );

  // 根据当前选中的 style 获取对应的封面图片
  const getCurrentStyleImage = () => {
    const currentStyle = styleData.find(d => d.id === animeStyle);
    return currentStyle?.image || exampleImageUrl;
  };

  const [resultImages, setResultImages] = useState<ImageData[]>([
    {
      id: -1,
      url_path: getCurrentStyleImage(),
      prompt: t('common:exampleResult'),
    },
  ]);

  const [animeName, setAnimeName] = useState<string>('styles.anime');
  const styleContainerRef = useRef<HTMLDivElement>(null);

  // Add model selection state
  const [selectedModel, setSelectedModel] = useState<ToolsModelType>(
    ToolsModel.BASIC,
  );
  const [cost, setCost] = useState(calculatePhotoToAnimeCost(selectedModel));
  const isAuth = useAtomValue(authAtom);
  const { submit: openModal } = useOpenModal();

  // 监听 animeStylePreset 的变化，确保路由切换时能正确更新
  useEffect(() => {
    if (animeStylePreset) {
      setAnimeStyle(animeStylePreset);
    }
  }, [animeStylePreset]);

  useEffect(() => {
    const style = styleData.find(d => d.id === animeStyle);
    if (style) {
      setAnimeName(style.key);
      // Scroll the selected style into view
      const selectedElement = styleContainerRef.current?.querySelector(
        `[data-style-id="${animeStyle}"]`,
      );
      if (selectedElement) {
        selectedElement.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest',
        });
      }
    }
  }, [animeStyle]);

  // 监听 animeStyle 变化，更新 example result 的图片
  useEffect(() => {
    const currentStyleImage = getCurrentStyleImage();
    setResultImages(prevImages => {
      // 只更新 example result (id: -1)，保留其他真实结果
      const updatedImages = prevImages.map(img => 
        img.id === -1 
          ? { ...img, url_path: currentStyleImage }
          : img
      );
      return updatedImages;
    });
  }, [animeStyle]);

  // Add cost calculation based on model
  useEffect(() => {
    setCost(calculatePhotoToAnimeCost(selectedModel));
  }, [selectedModel]);

  useEffect(() => {
    if (profile?.plan_codes?.length > 0) {
      setIsVip(true);
    } else {
      setIsVip(false);
    }
  }, [profile.plan_codes]);

  const fetchImages = async (needMerge?: boolean) => {
    try {
      const response = await imagesAPI({
        method: 'getImages',
        tool: 'photo_to_anime',
      });
      if (response.data) {
        const subs = await filterValidImage(response.data);
        if (!subs.length) {
          return;
        }
        if (!needMerge) {
          setResultImages(resultImages => [
            ...resultImages.filter(d => d.id !== -1),
            ...subs,
          ]);
          return;
        }
        setResultImages(resultImages => {
          const oldData = mergeMediaData(
            resultImages,
            subs,
            'url_path',
          ) as ImageData[];
          return [...oldData];
        });
      }
    } catch (error: any) {
      console.error('fetchImages failed', error);
      if (error.message.indexOf('401')) {
        return;
      }
      toast.error(t('toast.fetchFailed'), {
        position: 'top-center',
        style: {
          background: '#555',
          color: '#fff',
        },
      });
    }
  };

  useEffect(() => {
    isAuth && fetchImages();
  }, [isAuth]);

  const handleDeleteClick = (imageId: number) => {
    if (typeof imageId === 'number' && imageId < 0) {
      setResultImages(resultImages =>
        resultImages.filter(d => d.id !== imageId),
      );
      return;
    }
    setImageToDelete(imageId);
    setDeleteModalOpen(true);
  };

  // 确认删除时使用保存的 ID
  const handleConfirmDelete = async () => {
    setDeleteModalOpen(false);
    if (imageToDelete) {
      try {
        const response = await imagesAPI({
          method: 'deleteImage',
          tool: 'photo_to_anime',
          id: imageToDelete,
        });
        if (response.data && response.data.length > 0) {
          console.log('deleteImage success');
          deleteMediaData(setResultImages, imageToDelete);
          toast.success(t('toast.deleteSuccess'), {
            position: 'top-center',
            style: {
              background: '#555',
              color: '#fff',
            },
          });
        } else {
          console.log('deleteVideo failed');
          toast.error(t('toast.deleteFailed'), {
            position: 'top-center',
            style: {
              background: '#555',
              color: '#fff',
            },
          });
        }
        setImageToDelete(null); // 清除保存的 ID
      } catch (error) {
        console.error('deleteImage failed', error);
        toast.error(t('toast.deleteFailed'), {
          position: 'top-center',
          style: {
            background: '#555',
            color: '#fff',
          },
        });
      }
    }
  };

  const handleDownload = async (imageUrl: string) => {
    try {
      const response = await fetch(imageUrl);
      if (!response.ok) {
        throw new Error('Network response was not ok');
      }
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `result-${Date.now()}.png`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url); // 释放 URL 对象
    } catch (error) {
      console.error('Download failed:', error);
      toast.error(t('toast.downloadFailed'), {
        position: 'top-center',
        style: {
          background: '#555',
          color: '#fff',
        },
      });
    }
  };

  const handleChange = async (url: string) => {
    // 如果是用户上传的文件（blob URL），则需要转换为base64
    if (url.startsWith('blob:')) {
      const base64Data = await urlToBase64(url);
      setInputImage(base64Data, false); // 标记为用户上传
    } else {
      // 如果是从其他工具传来的URL，直接使用
      setInputImage(url, false); // 标记为用户上传
    }
  };

  const refreshList = async (id: number, resultUrl: string) => {
    await dispatchGenerated(id);
    setResultImages(resultVideos => {
      const index = resultVideos.findIndex(d => d.id === id);
      resultVideos[index] = {
        id,
        url_path: resultUrl,
      };
      if (index > -1) {
        return [...resultVideos];
      }
      return resultVideos;
    });
  };

  const handleSubmit = async () => {
    if (profile.credit < cost) {
      // toast.error(t('toast.purchaseZaps'), {
      //   position: "top-center",
      //   style: {
      //     background: "#555",
      //     color: "#fff",
      //   },
      // });
      openModal('pricing');
      return;
    }

    setLoading(true);
    try {
      const id = getId();
      setResultImages([
        {
          id,
          url_path: '',
          status: GenerationStatus.GENERATING,
        },
        ...resultImages.filter(d => d.id !== -1),
      ]);
      setLoading(false);

      // 调用 API 处理图片
      const startTime = Date.now();
      const result = await photoToAnimeAPI({
        inputImage,
        style: animeStyle,
        model: selectedModel,
      });

      if (result.error) {
        if (result.error_code === ERROR_CODES.RATE_LIMIT_EXCEEDED) {
          toastWarn(t('toast:common.rateLimitExceeded'));
        } else {
          toast.error(result.error, {
            position: 'top-center',
            style: {
              background: '#555',
              color: '#fff',
            },
          });
        }
        setResultImages(resultVideos => {
          const index = resultVideos.findIndex(d => d.id === id);
          if (index > -1) {
            resultVideos.splice(index, 1);
            return [...resultVideos];
          }
          return resultVideos;
        });
        return;
      }

      setProfile(profile => ({
        ...profile,
        credit: profile.credit - cost,
      }));

      const resultUrl = result.output;
      await refreshList(id, resultUrl);
      console.log(
        `API 处理时间: ${((Date.now() - startTime) / 1000).toFixed(2)}秒`,
      );

      // 上传视频到 Supabase
      const response = await imagesAPI({
        method: 'generateImage',
        tool: 'photo_to_anime',
        url_path: resultUrl,
        id: null,
        model: selectedModel === ToolsModel.ADVANCED ? 'Advanced' : 'Basic',
      });

      if (response.data && response.data.length > 0) {
        console.log('generateVideo success');
        const data = response.data[0];
        setResultImages(resultImages => {
          const index = resultImages.findIndex(d => d.id === id);
          if (index > -1) {
            resultImages[index] = {
              id: data.id,
              url_path: data.url_path,
            };
            return [...resultImages];
          }
          return resultImages;
        });

        toast.success(t('toast.conversionDone'), {
          position: 'top-center',
          style: {
            background: '#555',
            color: '#fff',
          },
        });
      } else {
        toast.error(t('toast.conversionFailed'), {
          position: 'top-center',
          style: {
            background: '#555',
            color: '#fff',
          },
        });
      }
    } catch (error) {
      console.error('frame interpolation failed:', error);
      if ((error as any).message === 'no zaps') {
        // toast.error(t('toast.noZaps'), {
        //   position: "top-center",
        //   style: {
        //     background: "#555",
        //     color: "#fff",
        //   },
        // });
        openModal('pricing');
        return;
      }
      toast.error(t('toast.generateFailed'), {
        position: 'top-center',
        style: {
          background: '#555',
          color: '#fff',
        },
      });
    } finally {
      setLoading(false);
    }
  };
  return (
    <div className='grid grid-cols-12 gap-4 md:gap-6 mt-4'>
      {/* Left input area */}
      <div className='col-span-12 md:col-span-6 lg:col-span-5'>
        <Card className='p-4 md:p-6 transition-all duration-300 shadow-md md:shadow-2xl border-1.5 border-primary-200'>
          <div className='flex justify-between items-center mb-3 md:mb-6'>
            <h2 className='text-lg font-bold text-primary-800 md:text-2xl'>
              {t('button.convert', { style: t(animeName) })}
            </h2>
            <Tooltip content={t('infoTooltip')} color='primary'>
              <span>
                <BsInfoCircleFill className='text-primary-500' />
              </span>
            </Tooltip>
          </div>

          {/* {isFromTool && hasMedia && (
            <div className="p-2 mb-4 text-sm text-blue-700 bg-blue-50 rounded-lg">
              <p>{tc('from_other_tools', {
                input_type: tc('input_types.image'),
                tools: tc(`tools.${mediaItem.source}`),
              })}</p>
            </div>
          )} */}

          <div className='mb-4 md:mb-6'>
            <UploadFile
              onChange={handleChange}
              accept='.png,.jpg,.jpeg,.webp'
              initialImage={inputImage}
            />
          </div>

          {/* Replace the Select component with a grid of style options */}
          <div className='mb-4 md:mb-6'>
            <label className='block mb-2 text-sm font-medium text-gray-700 md:text-base'>
              {t('styleSelection.label')}
            </label>
            <div
              ref={styleContainerRef}
              className='grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3 max-h-[300px] overflow-y-auto p-1'>
              {styleData.map(style => {
                const isVipStyle = VipAnimeStyles.includes(style.id as any);
                const isDisabled = isVipStyle && !isVip;

                return (
                  <div
                    key={style.id}
                    data-style-id={style.id}
                    role='button'
                    tabIndex={isDisabled ? -1 : 0}
                    onKeyDown={e => {
                      if (e.key === 'Enter' && !isDisabled) {
                        setAnimeStyle(style.id);
                      }
                    }}
                    onClick={() => {
                      if (isDisabled) {
                        openModal('pricing');
                        return;
                      }
                      setAnimeStyle(style.id);
                    }}
                    className={cn(
                      'cursor-pointer rounded-lg p-2 transition-all duration-200 relative',
                      {
                        'border-2 border-primary-600 bg-primary-50 shadow-md':
                          animeStyle === style.id && !isDisabled,
                        'border border-gray-200 hover:border-primary-300 hover:bg-primary-50/30':
                          animeStyle !== style.id && !isDisabled,
                        'opacity-50 !cursor-not-allowed border border-gray-200':
                          isDisabled,
                      },
                    )}>
                    {isVipStyle && (
                      <div className='absolute flex-center top-0 right-0 z-10 bg-primary/50 p-0.5 px-1 rounded-bl-lg rounded-tr-lg'>
                        <FaCrown className='text-yellow-500 w-4 h-4 drop-shadow-md' />
                      </div>
                    )}
                    <div className='overflow-hidden mb-2 rounded-md aspect-square'>
                      <img
                        src={style.image}
                        alt={t(style.key)}
                        className='object-cover object-top w-full h-full'
                      />
                    </div>
                    <p className='text-xs font-medium text-center truncate md:text-sm'>
                      {t(style.key)}
                    </p>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Add Model selection */}
          <div className='mb-4 md:mb-6'>
            <p className='block mb-1 font-medium text-gray-700 text-sm md:text-base'>
              {t('line-art-colorization:input.model.label', {
                defaultValue: 'Model',
              })}
            </p>
            <div className='mb-2 text-xs text-gray-500 md:text-sm'>
              <Chip size='sm' color='primary' className='mr-0.5'>
                {t('common.new', { defaultValue: 'New' })}
              </Chip>{' '}
              {t('input.model.description', {
                defaultValue:
                  'Advanced model provides better quality but costs more zaps',
              })}
            </div>
            <Select
              placeholder={t('line-art-colorization:input.model.placeholder', {
                defaultValue: 'Select model',
              })}
              defaultSelectedKeys={[`${ToolsModel.BASIC}`]}
              className='w-full'
              onSelectionChange={keys => {
                const selected = +Array.from(keys)[0] as ToolsModelType;
                setSelectedModel(selected);
              }}
              aria-label={t('line-art-colorization:input.model.ariaLabel', {
                defaultValue: 'Select model',
              })}
              classNames={{
                trigger:
                  'border-gray-300 hover:border-primary-500 transition-all duration-300',
              }}>
              <SelectItem key={ToolsModel.BASIC} value={ToolsModel.BASIC}>
                {t('line-art-colorization:input.model.options.basic', {
                  defaultValue: 'Basic',
                })}
              </SelectItem>
              <SelectItem key={ToolsModel.ADVANCED} value={ToolsModel.ADVANCED}>
                {t('line-art-colorization:input.model.options.advanced', {
                  defaultValue: 'Advanced',
                })}
              </SelectItem>
            </Select>
          </div>

          {/* Submit button */}
          <Button
            isLoading={loading}
            color='primary'
            className='w-full transform transition-all duration-300 hover:scale-[1.02] bg-gradient-to-r from-primary-600 to-purple-600 text-white shadow-md hover:shadow-lg'
            size='lg'
            onClick={handleSubmit}
            isDisabled={!inputImage}>
            <span className='mr-2'>
              {t('button.convert', { style: t(animeName) })}
            </span>
            <Chip
              startContent={
                <BiSolidZap className='mr-0 w-4 h-4 text-orange-400' />
              }
              variant='bordered'
              color={'primary'}
              size='sm'
              className='bg-white'>
              {t('button.zaps', { cost, credit: profile.credit })}
            </Chip>
          </Button>
        </Card>
      </div>

      {/* Right output area */}
      <div className='col-span-12 md:col-span-6 lg:col-span-7'>
        <Card className='p-4 md:p-6 h-full shadow-md md:shadow-2xl border-1.5 border-primary-50'>
          <h2 className='flex items-center mb-4 md:mb-6 text-lg font-bold text-primary-800 md:text-2xl'>
            <FaDownload className='mr-2 text-primary-600' />{' '}
            {t('results.title')}
          </h2>
          <div className='w-full max-h-[calc(847px-7rem)] flex flex-col'>
            <div className='overflow-y-auto flex-1 rounded-lg'>
              {resultImages?.length > 0 ? (
                <div className='grid grid-cols-1 gap-6'>
                  {resultImages.map((video, index) => (
                    <ResultCard
                      key={video.id}
                      data={video}
                      handleDownload={handleDownload}
                      handleDelete={handleDeleteClick}
                      index={index}
                      videoRefs={videoRefs}
                      type='image'
                      showPrompt={!!video.prompt}
                    />
                  ))}
                </div>
              ) : (
                <div className='flex flex-col justify-center items-center h-64 text-gray-400 rounded-lg border-2 border-gray-200 border-dashed'>
                  <MdOutlineAnimation
                    size={48}
                    className='mb-4 text-primary-300'
                  />
                  <p className='text-center'>{t('results.empty')}</p>
                </div>
              )}
            </div>
          </div>
        </Card>
      </div>
      <Modal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        classNames={{
          backdrop: 'bg-black/50 backdrop-blur-sm',
          base: 'border border-primary-200',
        }}>
        <ModalContent>
          <ModalHeader className='text-primary-800'>
            {t('deleteModal.title')}
          </ModalHeader>
          <ModalBody>
            <p>{t('deleteModal.message')}</p>
          </ModalBody>
          <ModalFooter>
            <Button
              variant='light'
              onPress={() => setDeleteModalOpen(false)}
              className='transition-all duration-300 hover:bg-gray-100'>
              {t('deleteModal.cancel')}
            </Button>
            <Button
              color='danger'
              onPress={handleConfirmDelete}
              className='bg-gradient-to-r from-red-500 to-pink-500 transition-all duration-300 hover:from-red-600 hover:to-pink-600'>
              {t('deleteModal.delete')}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
}

export default memo(PhotoToAnimeConvert);
