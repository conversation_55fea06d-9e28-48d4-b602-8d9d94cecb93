/*
 * @Author: kavinbj <EMAIL>
 * @Date: 2024-11-29 20:41:34
 * @LastEditors: kavinbj <EMAIL>
 * @LastEditTime: 2024-12-11 19:49:29
 * @FilePath: /ComicEditor/src/Components/ToolsPage/index.ts
 * @Description:
 */
export { default as ToolsPage } from "./ToolsPage";
export { default as ToolsHeader } from "./ToolsHeader";
export { default as ToolsInbetweenPage } from "./ToolsInbetweenPage";
export { default as ToolsVideoUpscalePage } from "./ToolsVideoUpscalePage";
export { default as ToolsLineArtColorizePage } from "./ToolsLineArtColorizePage";
export { default as ToolsVideoInterpolationPage } from "./ToolsVideoInterpolationPage";
export { default as ToolsImageUpscalePage } from "./ToolsImageUpscalePage";
export { default as ToolsImageToVideoPage } from "./ToolsImageToVideoPage";
export { default as ToolsRelightingPage } from "./ToolsRelightingPage";
export { default as ToolsPhotoToAnimePage } from "./ToolsPhotoToAnimePage";
export { default as ToolsLayerSplitterPage } from "./ToolsLayerSplitterPage";
export { default as ToolsSketchSimplifierPage } from "./ToolsSketchSimplifierPage";
export { default as FilterPhotoToAnimePage } from "./filters/FilterPhotoToAnime";
export { default as FilterPhotoToStudioGhibliPage } from "./filters/FilterPhotoToStudioGhibli";
export { default as FilterStudioGhibliGeneratorPage } from "./filters/FilterStudioGhibliGenerator";
export { default as FilterActionFigure } from "./filters/FilterActionFigure";
export { default as FilterCharacterSheet } from "./filters/FilterCharacterSheet";
export { default as FilterBarbie } from "./filters/FilterBarbie";
export { default as ToolsFramePackPage } from "./ToolsFramePackPage";
export { default as ToolsViduPage } from "./ToolsViduPage";
export { default as ToolsMagiPage } from "./ToolsMagiPage";
export { default as ToolsAniSoraPage } from "./ToolsAniSoraPage";
export { default as ToolsTalkingHeadPage } from "./ToolsTalkingHeadPage";
export { default as ToolsVeo3Page } from "./ToolsVeo3Page";
export { default as ToolsMidjourneyPage } from './ToolsMidjourneyPage';
export { default as ToolsMareyPage } from './ToolsMareyPage';
export { default as ToolsMoonvalleyPage } from './ToolsMoonvalleyPage';
export { default as ToolsVideoToVideoPage } from './ToolsVideoToVideoPage';
export { VideoToolsTabs } from "./VideoToolsTabs";
