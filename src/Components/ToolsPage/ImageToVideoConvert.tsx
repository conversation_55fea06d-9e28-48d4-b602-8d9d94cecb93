import React, { memo, useEffect, useRef, useState } from 'react';
import { FaDownload } from 'react-icons/fa';
import { BiSolidZap } from 'react-icons/bi';
import { BsInfoCircleFill } from 'react-icons/bs';
import { MdOutlineAnimation } from 'react-icons/md';
import toast from 'react-hot-toast';
import { v4 as uuidv4 } from 'uuid';
import {
  <PERSON>ton,
  Card,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  Chip,
  Select,
  SelectItem,
  Tooltip,
} from '@nextui-org/react';
import { useTranslation } from 'react-i18next';
import UploadFile from '../UploadFile';
import {
  filterValidVideo,
  mergeMediaData,
  deleteMediaData,
  dispatchGenerated,
  GenerationStatus,
  genId,
} from './utils';
import { ResultCard } from './ResultCard';
import { VideoData } from './utils';
import {
  calculateImageToAnimationCost,
  IMAGE_TO_ANIMATION_FRAME_PACK,
  IMAGE_TO_ANIMATION_KLING_5,
  IMAGE_TO_ANIMATION_KLING_V2_5,
  IMAGE_TO_ANIMATION_MAGI_1,
  IMAGE_TO_ANIMATION_MINIMAX,
  IMAGE_TO_ANIMATION_MJ_VIDEO,
  IMAGE_TO_ANIMATION_RAY,
  IMAGE_TO_ANIMATION_RAY_FLASH_V2_5,
  IMAGE_TO_ANIMATION_VEO,
  IMAGE_TO_ANIMATION_VIDU,
  IMAGE_TO_ANIMATION_VIDU_Q1,
  IMAGE_TO_ANIMATION_WAN,
  IMAGE_TO_ANIMATION_WAN_PRO,
  ImageToVideoModel,
} from '../../../api/tools/_zaps';
import { authAtom, profileAtom } from 'state';
import { useAtom, useAtomValue } from 'jotai';
import { toastError, toastWarn, redrawImageToBase64 } from '@/utils/index';
import KlingIcon from '../../../public/images/icons/kling.svg';
import LumaRayIcon from '../../../public/images/icons/ray.svg';
import MinimaxIcon from '../../../public/images/icons/hailuo.svg';
import VeoIcon from '../../../public/images/icons/veo.svg';
import WanIcon from '../../../public/images/icons/wavespeed.jpg';
import KoIcon from '../../../public/images/favicon.webp';
import ViduIcon from '../../../public/images/icons/vidu.png';
import AnisoraIcon from '../../../public/images/icons/anisora.webp';
import MjIcon from '../../../public/images/icons/midjourney.png';
import { uploadFile } from '@/utils/index';
import cn from 'classnames';
import { useRouter } from 'next/router';
import { useProcessInitialImage } from 'hooks/useProcessInitialImage';
import { ERROR_CODES } from '../../../api/_constants';
import { VideoToolsTabs } from './VideoToolsTabs';
import { useOpenModal } from 'hooks/useOpenModal';
import { useVideos } from 'hooks/useVideos';
import { ModelIds } from '../../../api/_constants';
import { isNil } from 'lodash-es';
import KoSelect, { SelectItem as KoSelectItem } from '../KoSelect';

type VideoApiParams = {
  method: 'getVideos' | 'generateVideo' | 'deleteVideo';
  tool: string;
  [key: string]: any;
};

interface VideosResponse {
  data: VideoData[];
}

enum VideoStyle {
  ANIME = 'anime',
  DEFAULT = 'default',
}

enum VideoResolution {
  '360p' = '360p',
  '540p' = '540p',
  '720p' = '720p',
  '1080p' = '1080p',
}

// 调用视频生成API
const videosAPI = async (params: VideoApiParams): Promise<VideosResponse> => {
  const response = await fetch('/api/tools/video-generation', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(params),
  });

  if (!response.ok) {
    throw new Error(`API request failed: ${response.status}`);
  }

  const data = await response.json();
  if (data.error) {
    toast.error(data.error);
    throw new Error(data.error);
  }
  return data;
};

// 2. 上传图片到 Supabase
const uploadImage = async (imageUrl: string): Promise<string> => {
  try {
    // 如果已经是 Supabase URL，直接返回
    // if (imageUrl.includes('supabase.co')) {
    //   return imageUrl;
    // }

    // 1. 获取 blob
    const response = await fetch(imageUrl);
    if (!response.ok) {
      throw new Error('Failed to fetch image URL');
    }
    const ext = imageUrl.match(/image\/(.+?);/)?.[1] || 'jpg';
    const blob = await response.blob();

    // 2. 创建文件
    const file = new File([blob], `${uuidv4()}.${ext}`, { type: 'image/jpeg' });
    // 3. 上传
    const imagePath = `app_media/${file.name}`;
    const url = await uploadFile(imagePath, file);
    return url;
  } catch (error) {
    console.error('Failed to upload image:', error);
    return '';
  }
};

// 3. 调用 interpolate API
const imageToVideoAPI = async ({
  inputImage,
  prompt,
  aspectRatio,
  model = ImageToVideoModel.VEO,
  duration,
  style,
  resolution,
  frames_per_second,
}: {
  inputImage: string;
  prompt?: string;
  aspectRatio?: string;
  model: ImageToVideoModel;
  duration?: '5' | '8' | '9' | '10';
  style?: VideoStyle;
  resolution?: VideoResolution;
  frames_per_second?: number;
}): Promise<{ output: string; error?: string; error_code?: number }> => {
  const params: { [key: string]: any } = {
    image: inputImage,
    prompt: prompt || '',
    aspect_ratio: aspectRatio || '16:9',
    model,
    duration,
    style: style === VideoStyle.DEFAULT ? undefined : style,
    resolution,
    frames_per_second,
  };

  // 为MAGI_1模型特殊处理参数
  if (model === ImageToVideoModel.MAGI_1) {
    // 如果duration是5，传递96帧(4秒视频)；如果是9，传递192帧(8秒视频)
    params.num_frames = duration === '5' ? 96 : 192;
    // 删除duration参数，MAGI不使用它
    delete params.duration;
  }

  try {
    const response = await fetch('/api/tools/image-to-video', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(params),
    });

    if (!response.ok) {
      return { error: 'Generate Animation failed', output: '' };
    }

    const data = await response.json();

    if (data.error === '429') {
      return { error: 'Resource has been exhausted', output: '' };
    }
    if (data.output) {
      return { output: data.output, error: '' };
    }
    return data;
  } catch (error) {
    console.error('API调用异常:', error);
    return { error: 'Generate Animation failed', output: '' };
  }
};

const getId = genId();

const convertModel = (model: ImageToVideoModel) => {
  switch (model) {
    case ImageToVideoModel.MAGI_1:
      return ModelIds.MAGI_1;
    case ImageToVideoModel.RAY_FLASH_V2:
      return ModelIds.RAY_FLASH_V2;
    case ImageToVideoModel.KLING_V2:
      return ModelIds.KLING_V2;
    case ImageToVideoModel.VIDU_Q1:
      return ModelIds.VIDU_Q1;
    case ImageToVideoModel.VEO:
      return ModelIds.VEO2;
    case ImageToVideoModel.WAN:
      return ModelIds.WAN;
    case ImageToVideoModel.WAN_PRO:
      return ModelIds.WAN_PRO;
    case ImageToVideoModel.KLING:
      return ModelIds.KLING;
    case ImageToVideoModel.PIXVERSE:
      return ModelIds.PIXVERSE;
    case ImageToVideoModel.RAY:
      return ModelIds.RAY;
    case ImageToVideoModel.ANISORA:
      return ModelIds.ANISORA;
    case ImageToVideoModel.FRAME_PACK:
      return ModelIds.FRAME_PACK;
    case ImageToVideoModel.VIDU:
      return ModelIds.VIDU;
    case ImageToVideoModel.HEDRA:
      return ModelIds.HEDRA;
    case ImageToVideoModel.VIDU_Q1:
      return ModelIds.VIDU_Q1;
    case ImageToVideoModel.MJ_VIDEO:
      return ModelIds.MJ_VIDEO;
    case ImageToVideoModel.MAGI_1:
    default:
      return model;
  }
};

function ImageToVideoConvert({
  model = ImageToVideoModel.VIDU,
  exampleVideoUrl = '/images/examples/image-animation-generator/output2.mp4',
}: {
  model?: ImageToVideoModel;
  exampleVideoUrl?: string;
}) {
  const { t } = useTranslation(['image-animation-generator', 'common']);
  const router = useRouter();
  const modelOptions: any[] = [
    // {
    //   ...(t('modelOptions.kling', { returnObjects: true }) as any),
    //   model: ImageToVideoModel.KLING,
    //   icon: KlingIcon.src,
    //   dollars: IMAGE_TO_ANIMATION_KLING_5 + '+',
    //   name: t('modelOptions.kling.name'),
    //   description: t('modelOptions.kling.description'),
    // },
    {
      ...t('modelOptions.vidu_base', { returnObjects: true }),
      model: ImageToVideoModel.VIDU,
      icon: KoIcon.src,
      dollars: IMAGE_TO_ANIMATION_VIDU,
    },
    {
      ...t('modelOptions.mj_video', { returnObjects: true }),
      model: ImageToVideoModel.MJ_VIDEO,
      icon: KoIcon.src,
      dollars: IMAGE_TO_ANIMATION_MJ_VIDEO,
    },
    {
      ...t('modelOptions.vidu', { returnObjects: true }),
      model: ImageToVideoModel.VIDU_Q1,
      icon: ViduIcon.src,
      dollars: IMAGE_TO_ANIMATION_VIDU_Q1,
    },
    {
      ...t('modelOptions.kling_v2', { returnObjects: true }),
      model: ImageToVideoModel.KLING_V2,
      icon: KlingIcon.src,
      dollars: IMAGE_TO_ANIMATION_KLING_V2_5 + '+',
      name: t('modelOptions.kling_v2.name'),
      description: t('modelOptions.kling_v2.description'),
    },
    {
      ...t('modelOptions.minimax', { returnObjects: true }),
      model: ImageToVideoModel.MINIMAX,
      icon: MinimaxIcon.src,
      dollars: IMAGE_TO_ANIMATION_MINIMAX,
    },
    {
      ...t('modelOptions.veo', { returnObjects: true }),
      model: ImageToVideoModel.VEO,
      icon: VeoIcon.src,
      dollars: IMAGE_TO_ANIMATION_VEO + '+',
    },
    // {
    //   ...t('modelOptions.ray_flash_v2', { returnObjects: true }),
    //   model: ImageToVideoModel.RAY_FLASH_V2,
    //   icon: LumaRayIcon.src,
    //   dollars: IMAGE_TO_ANIMATION_RAY_FLASH_V2_5 + '+',
    // },
    {
      ...t('modelOptions.wan', { returnObjects: true }),
      model: ImageToVideoModel.WAN,
      icon: WanIcon.src,
      dollars: IMAGE_TO_ANIMATION_WAN,
    },
    // {
    //   ...t('modelOptions.wan_pro', { returnObjects: true }),
    //   model: ImageToVideoModel.WAN_PRO,
    //   icon: WanIcon.src,
    //   dollars: IMAGE_TO_ANIMATION_WAN_PRO,
    // },
    {
      ...t('modelOptions.ray', { returnObjects: true }),
      model: ImageToVideoModel.RAY,
      icon: LumaRayIcon.src,
      dollars: IMAGE_TO_ANIMATION_RAY,
    },
    // {
    //   ...t('modelOptions.pixverse', { returnObjects: true }),
    //   model: ImageToVideoModel.PIXVERSE,
    //   icon: PixverseIcon.src,
    //   dollars: '1200+',
    // },
    {
      ...t('modelOptions.anisora', { returnObjects: true }),
      model: ImageToVideoModel.ANISORA,
      icon: AnisoraIcon.src,
      dollars: '600',
    },
    {
      ...t('modelOptions.frame_pack', { returnObjects: true }),
      model: ImageToVideoModel.FRAME_PACK,
      icon: KoIcon.src,
      dollars: IMAGE_TO_ANIMATION_FRAME_PACK,
    },
    // {
    //   ...t('modelOptions.magi_1', { returnObjects: true }),
    //   model: ImageToVideoModel.MAGI_1,
    //   icon: MagiIcon.src,
    //   dollars: "640+",
    // },
  ];

  const { inputImage, setInputImage, hasMedia, mediaItem, isFromTool } =
    useProcessInitialImage();

  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [videoToDelete, setVideoToDelete] = useState<number | null>(null);

  const [videoHeights, setVideoHeights] = useState<number[]>([]);
  const videoRefs = useRef<(HTMLVideoElement | null)[]>([]);

  // last video添加 ref
  const lastVideoRef = useRef<HTMLDivElement>(null);
  const [loading, setLoading] = useState(false);
  // const [profile, setProfile] = useAtom(profileAtom);
  // const isAuth = useAtomValue(authAtom);

  // Add these new states near the top of the component
  const [prompt, setPrompt] = useState<string>('');
  const [aspectRatio, setAspectRatio] = useState<string>('16:9');
  const [selectedModel, setSelectedModel] = useState<ImageToVideoModel>(model);
  const [selectedStyle, setSelectedStyle] = useState<VideoStyle>(
    VideoStyle.DEFAULT,
  );
  const [selectedResolution, setSelectedResolution] = useState<VideoResolution>(
    VideoResolution['720p'],
  );
  const [cost, setCost] = useState(
    calculateImageToAnimationCost(selectedModel),
  );
  const [duration, setDuration] = useState<'5' | '8' | '9' | '10'>('5');
  const [fpsValue, setFpsValue] = useState<string>('24');
  const { submit: openModal } = useOpenModal();

  const { resultVideos, setResultVideos, submitTask, profile, addTaskId } =
    useVideos('image_animation', exampleVideoUrl);

  useEffect(() => {
    setCost(
      calculateImageToAnimationCost(
        selectedModel,
        duration,
        selectedResolution,
      ),
    );
  }, [selectedModel, duration, selectedResolution]);

  useEffect(() => {
    setDuration('5');
  }, [selectedModel]);

  // Get prompt from URL query parameter
  useEffect(() => {
    if (!router.isReady) return;
    const { prompt } = router.query;
    if (prompt && typeof prompt === 'string' && prompt.trim() !== '') {
      setPrompt(prompt);
    }
  }, [router.isReady, router.query]);

  /**
   * @deprecated
   */
  const handleLoadedMetadata = (index: number) => {
    const videoElement = videoRefs.current[index];
    if (videoElement) {
      const aspectRatio = videoElement.videoWidth / videoElement.videoHeight;
      const containerWidth = videoElement.parentElement?.offsetWidth || 0;
      const newHeight = 12 + containerWidth / aspectRatio;
      setVideoHeights(prevHeights => {
        const updatedHeights = [...prevHeights];
        updatedHeights[index] = newHeight;
        return updatedHeights;
      });
    }
  };
  useEffect(() => {
    if (resultVideos.length > 0) {
      lastVideoRef.current?.scrollIntoView({ behavior: 'smooth' });
    }
  }, [resultVideos]);

  /* ******************************  点击删除视频相关    *****************************  */
  // 点击删除按钮时保存视频 ID
  const handleDeleteClick = (videoId: number) => {
    if (typeof videoId === 'number' && videoId < 0) {
      setResultVideos(resultVideos =>
        resultVideos.filter(d => d.id !== videoId),
      );
      return;
    }
    setVideoToDelete(videoId);
    setDeleteModalOpen(true);
  };

  // 确认删除时使用保存的 ID
  const handleConfirmDelete = async () => {
    setDeleteModalOpen(false);
    if (videoToDelete) {
      try {
        const response = await videosAPI({
          method: 'deleteVideo',
          tool: 'image_animation',
          id: videoToDelete,
        });
        if (response.data && response.data.length > 0) {
          console.log('deleteVideo success');
          deleteMediaData(setResultVideos, videoToDelete);
          toast.success(t('toast:imageToVideo.deleteSuccess'), {
            position: 'top-center',
            style: {
              background: '#555',
              color: '#fff',
            },
          });
        } else {
          console.log('deleteVideo failed');
          toast.error(t('toast:imageToVideo.deleteFailed'), {
            position: 'top-center',
            style: {
              background: '#555',
              color: '#fff',
            },
          });
        }
        setVideoToDelete(null); // 清除保存的 ID
      } catch (error) {
        console.error('deleteVideo failed', error);
        toast.error(t('toast:imageToVideo.deleteFailed'), {
          position: 'top-center',
          style: {
            background: '#555',
            color: '#fff',
          },
        });
      }
    }
  };

  /* ******************************  下载短视频   *****************************  */
  // 下载短视频按键
  const handleDownload = async (videoUrl: string) => {
    try {
      const response = await fetch(videoUrl);
      if (!response.ok) {
        throw new Error('Network response was not ok');
      }
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `result-${Date.now()}.mp4`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url); // 释放 URL 对象
    } catch (error) {
      console.error('Download failed:', error);
      toast.error(t('toast:imageToVideo.downloadFailed'), {
        position: 'top-center',
        style: {
          background: '#555',
          color: '#fff',
        },
      });
    }
  };

  /* ******************************   图片上传生成视频相关    *****************************  */

  const handleChange = async (url: string) => {
    // 如果是 Supabase URL，直接使用
    if (url.includes('supabase.co')) {
      setInputImage(url);
    } else {
      const base64 = await redrawImageToBase64(url);
      setInputImage(base64);
    }
  };

  const handleSubmit = async () => {
    if (!inputImage) {
      toast.error(
        t('toast:imageToVideo.noImageSelected', 'Please select an image first'),
        {
          position: 'top-center',
          style: {
            background: '#555',
            color: '#fff',
          },
        },
      );
      return;
    }

    if (profile.credit < cost) {
      openModal('pricing');
      return;
    }

    setLoading(true);
    let imageUrl = '';

    let should_delete_media = false;

    if (inputImage.includes('supabase.co')) {
      imageUrl = inputImage;
      should_delete_media = false;
    } else {
      imageUrl = await uploadImage(inputImage);
      should_delete_media = true;
    }
    if (!imageUrl) {
      toast.error(t('toast:error.uploadImageFailed'), {
        position: 'top-center',
        style: {
          background: '#555',
          color: '#fff',
        },
      });
      setLoading(false);
      return;
    }
    try {
      // 调用 API 处理图片
      const startTime = Date.now();
      const params = {
        image: imageUrl,
        prompt,
        aspect_ratio: aspectRatio,
        target_model: convertModel(selectedModel) as any,
        model: selectedModel,
        duration,
        style: selectedStyle,
        resolution: selectedResolution,
        frames_per_second:
          selectedModel === ImageToVideoModel.MAGI_1
            ? Number(fpsValue)
            : undefined,
        tool: 'image_animation',
        should_delete_media,
      };
      const taskId = await submitTask(params).catch();
      if (isNil(taskId)) {
        return;
      }

      addTaskId(taskId);
      setResultVideos([
        {
          id: taskId,
          video_url: '',
          prompt: prompt,
          status: GenerationStatus.GENERATING,
        },
        ...resultVideos.filter(video => video.id !== 1),
      ]);
      setLoading(false);
    } catch (error) {
      toast.error(t('toast:imageToVideo.generateFailed'), {
        position: 'top-center',
        style: {
          background: '#555',
          color: '#fff',
        },
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className='grid grid-cols-12 gap-6 mt-4'>
      {/* Left input area */}
      <div className='col-span-12 md:col-span-6 lg:col-span-5'>
        <Card className='p-4 md:px-6 md:pt-4 md:pb-6 transition-all duration-300 shadow-2xl border-1.5 border-primary-200'>
          <div className='flex justify-between items-center mb-6'>
            <VideoToolsTabs activeTab='image-to-animation' />
            <Tooltip
              content={t('input.tooltip')}
              color='primary'
              className='hidden md:block'>
              <span>
                <BsInfoCircleFill className='text-primary-500 hidden md:block' />
              </span>
            </Tooltip>
          </div>

          {/* {isFromTool && hasMedia && (
            <div className="p-2 mb-4 text-sm text-blue-700 bg-blue-50 rounded-lg">
              <p>{t('common:from_other_tools', {
                input_type: t('common:input_types.image'),
                tools: t(`common:tools.${mediaItem.source}`),
              })}</p>
            </div>
          )} */}

          <div className='mb-6'>
            <UploadFile
              onChange={handleChange}
              accept='.png,.jpg,.jpeg,.webp'
              initialImage={inputImage}
            />
          </div>

          {/* Prompt input */}
          <div className='mb-6'>
            <label className='block mb-2 font-bold text-gray-700 text-sm md:text-base'>
              {t('input.prompt.label')}
            </label>
            <textarea
              value={prompt}
              onChange={e => setPrompt(e.target.value)}
              placeholder={t('input.prompt.placeholder')}
              className='p-3 w-full text-base rounded-lg border border-gray-300 transition-all duration-300 resize-none focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500'
              rows={3}
            />
          </div>

          {/* Model selection */}
          <div className='mb-6'>
            <label className='block mb-2 font-bold text-gray-700 text-sm md:text-base'>
              {t('input.model.label')}
            </label>
            <KoSelect
              placeholder={t('input.model.placeholder')}
              defaultSelectedKeys={[selectedModel + '']}
              className='w-full'
              onSelectionChange={keys => {
                const selected = +Array.from(keys)[0] as ImageToVideoModel;
                setSelectedModel(selected);
              }}
              aria-label={t('input.model.ariaLabel')}
              classNames={{
                trigger:
                  'border-gray-300 hover:border-primary-500 transition-all duration-300',
                listboxWrapper: '!max-h-[520px] overflow-y-auto',
              }}
              renderValue={items => {
                const selectedItem = items[0];
                // const selectedKey = selectedItem?.key?.toString() || "";
                const selectedKey = selectedItem?.itemKey?.toString() || '';
                const model = modelOptions.find(
                  m => m.model === Number(selectedKey),
                );
                return (
                  <div className='flex gap-2 items-center'>
                    {model?.icon && (
                      <img
                        src={model.icon}
                        alt={(model as any)?.name || 'Model'}
                        className='w-6 h-6'
                      />
                    )}
                    <span>{(model as any)?.name || 'Model'}</span>
                  </div>
                );
              }}>
              {modelOptions.map((option: any) => (
                <KoSelectItem
                  key={option.model}
                  itemKey={option.model}
                  value={option.model}
                  textValue={option.name}>
                  {/* Main container for icon and content block */}
                  <div className='flex gap-2 w-full items-center'>
                    {/* Icon on the left */}
                    {option.icon && (
                      <img
                        src={option.icon}
                        alt={option.name}
                        className='flex-shrink-0 mt-1 w-6 h-6'
                      />
                    )}

                    {/* Content Area: Two rows (Name/Chips + Description) */}
                    <div className='flex flex-col flex-1 min-w-0'>
                      {/* First Row: Name and Chips */}
                      <div className='flex justify-between items-center w-full'>
                        {/* Model Name: takes available space, truncates if too long */}
                        <span className='flex-1 pr-2 min-w-0 text-sm font-medium truncate'>
                          {option.name}
                        </span>
                        {/* Chips Container: aligned to the right, chips can wrap */}
                        <div className='flex flex-wrap flex-shrink-0 gap-1 justify-end items-center'>
                          <Chip size='sm' color='primary' variant='flat'>
                            {option.timeCost}
                          </Chip>
                          <Chip size='sm' color='secondary' variant='flat'>
                            {option.fps}
                          </Chip>
                          <Chip
                            size='sm'
                            color='warning'
                            variant='flat'
                            startContent={
                              <BiSolidZap className='mr-0 w-4 h-4 text-orange-400' />
                            }>
                            {option.dollars}
                          </Chip>
                        </div>
                      </div>

                      {/* Second Row: Description (only if description exists) */}
                      {option.description && (
                        <span className='text-xs text-default-400 mt-0.5'>
                          {option.description}
                        </span>
                      )}
                    </div>
                  </div>
                </KoSelectItem>
              ))}
            </KoSelect>
          </div>

          {/* Aspect Ratio selection - only shown for specific models */}
          {selectedModel !== ImageToVideoModel.MINIMAX &&
            selectedModel !== ImageToVideoModel.VIDU &&
            selectedModel !== ImageToVideoModel.VIDU_Q1 &&
            selectedModel !== ImageToVideoModel.WAN_PRO &&
            selectedModel !== ImageToVideoModel.ANISORA &&
            selectedModel !== ImageToVideoModel.MJ_VIDEO && (
              <div className='mb-6'>
                <label className='block mb-2 font-medium text-gray-700'>
                  {t('input.aspectRatio.label')}
                </label>
                <div className='flex gap-4'>
                  <label
                    className={`flex items-center p-3 cursor-pointer border rounded-lg transition-all duration-300 hover:bg-primary-50 hover:border-primary-300 ${aspectRatio === '16:9' ? 'bg-primary-50 border-primary-300' : 'bg-white border-gray-300'}`}>
                    <input
                      type='radio'
                      name='aspectRatio'
                      value='16:9'
                      checked={aspectRatio === '16:9'}
                      onChange={e => setAspectRatio(e.target.value)}
                      className={'mr-2 accent-primary-600'}
                    />
                    <span>{t('buttons.landscape')}</span>
                  </label>
                  <label
                    className={`flex items-center p-3 cursor-pointer border rounded-lg transition-all duration-300 hover:bg-primary-50 hover:border-primary-300 ${aspectRatio === '9:16' ? 'bg-primary-50 border-primary-300' : 'bg-white border-gray-300'}`}>
                    <input
                      type='radio'
                      name='aspectRatio'
                      value='9:16'
                      checked={aspectRatio === '9:16'}
                      onChange={e => setAspectRatio(e.target.value)}
                      className='mr-2 accent-primary-600'
                    />
                    <span>{t('buttons.portrait')}</span>
                  </label>
                </div>
              </div>
            )}

          {/* Duration selection for Kling model */}
          {(selectedModel === ImageToVideoModel.KLING ||
            selectedModel === ImageToVideoModel.KLING_V2) && (
            <div className='mb-6'>
              <label className='block mb-2 font-medium text-gray-700'>
                Duration
              </label>
              <Select
                placeholder={t('input.duration.placeholder')}
                defaultSelectedKeys={['5']}
                className='w-full'
                onSelectionChange={keys => {
                  setDuration(Array.from(keys)[0] as '5' | '10');
                }}
                classNames={{
                  trigger:
                    'border-gray-300 hover:border-primary-500 transition-all duration-300',
                }}>
                <SelectItem key='5' value='5'>
                  {t('input.duration.sections', { sections: 5 })}
                </SelectItem>
                <SelectItem key='10' value='10'>
                  {t('input.duration.sections', { sections: 10 })}
                </SelectItem>
              </Select>
            </div>
          )}

          {selectedModel === ImageToVideoModel.VEO && (
            <div className='mb-6'>
              <label className='block mb-2 font-medium text-gray-700'>
                Duration
              </label>
              <Select
                placeholder={t('input.duration.placeholder')}
                defaultSelectedKeys={['5']}
                className='w-full'
                onSelectionChange={keys => {
                  setDuration(Array.from(keys)[0] as '5' | '10');
                }}
                classNames={{
                  trigger:
                    'border-gray-300 hover:border-primary-500 transition-all duration-300',
                }}>
                <SelectItem key='5' value='5'>
                  {t('input.duration.sections', { sections: 5 })}
                </SelectItem>
                <SelectItem key='6' value='6'>
                  {t('input.duration.sections', { sections: 6 })}
                </SelectItem>
                <SelectItem key='7' value='7'>
                  {t('input.duration.sections', { sections: 7 })}
                </SelectItem>
                <SelectItem key='8' value='8'>
                  {t('input.duration.sections', { sections: 8 })}
                </SelectItem>
              </Select>
            </div>
          )}

          {selectedModel === ImageToVideoModel.RAY_FLASH_V2 && (
            <div className='mb-6'>
              <label className='block mb-2 font-medium text-gray-700'>
                {t('input.duration.label')}
              </label>
              <Select
                placeholder={t('input.duration.placeholder')}
                defaultSelectedKeys={['5']}
                className='w-full'
                onSelectionChange={keys => {
                  setDuration(Array.from(keys)[0] as '5' | '9');
                }}
                classNames={{
                  trigger:
                    'border-gray-300 hover:border-primary-500 transition-all duration-300',
                }}>
                <SelectItem key='5' value='5'>
                  {t('input.duration.sections', { sections: 5 })}
                </SelectItem>
                <SelectItem key='9' value='9'>
                  {t('input.duration.sections', { sections: 9 })}
                </SelectItem>
              </Select>
            </div>
          )}
          {selectedModel === ImageToVideoModel.PIXVERSE && (
            <div className='mb-6'>
              <label className='block mb-2 font-medium text-gray-700'>
                {t('input.duration.label')}
              </label>
              <Select
                placeholder={t('input.duration.placeholder')}
                defaultSelectedKeys={['5']}
                className='w-full'
                onSelectionChange={keys => {
                  setDuration(Array.from(keys)[0] as '5' | '10');
                }}
                classNames={{
                  trigger:
                    'border-gray-300 hover:border-primary-500 transition-all duration-300',
                }}>
                <SelectItem key='5' value='5'>
                  {t('input.duration.sections', { sections: 5 })}
                </SelectItem>
                <SelectItem key='8' value='8'>
                  {t('input.duration.sections', { sections: 8 })}
                </SelectItem>
              </Select>
            </div>
          )}

          {selectedModel === ImageToVideoModel.PIXVERSE && (
            <div className='mb-6'>
              <label className='block mb-2 font-medium text-gray-700'>
                {t('input.style.label')}
              </label>
              <Select
                placeholder={t('input.style.placeholder')}
                defaultSelectedKeys={['default']}
                className='w-full'
                onSelectionChange={keys => {
                  setSelectedStyle(Array.from(keys)[0] as VideoStyle);
                }}
                classNames={{
                  trigger:
                    'border-gray-300 hover:border-primary-500 transition-all duration-300',
                }}>
                <SelectItem key={VideoStyle.DEFAULT} value={VideoStyle.DEFAULT}>
                  {t('input.style.default')}
                </SelectItem>
                <SelectItem key={VideoStyle.ANIME} value={VideoStyle.ANIME}>
                  {t('input.style.anime')}
                </SelectItem>
              </Select>
            </div>
          )}

          {selectedModel === ImageToVideoModel.PIXVERSE && (
            <div className='mb-6'>
              <label className='block mb-2 font-medium text-gray-700'>
                {t('input.resolution.label')}
              </label>
              <Select
                placeholder={t('input.resolution.placeholder')}
                defaultSelectedKeys={['720p']}
                className='w-full'
                onSelectionChange={keys => {
                  setSelectedResolution(Array.from(keys)[0] as VideoResolution);
                }}
                classNames={{
                  trigger:
                    'border-gray-300 hover:border-primary-500 transition-all duration-300',
                }}>
                <SelectItem
                  key={VideoResolution['360p']}
                  value={VideoResolution['360p']}>
                  360p
                </SelectItem>
                <SelectItem
                  key={VideoResolution['540p']}
                  value={VideoResolution['540p']}>
                  540p
                </SelectItem>
                <SelectItem
                  key={VideoResolution['720p']}
                  value={VideoResolution['720p']}>
                  720p
                </SelectItem>
                <SelectItem
                  key={VideoResolution['1080p']}
                  value={VideoResolution['1080p']}>
                  1080p
                </SelectItem>
              </Select>
            </div>
          )}

          {selectedModel === ImageToVideoModel.MAGI_1 && (
            <div className='mb-6'>
              <label className='block mb-2 font-medium text-gray-700'>
                Resolution
              </label>
              <Select
                placeholder='Select resolution'
                defaultSelectedKeys={['720p']}
                className='w-full'
                onSelectionChange={keys => {
                  setSelectedResolution(Array.from(keys)[0] as VideoResolution);
                }}
                classNames={{
                  trigger:
                    'border-gray-300 hover:border-primary-500 transition-all duration-300',
                }}>
                <SelectItem key='480p' value='480p'>
                  480p (0.5x cost)
                </SelectItem>
                <SelectItem key='720p' value='720p'>
                  720p (standard)
                </SelectItem>
              </Select>
            </div>
          )}

          {selectedModel === ImageToVideoModel.MAGI_1 && (
            <div className='mb-6'>
              <label className='block mb-2 font-medium text-gray-700'>
                {t('input.duration.label')}
              </label>
              <Select
                placeholder={t('input.duration.placeholder')}
                defaultSelectedKeys={['5']}
                className='w-full'
                onSelectionChange={keys => {
                  setDuration(Array.from(keys)[0] as '5' | '9');
                }}
                classNames={{
                  trigger:
                    'border-gray-300 hover:border-primary-500 transition-all duration-300',
                }}>
                <SelectItem key='5' value='5'>
                  4 seconds (96 frames)
                </SelectItem>
                <SelectItem key='9' value='9'>
                  8 seconds (192 frames)
                </SelectItem>
              </Select>
            </div>
          )}

          {selectedModel === ImageToVideoModel.MAGI_1 && (
            <div className='mb-6'>
              <label className='block mb-2 font-medium text-gray-700'>
                Frames Per Second
              </label>
              <Select
                placeholder='Select FPS'
                defaultSelectedKeys={['24']}
                className='w-full'
                onSelectionChange={keys => {
                  setFpsValue(Array.from(keys)[0] as string);
                }}
                classNames={{
                  trigger:
                    'border-gray-300 hover:border-primary-500 transition-all duration-300',
                }}>
                <SelectItem key='24' value='24'>
                  24 FPS (standard)
                </SelectItem>
                <SelectItem key='30' value='30'>
                  30 FPS (smoother)
                </SelectItem>
                <SelectItem key='15' value='15'>
                  15 FPS (slower)
                </SelectItem>
              </Select>
            </div>
          )}

          {/* Submit button */}
          <Button
            isLoading={loading}
            color='primary'
            className='w-full transform transition-all duration-300 hover:scale-[1.02] bg-gradient-to-r from-primary-600 to-purple-600 text-white shadow-md hover:shadow-lg'
            size='lg'
            onClick={handleSubmit}
            isDisabled={!prompt || !inputImage}>
            <span className='mr-2'>{t('buttons.generateAnimation')}</span>
            <Chip
              startContent={
                <BiSolidZap className='mr-0 w-4 h-4 text-orange-400' />
              }
              variant='bordered'
              color={'primary'}
              size='sm'
              className='bg-white'>
              -{cost}/{profile.credit}
            </Chip>
          </Button>
        </Card>
      </div>

      {/* Right output area */}
      <div className='col-span-12 md:col-span-6 lg:col-span-7'>
        <Card className='p-4 md:pb-6 md:px-6 md:pt-4 h-full shadow-2xl border-1.5 border-primary-50'>
          <h2 className='flex items-center mb-4 md:mb-6 text-xl md:text-2xl font-bold text-primary-800'>
            <FaDownload className='mr-2 text-primary-600' /> {t('output.title')}
          </h2>
          <div
            className={cn(
              'flex flex-col w-full max-h-[calc(829px-7rem)]',
              {
                '!max-h-[calc(733px-7rem)]':
                  selectedModel === ImageToVideoModel.WAN ||
                  selectedModel === ImageToVideoModel.RAY ||
                  selectedModel === ImageToVideoModel.FRAME_PACK,
              },
              {
                '!max-h-[calc(627px-7rem)]':
                  selectedModel === ImageToVideoModel.WAN_PRO ||
                  selectedModel === ImageToVideoModel.MINIMAX ||
                  selectedModel === ImageToVideoModel.VIDU ||
                  selectedModel === ImageToVideoModel.VIDU_Q1 ||
                  selectedModel === ImageToVideoModel.ANISORA ||
                  selectedModel === ImageToVideoModel.MJ_VIDEO,
              },
              {
                '!max-h-[calc(1021px-7rem)]':
                  selectedModel === ImageToVideoModel.PIXVERSE,
              },
            )}>
            <div className='overflow-y-auto flex-1 rounded-lg'>
              {resultVideos?.length > 0 ? (
                <div className='grid grid-cols-1 gap-6'>
                  {resultVideos.map((video, index) => (
                    <ResultCard
                      showPrompt
                      key={video.id}
                      data={video}
                      handleDownload={handleDownload}
                      handleDelete={handleDeleteClick}
                      handleLoadedMetadata={handleLoadedMetadata}
                      index={index}
                      videoRefs={videoRefs}
                      type='video'
                    />
                  ))}
                </div>
              ) : (
                <div className='flex flex-col justify-center items-center h-64 text-gray-400 rounded-lg border-2 border-gray-200 border-dashed'>
                  <MdOutlineAnimation
                    size={48}
                    className='mb-4 text-primary-300'
                  />
                  <p className='text-center'>{t('output.empty.line1')}</p>
                  <p className='mt-2 text-sm text-center'>
                    {t('output.empty.line2')}
                  </p>
                </div>
              )}
            </div>
          </div>
        </Card>
      </div>
      <Modal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        classNames={{
          backdrop: 'bg-black/50 backdrop-blur-sm',
          base: 'border border-primary-200',
        }}>
        <ModalContent>
          <ModalHeader className='text-primary-800'>
            {t('deleteModal.title')}
          </ModalHeader>
          <ModalBody>
            <p>{t('deleteModal.message')}</p>
          </ModalBody>
          <ModalFooter>
            <Button
              variant='light'
              onPress={() => setDeleteModalOpen(false)}
              className='transition-all duration-300 hover:bg-gray-100'>
              {t('deleteModal.cancelButton')}
            </Button>
            <Button
              color='danger'
              onPress={handleConfirmDelete}
              className='bg-gradient-to-r from-red-500 to-pink-500 transition-all duration-300 hover:from-red-600 hover:to-pink-600'>
              {t('deleteModal.deleteButton')}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
}

export default memo(ImageToVideoConvert);
