import { <PERSON><PERSON>, Divide<PERSON>, Toolt<PERSON> } from '@nextui-org/react';
import { useRouter } from 'next/router';
import { Navbar, NavbarContent, NavbarItem } from '@nextui-org/react';
import { FaCrown, FaTheaterMasks } from 'react-icons/fa';
import { IoLay<PERSON>, IoPlanetSharp } from 'react-icons/io5';
import { GoHomeFill, GoPlay } from 'react-icons/go';
import { CgProfile } from 'react-icons/cg';
import { useEffect, useState } from 'react';
import mixpanel from 'mixpanel-browser';
import { profileAtom } from 'state';
import { useAtom } from 'jotai';
import { useTranslation } from 'react-i18next';
import Link from 'next/link';
import { FiHelpCircle, FiTool, FiPlus } from 'react-icons/fi';
import { useCollectForyou } from 'hooks/useCollectForyou';
import cn from 'classnames';
import { FaWandMagicSparkles } from 'react-icons/fa6';
import { HiOutlineArrowsExpand } from 'react-icons/hi';
import { RiScissorsCutFill, RiSketching } from 'react-icons/ri';
import { TbPhotoEdit } from 'react-icons/tb';
import { IconUserSquare } from '@tabler/icons-react';
import { MdAnimation, MdDashboard } from 'react-icons/md';
import { IoIosColorPalette } from 'react-icons/io';
import { PiUserBold } from 'react-icons/pi';
import { VideoIcon } from 'lucide-react';
import { trackUpgradeButtonClicked } from '../../utilities/analytics';

// Demo tools list for showcase

export default function Sidebar({
  isMobile,
  loginPopupOnly,
}: {
  isMobile?: boolean;
  loginPopupOnly?: boolean;
}) {
  const router = useRouter();
  const [profile, setProfile] = useAtom(profileAtom);
  const { t } = useTranslation(['sidebar', 'common']);
  const [shouldShowMobile, setShouldShowMobile] = useState(true); // Default to mobile view to prevent flash
  const [isClient, setIsClient] = useState(false);

  const currentPath = router.pathname;
  // console.log(currentPath);
  const explorePath = '/home';
  const characterPath = '/character';
  const publishPath = '/publish';
  const communityPath = '/community';
  const profilePath = '/profile';
  const allToolsPath = '/tools';
  const pricingPath = '/pricing';
  const { collList: favoriteTools } = useCollectForyou();
  // console.log(favoriteTools);

  //! HANDLE AUTH CHECK

  useEffect(() => {
    const fetchProfile = async () => {
      const response = await fetch('/api/fetchProfile', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ method: 'profile' }),
      });
      const data = await response.json();
      if (!data.error) {
        setProfile(data);
      }
    };
    fetchProfile();
  }, []);

  // Initialize client-side rendering
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Handle responsive breakpoint based on current page
  useEffect(() => {
    if (!isClient) return;

    const checkScreenSize = () => {
      setShouldShowMobile(window.innerWidth < 1024);
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    return () => window.removeEventListener('resize', checkScreenSize);
  }, [currentPath, isClient]);

  const navLinks = [
    {
      path: explorePath,
      icon: <GoHomeFill className='w-[18px] h-[18px]' />,
      text: t('home'),
      active: currentPath.includes(explorePath),
    },
    {
      path: characterPath + '/1',
      icon: <FaTheaterMasks className='w-[18px] h-[18px]' />,
      text: t('characters'),
      active: currentPath.includes(characterPath),
    },
    {
      path: publishPath,
      icon: <FiPlus className='w-[18px] h-[18px]' />,
      text: t('post'),
      active: currentPath.includes(publishPath),
    },
    {
      path: communityPath,
      icon: <IoPlanetSharp className='w-[18px] h-[18px]' />,
      text: t('community'),
      active: currentPath.includes(communityPath),
    },
    {
      path: profilePath,
      icon: <CgProfile className='w-[18px] h-[18px]' />,
      text: t('profile'),
      active: currentPath.includes(profilePath),
    },
  ];

  return (
    <>
      {!loginPopupOnly && (
        <div className='caffelabs text-foreground bg-background'>
          <div
            className={cn(
              'fixed top-0 flex-col p-5 pr-0 w-56 h-full bg-white lg:w-[240px] z-[10]',
              {
                hidden: shouldShowMobile,
                flex: !shouldShowMobile,
              },
            )}>
            <div className='overflow-y-auto flex-grow pt-16 pr-5'>
              {/* 主导航 */}
              <div className='mb-4'>
                {navLinks.map(link => (
                  <Link href={link.path} key={link.path}>
                    <Button
                      size='md'
                      variant='light'
                      radius='lg'
                      className={cn(
                        'w-full flex items-center gap-3 px-3 py-2 rounded-lg text-sm',
                        {
                          'bg-[#f7f5fd] text-primary font-semibold':
                            link.active,
                          'text-slate-600 hover:bg-[#f5f6f8]': !link.active,
                        },
                      )}
                      style={{ justifyContent: 'flex-start' }}>
                      <div
                        className={cn({
                          'text-primary': link.active,
                          'text-slate-500': !link.active,
                        })}>
                        {link.icon}
                      </div>
                      {link.text}
                    </Button>
                  </Link>
                ))}
              </div>

              {/* <Divider className="my-2" /> */}

              {/* 工具分组 */}
              <div>
                <div className='flex gap-1 items-center px-3 mt-8 mb-2 text-xs font-semibold text-gray-400'>
                  {t('tools')}
                  {/* add a question mark icon for tooltip */}
                  <Tooltip content={t('tools_description')} placement='right'>
                    <span tabIndex={0} className='cursor-pointer'>
                      <FiHelpCircle className='w-4 h-4' />
                    </span>
                  </Tooltip>
                </div>
                {favoriteTools.map(tool => (
                  <Link href={tool.path} key={tool.path}>
                    <Button
                      size='md'
                      variant='light'
                      radius='lg'
                      className={cn(
                        'w-full flex items-center gap-3 px-3 py-2 rounded-lg text-sm',
                        {
                          'bg-[#f7f5fd] text-primary font-semibold':
                            currentPath === tool.path,
                          'text-gray-600 hover:bg-[#f5f6f8]':
                            currentPath !== tool.path,
                        },
                      )}
                      style={{ justifyContent: 'flex-start' }}>
                      <div
                        className={cn({
                          'text-primary': currentPath === tool.path,
                          'text-slate-500': currentPath !== tool.path,
                        })}>
                        {/* <FaWandMagicSparkles className="w-[18px] h-[18px]" /> */}
                        {(() => {
                          switch (tool.path) {
                            case '/oc-maker':
                              return (
                                <PiUserBold className='w-[18px] h-[18px] stroke-1' />
                              );
                            case '/create':
                              return (
                                <MdDashboard className='w-[18px] h-[18px]' />
                              );
                            case '/ai-comic-generator':
                              return (
                                <MdDashboard className='w-[18px] h-[18px] stroke-1' />
                              );
                            case '/image-animation-generator':
                              return (
                                <GoPlay className='w-[18px] h-[18px] stroke-1' />
                              );
                            case '/inbetween':
                              return (
                                <MdAnimation className='w-[18px] h-[18px]' />
                              );
                            case '/line_art_colorization':
                              return (
                                <IoIosColorPalette className='w-[18px] h-[18px] stroke-1' />
                              );
                            case '/sketch_simplification':
                              return (
                                <RiSketching className='w-[18px] h-[18px] stroke-1' />
                              );
                            case '/layer_splitter':
                              return (
                                <IoLayers className='w-[18px] h-[18px] stroke-1' />
                              );
                            case '/playground':
                              return (
                                <TbPhotoEdit className='w-[18px] h-[18px]' />
                              );
                            case '/background-removal':
                              return (
                                <RiScissorsCutFill className='w-[18px] h-[18px]' />
                              );
                            case '/image-upscaling':
                              return (
                                <HiOutlineArrowsExpand className='w-[18px] h-[18px]' />
                              );
                            case '/video_upscaling':
                              return (
                                <HiOutlineArrowsExpand className='w-[18px] h-[18px]' />
                              );
                            case '/video_interpolation':
                              return (
                                <MdAnimation className='w-[18px] h-[18px]' />
                              );
                            case '/filter/ai-character-sheet-generator':
                              return (
                                <IconUserSquare className='w-[18px] h-[18px]' />
                              );
                            case '/video-to-video':
                              return (
                                <VideoIcon className='w-[18px] h-[18px]' />
                              );
                            default:
                              return (
                                <FaWandMagicSparkles className='w-[18px] h-[18px]' />
                              );
                          }
                        })()}
                      </div>
                      <span className='truncate'>
                        {t(`common:${tool.title_key}`)}
                      </span>
                    </Button>
                  </Link>
                ))}
              </div>
            </div>
            <div className='mt-4 sticky bottom-0 left-0 right-0 bg-white text-[#333] px-4 py-4'>
              <Divider className='mb-4' />
              {profile ? (
                <div className='flex justify-between items-center px-2 mb-2 w-full text-sm font-bold'>
                  <div className='overflow-hidden max-w-2/3 text-ellipsis'>
                    {profile.user_name}
                  </div>
                  <div>{profile.credit || 0} Zaps</div>
                </div>
              ) : null}
              <Button
                size='md'
                className='flex overflow-hidden gap-1 items-center px-4 py-1 w-full text-[15px] font-semibold text-white rounded-full bg-gradient-to-r from-primary-500 to-purple-500'
                onClick={() => {
                  if (profile?.id) {
                    trackUpgradeButtonClicked(profile.id, 'sidebar');
                  }
                  router.push(pricingPath);
                }}>
                <FaCrown className='mr-0 w-5 h-5 mr-1.5' />
                {t('upgrade_now')}
              </Button>
            </div>
          </div>

          <Navbar
            className={cn(
              'fixed bottom-0 justify-center p-0 w-full ios-safe-bottom pwa-bottom-nav border-t border-gray-200',
              {
                block: shouldShowMobile,
                hidden: !shouldShowMobile,
              },
            )}
            maxWidth={'full'}
            isBlurred={false}
            style={{ top: 'auto', height: '4.5rem' }}>
            {/* <NavbarContent className="flex justify-center p-0 w-full" justify="center"> */}
            <NavbarContent
              className='flex -gap-1 sm:gap-3 md:gap-8 justify-center items-center p-2 w-full h-full'
              justify='center'>
              <NavbarItem className='p-0 m-0'>
                <Button
                  as={Link}
                  isIconOnly
                  color='primary'
                  variant='flat'
                  className='m-0 h-full w-16 p-2'
                  style={{
                    backgroundColor: 'transparent',
                    color:
                      currentPath !== explorePath
                        ? 'lightslategray'
                        : 'text-primary',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                  }}
                  href={explorePath}>
                  <div className='flex flex-col items-center gap-1'>
                    <GoHomeFill className='w-4 h-4 sm:w-5 sm:h-5' />
                    <span className='text-xs font-medium'>{t('home')}</span>
                  </div>
                </Button>
              </NavbarItem>

              <NavbarItem className='p-0 m-0'>
                <Button
                  as={Link}
                  isIconOnly
                  color='primary'
                  variant='flat'
                  className='m-0 h-full w-16 p-2'
                  style={{
                    backgroundColor: 'transparent',
                    color: !currentPath.includes(characterPath)
                      ? 'lightslategray'
                      : 'text-primary',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                  }}
                  href={`${characterPath}/1`}
                  onClick={e => {
                    e.preventDefault();
                    try {
                      mixpanel.track('click.home.sidebar.character');
                    } catch (error) {}
                    router.push(`${characterPath}/1`);
                  }}>
                  <div className='flex flex-col items-center gap-1'>
                    <FaTheaterMasks className='w-4 h-4 sm:w-5 sm:h-5' />
                    <span className='text-xs font-medium'>
                      {t('characters')}
                    </span>
                  </div>
                </Button>
              </NavbarItem>
              {/* <NavbarItem className="p-0 m-0">
                            <Button
                                as={Link}
                                color="primary"
                                variant="flat"
                                className="m-0 h-[3rem] w-18 p-0 mb-3"
                                style={{
                                    backgroundColor: "white",
                                    color: currentPath != worldPath ? 'lightslategray' : 'black',
                                    display: 'flex',
                                    flexDirection: 'column',
                                    alignItems: 'center'
                                }}
                                onClick={() => {

                                    try { mixpanel.track('click.home.sidebar.roleplay'); } catch (error) { }
                                    router.push(`${worldPath}?character_id=`);
                                }}
                            >
                                <div className="flex flex-col items-center">
                                    <IoIosChatbubbles className="w-6 h-6" />
                                    <span className="text-xs">Roleplay</span>
                                </div>
                            </Button>
                        </NavbarItem> */}

              <NavbarItem className='p-0 flex flex-col items-center'>
                <Button
                  isIconOnly
                  as={Link}
                  size='sm'
                  color='primary'
                  variant='shadow'
                  className='mb-1 h-8 w-8 sm:h-10 sm:w-10 mx-2 sm:mx-2 rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 bg-gradient-to-r from-primary-500 to-purple-600'
                  href={publishPath}>
                  <FiPlus className='w-3 h-3 sm:w-4 sm:h-4 text-white' />
                </Button>
              </NavbarItem>

              <NavbarItem className='p-0 m-0'>
                <Button
                  isIconOnly
                  as={Link}
                  color='primary'
                  variant='flat'
                  className='m-0 h-full w-16 p-2'
                  style={{
                    backgroundColor: 'transparent',
                    color:
                      currentPath !== communityPath
                        ? 'lightslategray'
                        : 'text-primary',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                  }}
                  href={communityPath}>
                  <div className='flex flex-col items-center gap-1'>
                    <IoPlanetSharp className='w-4 h-4 sm:w-5 sm:h-5' />
                    <span className='text-xs font-medium'>
                      {t('community')}
                    </span>
                  </div>
                </Button>
              </NavbarItem>
              {/* <NavbarItem className="p-0 m-0">
                            <Button
                                as={Link}
                                color="primary"
                                variant="flat"
                                className="m-0 h-[3rem] w-18 p-0 mb-3"
                                style={{
                                    backgroundColor: "white",
                                    color: currentPath != leaderboardPath ? 'lightslategray' : 'black',
                                    display: 'flex',
                                    flexDirection: 'column',
                                    alignItems: 'center'
                                }}
                                href={leaderboardPath}
                            >
                                <div className="flex flex-col items-center">
                                    <MdLeaderboard className="w-6 h-6" />
                                    <span className="text-xs">Leaderboard</span>
                                </div>
                            </Button>
                        </NavbarItem> */}
              <NavbarItem className='p-0 m-0'>
                <Button
                  isIconOnly
                  as={Link}
                  color='primary'
                  variant='flat'
                  className='m-0 h-full w-16 p-2'
                  style={{
                    backgroundColor: 'transparent',
                    color:
                      currentPath !== profilePath
                        ? 'lightslategray'
                        : 'text-primary',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                  }}
                  href={profilePath}>
                  <div className='flex flex-col items-center gap-1'>
                    <CgProfile className='w-4 h-4 sm:w-5 sm:h-5' />
                    <span className='text-xs font-medium'>{t('profile')}</span>
                  </div>
                </Button>
              </NavbarItem>
            </NavbarContent>
          </Navbar>
        </div>
      )}
    </>
  );
}
