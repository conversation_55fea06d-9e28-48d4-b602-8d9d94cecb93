import React, { useState, useEffect, useRef } from 'react';
import {
  Button,
  Progress,
  Avatar,
  Card,
  Tabs,
  Tab,
  useDisclosure,
  Spinner,
} from '@nextui-org/react';
import { useRouter } from 'next/router';
import {
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  Input,
  Textarea,
} from '@nextui-org/react';
import { FaCloudUploadAlt, FaCrown } from 'react-icons/fa';
import { BiSolidZap } from 'react-icons/bi';
import { v4 as uuidv4 } from 'uuid';
import toast from 'react-hot-toast';
import { Trans, useTranslation } from 'react-i18next';
import {
  MdOutlineCollections,
  MdPhotoLibrary,
  MdVideoCameraBack,
} from 'react-icons/md';

import SpecificFeed from './SpecificFeed';
import { authAtom, profileAtom } from '../../state';
import { useAtom, useAtomValue } from 'jotai';
import ProfileGallery from './ProfileGallery';
import ProfileVideos from './ProfileVideos';
import Link from 'next/link';
import { fetchInvitations } from '../../api/profile';
import UserInfoCard from './UserInfoCard';
import { trackUpgradeButtonClicked } from '../../utilities/analytics';

const uploadImage = async (file: File): Promise<string | null> => {
  const maxSizeInBytes = 2 * 1024 * 1024; // 2 MB size limit (adjust as needed)

  if (file.size > maxSizeInBytes) {
    console.error('File size exceeds the maximum limit of 2 MB.');
    toast.error('File size exceeds maximum limit of 2MB.');
    return null;
  }

  const form = new FormData();
  const imagePath = `app_media/${uuidv4()}.jpg`;
  form.append('file', file);
  form.append('imagePath', imagePath);
  form.append('width', '300');
  form.append('height', '300');

  const result = await fetch('/api/uploadImage', {
    method: 'POST',
    body: form,
  })
    .then(res => res.json())
    .catch();
  if (!result || result.error) {
    return null;
  }
  return result.data;
};

export default function ProfilePage() {
  const { t } = useTranslation('profile');

  const { isOpen, onOpen, onClose } = useDisclosure();
  const [username, setUsername] = useState('');
  const [bio, setBio] = useState('');

  const [profile, setProfile] = useAtom(profileAtom);
  const isAuth = useAtomValue(authAtom);

  //! FETCH PROFILE
  useEffect(() => {
    const fetchProfile = async () => {
      try {
        console.log(
          'useEffect fetchProfile API 被调用',
          new Date().toISOString(),
        ); // 在终端打印调用时间
        const response = await fetch('/api/fetchProfile', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ method: 'profile' }),
        });
        console.log('calling fetch profile');

        const data = await response.json();
        console.log('data success', data);
        setProfile({ ...data, authUserId: data.id });
        setUsername(data.user_name);
        setBio(data.user_desc);
      } catch (error) {
        console.error('Error fetching profile:', error);
      }
    };

    isAuth && fetchProfile();
  }, [isAuth, setProfile]);

  //! HANDLE MODAL
  const handleOpen = () => {
    onOpen();
  };

  const handleClose = () => {
    onClose();
  };

  //! HANDLE EDIT
  const handleUsernameChange = (
    event: React.ChangeEvent<HTMLInputElement>,
  ): void => {
    setUsername(event.target.value);
    // setProfile((prevProfile) => ({
    //     ...prevProfile,
    //     user_name: event.target.value,
    // }));
  };

  const handleBioChange = (
    event: React.ChangeEvent<HTMLInputElement>,
  ): void => {
    // setProfile((prevProfile) => ({
    //     ...prevProfile,
    //     user_desc: event.target.value,
    // }));
    setBio(event.target.value);
  };

  let canCall = true;
  const handleEditProfile = async () => {
    if (!canCall) {
      console.log('Cooldown active. Please wait.');
      return;
    }

    if (!username.trim()) {
      console.log('Empty username. Skipping.');
      return;
    }

    canCall = false;

    setTimeout(() => {
      canCall = true;
    }, 10000);

    try {
      console.log('Sending update request');

      // Make API call to update the profile on the server
      const response = await fetch(`/api/fetchProfile`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          method: 'edit-profile',
          authUserId: profile.authUserId,
          new_username: username,
          new_bio: bio,
          new_image: profile.image,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update profile');
      }

      await response.json();

      // Update the profile state with the new data
      setProfile(prevProfile => ({
        ...prevProfile,
        user_name: username,
        user_desc: bio,
      }));

      console.log('Profile updated successfully');

      onClose();
    } catch (error) {
      console.error('Error updating profile:', error);
    }
  };

  //! Handle upload
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleImageUpload = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      const imageUrl = await uploadImage(file);

      if (imageUrl) {
        console.log('Uploaded image URL:', imageUrl);
        setProfile(prevProfile => ({
          ...prevProfile,
          image: imageUrl as string,
        }));
      } else {
        console.log('Image not updated.');
      }
    }
  };

  return (
    <div>
      {/* 用户信息卡片 */}
      <UserInfoCard
        profile={profile}
        onEditClick={handleOpen}
        isOwnProfile={true}
      />

      {/* 积分条 */}
      {profile.authUserId !== '' && (
        <div className='container relative mx-auto w-full max-w-4xl flex-grow px-4 md:px-6 mt-6'>
          <CreditBar
            profile={profile}
            setProfile={setProfile}
            className='mt-4'
          />
        </div>
      )}

      {/* POP UP MODAL */}
      <Modal size='lg' placement='center' isOpen={isOpen} onClose={handleClose}>
        <ModalContent>
          {onClose => (
            <>
              <ModalHeader>
                <p>{t('editProfile')}</p>
              </ModalHeader>
              <ModalBody className='overflow-y-auto h-full md:overflow-y-none'>
                <div
                  className='relative w-24 h-24 cursor-pointer md:w-28 md:h-28 lg:w-28 lg:h-28'
                  onClick={handleImageUpload}>
                  <img
                    className='object-cover w-full h-full rounded-full'
                    src={profile.image}
                    alt={profile.user_name}
                  />
                  <div className='flex absolute inset-0 justify-center items-center bg-black bg-opacity-50 rounded-full'>
                    <FaCloudUploadAlt
                      className='text-white'
                      style={{ height: '2rem', width: '2rem' }}
                    />
                  </div>
                </div>
                <input
                  type='file'
                  accept='image/*'
                  ref={fileInputRef}
                  style={{ display: 'none' }}
                  onChange={handleFileChange}
                />
                <Input
                  type='username'
                  variant={'flat'}
                  label={t('username')}
                  value={username}
                  onChange={handleUsernameChange}
                  size='lg'
                  className='max-w-s'
                />
                <Textarea
                  label={t('bio')}
                  className='flex text-lg rounded-full'
                  radius='full'
                  variant='flat'
                  placeholder={t('joinDiscussion')}
                  size='lg'
                  minRows={2}
                  maxRows={4}
                  style={{ resize: 'none' }}
                  classNames={{
                    base: 'max-w-s',
                    input: 'resize-y',
                  }}
                  value={bio}
                  onChange={handleBioChange}
                />
              </ModalBody>
              <ModalFooter>
                <Button
                  onClick={() => handleEditProfile()}
                  radius='full'
                  className='text-white bg-gradient-to-tr from-sky-400 to-purple-300 shadow-lg'>
                  {t('save')}
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>

      {/* PERSONAL FEED */}
      <div className='flex flex-col mt-2 w-full'>
        <Tabs
          aria-label='Options'
          radius='full'
          color='secondary'
          size='md'
          variant='solid'
          classNames={{
            tabList: 'bg-white flex justify-center px-4 md:ml-6',
            tab: 'bg-gray-100 w-[150px] text-sm md:text-base',
          }}>
          <Tab
            key='posts'
            title={
              <div className='flex gap-2 items-center'>
                <MdOutlineCollections className='text-xl' />
                <span>{t('myPosts')}</span>
              </div>
            }>
            <Card className='border-none shadow-none min-h-0'>
              <SpecificFeed showMore />
            </Card>
          </Tab>
          <Tab
            key='images'
            title={
              <div className='flex gap-2 items-center'>
                <MdPhotoLibrary className='text-xl' />
                <span>{t('images')}</span>
              </div>
            }>
            <Card className='border-none shadow-none min-h-0'>
              <ProfileGallery />
            </Card>
          </Tab>
          <Tab
            key='videos'
            title={
              <div className='flex gap-2 items-center'>
                <MdVideoCameraBack className='text-xl' />
                <span>{t('videos')}</span>
              </div>
            }>
            <Card className='border-none shadow-none min-h-0'>
              <ProfileVideos />
            </Card>
          </Tab>
        </Tabs>
      </div>
    </div>
  );
}

export function CreditBar({
  profile,
  setProfile,
  className,
}: any): React.ReactElement {
  const router = useRouter();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { t } = useTranslation('profile');
  const maxZap = 1000;
  const [isLoadingInvitations, setIsLoadingInvitations] = useState(false);
  const [invitations, setInvitations] = useState<
    { name?: string; email: string }[]
  >([]);
  const Discord = () => (
    <a
      className='text-blue-600 underline'
      href='https://discord.gg/rxX4B9b2ct'
      target='_blank'
      rel='noopener noreferrer'>
      Discord
    </a>
  );

  useEffect(() => {
    const loadInvitations = async () => {
      if (isOpen) {
        setIsLoadingInvitations(true);
        try {
          const response = await fetchInvitations();
          if (response.data) {
            setInvitations(response.data);
          }
        } catch (error) {
          console.error('Error fetching invitations:', error);
        } finally {
          setIsLoadingInvitations(false);
        }
      }
    };
    loadInvitations();
  }, [isOpen]);

  let tip = t('createAway');
  if (profile.credit < 1000 && profile.credit >= 200) {
    tip = t('getMoreZaps');
  } else if (profile.credit < 200 && profile.credit > 0) {
    tip = t('lowOnZaps');
  } else if (profile.credit <= 0) {
    tip = t('noZapsLeft');
  }
  const today = new Date();
  const todayString = today.toISOString().split('T')[0];
  console.log('profile', profile);
  const [tasks, setTasks] = useState([
    {
      id: 1,
      title: (
        <div>
          {t('referUsers')}{' '}
          <p className='text-gray-500'>({t('referReward', { count: 500 })})</p>
        </div>
      ),
      points: 500,
      completed: false,
      onClick: () => {
        //! HANDLE COPY REFERRAL LINK
        const textToCopy = `https://komiko.app?code=${profile.invite_code}`;
        navigator.clipboard.writeText(textToCopy).then(() => {
          toast.success(t('referralLinkCopied'));
        });
      },
      buttonCompleted: t('copyLink'),
      buttonIncomplete: t('copyLink'),
    },
    {
      id: 5,
      title: (
        <div>
          {t('referUsers')}{' '}
          <p className='text-gray-500'>({t('referReward', { count: 500 })})</p>
        </div>
      ),
      points: 500,
      completed: false,
      onClick: () => {
        //! HANDLE COPY REFERRAL LINK
        const textToCopy = `${profile.invite_code}`;
        navigator.clipboard.writeText(textToCopy).then(() => {
          toast.success(t('referralCodeCopied'));
        });
      },
      buttonCompleted: t('copyCode'),
      buttonIncomplete: t('copyCode'),
    },
    {
      id: 2,
      title: t('dailyCheckIn'),
      points: 50,
      completed: profile.date_checkin === todayString,
      onClick: async () => {
        if (profile.date_checkin === todayString) {
          return;
        }
        const res = await fetch('/api/dailyCheckIn')
          .then(res => res.json())
          .catch();
        if (res?.error) {
          return;
        }
        setProfile((profile: any) => ({
          ...profile,
          date_checkin: todayString,
          credit: res.data,
        }));

        setTasks(tasks =>
          tasks.map(task =>
            task.id === 2 ? { ...task, completed: true } : task,
          ),
        );
      },
      buttonCompleted: t('completed'),
      buttonIncomplete: t('complete'),
    },
    {
      id: 3,
      title: t('postStory'),
      points: 50,
      completed: profile.date_post === todayString,
      onClick: () => {
        router.push('/create');
      },
      buttonCompleted: t('completed'),
      buttonIncomplete: t('complete'),
    },
    {
      id: 4,
      title: t('likePost'),
      points: 20,
      completed: profile.date_like === todayString,
      onClick: () => {
        router.push('/');
      },
      buttonCompleted: t('completed'),
      buttonIncomplete: t('complete'),
    },
  ]);

  useEffect(() => {
    setTasks(tasks =>
      tasks.map(task => {
        if (task.id === 2)
          return { ...task, completed: profile.date_checkin === todayString };
        if (task.id === 3)
          return { ...task, completed: profile.date_post === todayString };
        if (task.id === 4)
          return { ...task, completed: profile.date_like === todayString };
        return task;
      }),
    );
  }, [profile]);

  return (
    <Card
      className={`p-2 md:p-3 md:mx-2 mb-2 bg-gray-50 rounded-lg ${className || ''}`}>
      <Modal size='lg' isOpen={isOpen} onClose={onClose}>
        <ModalContent>
          {onClose => (
            <>
              <ModalHeader>
                <p>{t('dailyRewards')}</p>
              </ModalHeader>
              <ModalBody className='overflow-y-auto h-full md:overflow-y-none max-h-[500px]'>
                <div style={{ padding: '20px' }}>
                  {tasks.map(task => (
                    <div key={task.id} style={{ marginBottom: '20px' }}>
                      <Card>
                        <div
                          style={{
                            display: 'flex',
                            alignItems: 'center',
                            padding: '16px',
                          }}>
                          <BiSolidZap className='w-5 h-5 text-orange-400' />

                          <div
                            style={{ marginLeft: '10px', fontWeight: 'bold' }}>
                            x{task.points}
                          </div>
                          <div style={{ marginLeft: '20px', flex: 1 }}>
                            {task.title}
                          </div>
                          <Button
                            onClick={task.onClick}
                            disabled={task.completed}
                            className='text-white bg-primary'
                            style={{
                              background: task.completed
                                ? '#f5f5f5'
                                : '#563AFA',
                              color: task.completed ? '#ccc' : '#fff',
                            }}>
                            {task.completed
                              ? task.buttonCompleted
                              : task.buttonIncomplete}
                          </Button>
                        </div>
                      </Card>
                    </div>
                  ))}
                  <div className='mt-6'>
                    <div className='mb-4 text-lg font-bold'>
                      {t('invitedUsers')}
                    </div>
                    <div>
                      {isLoadingInvitations ? (
                        <div className='flex justify-center'>
                          <Spinner size='sm' />
                        </div>
                      ) : invitations.length > 0 ? (
                        <div className='space-y-2'>
                          {invitations.map((invitation, index) => (
                            <Card key={index} className='p-2'>
                              <div className='flex items-center'>
                                <Avatar
                                  name={invitation.name || invitation.email}
                                  size='sm'
                                  className='mr-2'
                                />
                                <div>{invitation.name || invitation.email}</div>
                              </div>
                            </Card>
                          ))}
                        </div>
                      ) : (
                        <div className='text-center text-gray-500'>
                          {t('noInvitations')}
                        </div>
                      )}
                    </div>
                  </div>
                  <div className='mt-6 text-lg'>
                    <Trans
                      i18nKey='joinDiscord'
                      ns='profile'
                      components={{
                        discord: <Discord />,
                      }}></Trans>
                  </div>
                </div>
              </ModalBody>
            </>
          )}
        </ModalContent>
      </Modal>
      <div className='flex justify-between items-center gap-2 md:gap-3'>
        <div className='flex flex-col flex-1'>
          <div className='flex justify-between items-center mb-0.5 md:mb-1'>
            <div className='flex items-center'>
              <BiSolidZap className='mr-1.5 md:mr-2 w-4 h-4 md:w-5 md:h-5 text-orange-400' />
              <span className='font-medium text-sm md:text-base'>
                {profile.credit}
              </span>
            </div>
            <div className='text-xs md:text-sm text-gray-600 text-right flex-shrink-0'>
              {tip}
            </div>
          </div>
          <Progress
            value={(100 * profile.credit) / maxZap}
            color='primary'
            className='h-1.5 md:h-2 rounded-lg'
          />
        </div>
        <div className='flex gap-1.5 md:gap-2 ml-2 md:ml-3'>
          <Button
            className='text-white rounded-full bg-primary-500 text-xs md:text-sm px-2 md:px-3'
            size='sm'
            onClick={() => {
              if (profile?.id) {
                trackUpgradeButtonClicked(profile.id, 'profile');
              }
              router.push('/pricing');
            }}>
            <FaCrown className='w-3 h-3 md:w-4 md:h-4 mr-0.5' />
            <span>{t('upgrade')}</span>
          </Button>
          <Button
            className='text-white rounded-full bg-secondary text-xs md:text-sm px-2 md:px-3'
            onClick={onOpen}
            size='sm'>
            {t('rewards')}
          </Button>
        </div>
      </div>
    </Card>
  );
}
