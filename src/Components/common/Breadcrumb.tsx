import React from 'react';
import { Breadcrumbs, BreadcrumbItem } from '@nextui-org/react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import { useTranslation } from 'react-i18next';

interface BreadcrumbProps {
  className?: string;
}

export const Breadcrumb: React.FC<BreadcrumbProps> = ({ className }) => {
  const router = useRouter();
  const { t } = useTranslation('common');

  // 从路径中提取基础工具和变体信息
  const pathSegments = router.asPath.split('/').filter(segment => segment && !segment.startsWith('?'));

  // 如果不是变体页面（路径段少于2个），不显示breadcrumb
  if (pathSegments.length < 2) {
    return null;
  }

  const baseTool = pathSegments[0];
  const variant = pathSegments[1];

  // 工具名称映射
  const toolNameMap: Record<string, string> = {
    'ai-anime-generator': t('nav.ai_anime_generator') || 'AI Anime Generator',
    'ai-comic-generator': t('nav.ai_comic_generator') || 'AI Comic Generator',
    'video-to-video': t('nav.video_to_video') || 'Video to Video AI',
    'layer-splitter': t('nav.layer_splitter') || 'Layer Splitter',
    'ai-talking-head': t('nav.ai-talking-head') || 'AI Talking Head',
    'oc-maker': t('nav.oc_maker') || 'OC Maker',
    playground: t('nav.playground') || 'AI Playground',
  };

  const baseToolName =
    toolNameMap[baseTool] ||
    baseTool.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

  const getBaseToolHref = (tool: string): string => {
    const toolPathMap: Record<string, string> = {
      playground: '/playground',
      'oc-maker': '/oc-maker',
      'ai-anime-generator': '/ai-anime-generator',
      'ai-comic-generator': '/ai-comic-generator',
      'video-to-video': '/video-to-video',
      'layer-splitter': '/layer-splitter',
      'ai-talking-head': '/ai-talking-head',
    };

    return toolPathMap[tool] || `/${tool}`;
  };

  return (
    <div className={className}>
      <Breadcrumbs
        size='sm'
        variant='light'
        separator='/'
        classNames={{
          base: 'px-0',
          list: 'bg-transparent',
          separator: 'text-gray-400 px-1',
        }}>
        <BreadcrumbItem>
          <Link
            href={getBaseToolHref(baseTool)}
            className='text-gray-500 hover:text-primary-600 transition-colors text-sm'>
            {baseToolName}
          </Link>
        </BreadcrumbItem>
        <BreadcrumbItem>
          <span className='text-gray-700 text-sm'>
            {variant
              .replace(/-/g, ' ')
              .replace(/\b\w/g, l => l.toUpperCase())
              .replace(/\bAi\b/g, 'AI')
              .replace(/\bOc\b/g, 'OC')
              .replace(/\bTo\b/g, 'to')}
          </span>
        </BreadcrumbItem>
      </Breadcrumbs>
    </div>
  );
}; 