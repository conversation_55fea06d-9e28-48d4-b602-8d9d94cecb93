{"copyright": "Copyright © {{currentYear}} Caffelabs. Alle Rechte vorbehalten.", "dashboard": {"main_nav_documentation": "Dokumentation", "main_nav_support": "Support", "sidebar_nav_clusters": "Cluster", "sidebar_nav_billing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sidebar_nav_settings": "Einstellungen", "title_text": "Cluster erstellen und verwalten"}, "marketing": {"introducing": "Wir präsentieren Saasfly", "get_started": "Loslegen", "price_features": "Kosteneffizient für Produktionsumgebungen.", "main_nav_features": "Funktionen", "main_nav_pricing": "<PERSON><PERSON>", "main_nav_create_comics": "Spielplatz", "main_nav_products": "Produkte", "main_nav_blog": "Blog", "main_nav_partners": "Partner", "main_nav_documentation": "Dokumentation", "login": "Anmelden", "signup": "Registrieren"}, "price": {"title": "Günstige und transparente Preise!", "pricing": "<PERSON><PERSON>", "slogan": "Starte mit voller Geschwindigkeit!", "monthly_bill": "Monatliche A<PERSON>nung", "annual_bill": "Jährliche Abrechnung", "annual_info": "wird j<PERSON><PERSON>lich abgerechnet", "monthly_info": "wird monatlich abgerechnet", "mo": "/Monat", "contact": "um unser Support-Team zu kontaktieren.", "contact_2": "Du kannst die Abonnements testen, es fallen keine Gebühren an.", "faq": "Häufig gestellte Fragen", "faq_detail": "In unseren umfassenden FAQs findest du schnelle Antworten auf häufige Fragen. Wenn du weitere Unterstützung benötigst, <PERSON><PERSON><PERSON><PERSON> nicht, uns zu kontaktieren.", "signup": "Registrieren", "upgrade": "Upgrade", "manage_subscription": "Abonnement verwalten", "go_to_dashboard": "Zum Dashboard"}, "nav": {"features": "Funktionen", "ai_comic_generator": "KI-Comic-Generator", "pricing": "<PERSON><PERSON>", "faq": "FAQ", "blog": "Blog", "partners": "Partner", "menu": "<PERSON><PERSON>", "ai_anime_generator": "AI-Anime-Generator", "anigen_competition": "AniGen-Wettbewerb", "video_to_video": "Video zu Video KI", "layer_splitter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ai-talking-head": "KI-Sprechender Kopf", "professional_artists": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "oc_maker": "O<PERSON>", "playground": "KI-Spielplatz"}, "footer": {"pricing": "<PERSON><PERSON>", "ai_comic_generator": "KI-Comic-Generator", "logo_alt": "Komiko KI Logo", "twitter_aria": "Twitter", "twitter_alt": "Twitter", "tiktok_aria": "TikTok", "tiktok_alt": "TikTok", "ai_tools": "KI-Tools", "description": "<PERSON><PERSON><PERSON> ist eine One-Stop-KI-Plattform für die Erstellung von Comics, Manga, Manhwa und Anime.", "ai_models": "KI-Modelle", "all_tools": "Alle Werkzeuge", "comic_tools": "Comic-Tools", "illustration_tools": "Illustrationstools", "animation_tools": "Animationstools", "learn_more": "<PERSON><PERSON> er<PERSON>", "languages": "<PERSON><PERSON><PERSON>", "carbon_removal": "Ko<PERSON>enstoffentfernung", "blog": "Blog", "partners": "Partner"}, "layer_options": {"send_to_back": "In den Hintergrund", "send_backward": "Schrittweise nach hinten", "send_forward": "Schrittweise nach vorne", "send_to_front": "In den Vordergrund"}, "ai_tools": {"comic_generation": {"title": "Comics, Manga und Manhwa erstellen", "create_character": {"title": "<PERSON><PERSON><PERSON>", "content": "Entwirf originelle Charaktere für deine Geschichten."}, "create_on_canvas": {"title": "Comic-<PERSON><PERSON><PERSON><PERSON>", "content": "<PERSON><PERSON><PERSON> deine Comics von Grund auf auf einer leeren <PERSON>wan<PERSON>."}, "ai_comic_generator": {"title": "KI-Comic-Generator", "content": "Gib eine Idee ein und generiere in Sekundenschnelle einen Comic."}, "ai_comic_factory": {"title": "KI Comic Fabrik"}, "ai_manga_maker": {"title": "KI Manga Macher"}, "ai_manhwa_generator": {"title": "KI Manhwa Generator"}, "ai_manga_generator": {"title": "KI Manga Generator"}, "ai_comic_strip_generator": {"title": "KI Comic-Strip Generator"}, "ai_comic_book_generator": {"title": "KI Comicbuch Generator"}, "ai_comic_maker": {"title": "KI Comic Macher"}, "ai_storyboard_generator": {"title": "KI Storyboard Generator"}, "free_ai_comic_strip_maker": {"title": "Kostenloser KI Comic-Strip Macher"}, "nsfw_ai_comic_generator": {"title": "NSFW KI Comic Generator"}, "ai_marvel_comic_generator": {"title": "KI Marvel Comic Generator"}, "batman_comic_ai_generator": {"title": "Batman Comic KI Generator"}, "dc_comic_ai_generator": {"title": "DC Comic KI Generator"}, "ai_porn_comic_generator": {"title": "KI Porn Comic Generator"}, "ai_erotic_comic_generator": {"title": "KI Erotischer Comic Generator"}, "boomer_comic_ai_generator": {"title": "Boomer Comic KI Generator"}, "anime_manga_ai_generator": {"title": "Anime Manga KI Generator"}, "ai_webtoon_generator": {"title": "KI Webtoon Generator"}}, "illustration": {"title": "Unterstützung für Kunst, Illustration und Storyboarding", "line_art_colorization": {"title": "Line-Art-Kolorierung", "content": "Füge deinen Strichzeichnungen und Skizzen Farbe hinzu."}, "background_removal": {"title": "Hintergrundentfernung", "content": "Entferne mit KI den Hintergrund aus Bildern."}, "image_upscaling": {"title": "Bild-Upscaling", "content": "Skaliere deine Bilder mit KI hoch."}, "image_relighting": {"title": "Bild-Relighting", "content": "Beleuchte deine Bilder mit KI neu."}, "photo_to_anime": {"title": "Foto zu Anime", "content": "Verwandle dein Foto in Anime- und andere Stilrichtungen."}, "ai_playground": {"title": "KI-Spielplatz", "content": "Konvertieren Sie Fotos in Anime, Manga, Cartoon und mehr als 30 andere Kunststile."}, "frame_pack_ai_video_generator": {"title": "FramePack KI-Video-Generator"}, "vidu_ai_video_generator": {"title": "Vidu Q1 Video-Generator"}, "magi_1_video_generator": {"title": "Magi-1 Video-Generator"}, "ai-anime-generator": {"title": "KI Anime Generator"}, "ai_anime_generator": {"title": "An<PERSON><PERSON><PERSON><PERSON>-Generator", "content": "<PERSON><PERSON><PERSON>-Stil-Bilder mit Charakteren."}, "sketch_simplification": {"content": "Wandle grobe, unordentliche Skizzen in saubere Liniendarstellungen um.", "title": "Skizzenvereinfachung"}, "veo3_video_generator": {"title": "Veo3 KI-Video-Generator"}, "ai_waifu_generator": {"title": "KI-Waifu-Generator"}, "ai_naruto_generator": {"title": "KI-Naruto-Generator"}, "random_genshin_character_generator": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>Charakter-Generator"}, "dragon_ball_ai_generator": {"title": "Dragon Ball KI-Generator"}, "pokemon_ai_generator": {"title": "Pokemon KI-Generator"}, "ai_genshin_impact_generator": {"title": "KI Genshin Impact-Generator"}, "one_piece_ai_generator": {"title": "One Piece KI-Generator"}, "demon_slayer_ai_generator": {"title": "Demon Slayer KI-Generator"}, "attack_on_titan_ai_generator": {"title": "Attack on Titan KI-Generator"}, "jujutsu_kaisen_ai_generator": {"title": "<PERSON><PERSON><PERSON> KI-Generator"}, "my_hero_academia_ai_generator": {"title": "My Hero Academia KI-Generator"}, "spy_x_family_ai_generator": {"title": "Spy x Family KI-Generator"}, "league_of_legends_ai_generator": {"title": "League of Legends KI-Generator"}, "ai_disney_pixar_generator": {"title": "KI-Disney-Pixar-Generator"}, "marvel_ai_generator": {"title": "Marvel KI-Generator"}, "sonic_ai_generator": {"title": "Sonic KI-Generator"}, "ai_spiderman_generator": {"title": "KI-Spiderman-Generator"}, "anime_ai_generator": {"title": "Anime KI-Generator"}, "anime_art_ai_generator": {"title": "Anime-Kunst-KI-Generator"}, "studio_ghibli_style_ai_image_generator": {"title": "Studio Ghibli Stil KI-Bildgenerator"}, "female_character_generator": {"title": "<PERSON><PERSON><PERSON><PERSON> Charakter-Generator"}, "superhero_ai_generator": {"title": "Superhelden KI-Generator"}, "villain_maker": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "villain_generator": {"title": "Schurkengenerator"}, "fantasy_character_ai_generator": {"title": "Fan<PERSON>ie-Charakter-KI-Generator"}, "tim_burton_character_maker": {"title": "<PERSON>-<PERSON><PERSON><PERSON>"}, "dbz_ai_generator": {"title": "DBZ KI-Generator"}, "ai_elf_generator": {"title": "KI-Elfen-Generator"}, "ai_princess_generator": {"title": "KI-Prinzessinnen-Generator"}, "ai_vampire_generator": {"title": "KI-Vampir-Generator"}, "ai_pirate_generator": {"title": "KI-Piraten-Generator"}, "ai_couple_generator": {"title": "KI-Paar-Generator"}, "chibi_creator": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "chibi_ai_generator": {"title": "Chibi KI-Generator"}, "ai_character_generator_from_text": {"title": "KI-Charakter-Generator aus Text"}, "video_game_character_generator": {"title": "Videospiel-Charakter-Generator"}, "ai_fantasy_art_generator": {"title": "KI-Fantasie-<PERSON>nst-Generator"}, "ai_world_generator": {"title": "KI-Welt-Generator"}, "ai_landscape_generator": {"title": "KI-Landschafts-Generator"}, "ai_map_generator": {"title": "KI-Karten-Generator"}, "ai_village_generator": {"title": "KI-Dorf-Generator"}, "ai_mansion_builder": {"title": "KI-Herrenhaus-Erbauer"}, "ai_castle_generator": {"title": "KI-Schloss-Generator"}, "fantasy_island_generator": {"title": "<PERSON><PERSON><PERSON>-Insel-Generator"}, "city_map_maker": {"title": "Stadtplan-Ersteller"}, "ai_skybox_generator": {"title": "KI-Skybox-Generator"}, "pop_art_creator": {"title": "Pop-Art-<PERSON><PERSON><PERSON>"}, "pop_art_generator": {"title": "Pop-Art-Generator"}, "ai_watercolor_generator": {"title": "KI-Aquarell-Generator"}, "abstract_ai_art_generator": {"title": "Abstrakter KI-Kunst-Generator"}, "ai_renaissance_painting_generator": {"title": "KI-Renaissance-Malerei-Generator"}, "ai_oil_painter": {"title": "KI-Ö<PERSON><PERSON>"}, "ai_dark_art_generator": {"title": "KI-Dunkle-Kunst-Generator"}, "ai_cartoon_generator_from_text_free": {"title": "Kostenloser KI-Cartoon-Generator aus Text"}, "ai_pencil_drawing_generator": {"title": "KI-Bleistiftzeichnung-Generator"}, "ai_generated_coloring_pages": {"title": "KI-<PERSON><PERSON><PERSON>"}, "ai_generated_collager": {"title": "KI-generier<PERSON>nhersteller"}, "ai_mural_generator": {"title": "KI-Wandbild-Generator"}, "glitch_art_generator": {"title": "Glitch-Kunst-Generator"}, "ai_vaporwave_generator": {"title": "KI-Vaporwave-Generator"}, "ai_funko_pops_creator": {"title": "KI-Funko-Pops-Ersteller"}, "ai_weapon_generator": {"title": "KI-Waffen-Generator"}, "sword_generator": {"title": "Schwert-Generator"}, "ai_monster_generator": {"title": "KI-Monster-Generator"}, "bratz_ai_generator": {"title": "Bratz KI-Generator"}, "ai_creature_generator": {"title": "KI-Kreaturen-Generator"}, "ai_muppet_generator": {"title": "KI-Muppet-Generator"}, "ai_clothing_generator": {"title": "KI-Kleidung-Generator"}, "ai_jersey_generator": {"title": "KI-Trikot-Generator"}, "ai_t_shirt_designer": {"title": "KI-T-Shirt-Designer"}, "midjourney_video_generator": {"title": "Midjourney KI-Video-Generator"}, "anime_character_generator": {"title": "Anime-<PERSON><PERSON><PERSON>-Generator"}, "vampire_oc_maker": {"title": "Vampir OC-Ersteller"}, "anime_oc_maker": {"title": "Anime <PERSON>"}, "perchance_ai_character_generator": {"title": "Perchance KI-Charakter-Generator"}, "genshin_oc_maker": {"title": "Genshin OC-Ersteller"}, "genshin_oc_generator": {"title": "Genshin OC-Generator"}, "naruto_oc_maker": {"title": "<PERSON><PERSON><PERSON>-<PERSON>eller"}, "demon_slayer_oc_maker": {"title": "Demon Slayer OC-<PERSON><PERSON><PERSON>"}, "pokemon_oc_maker": {"title": "Pokemon OC-Ersteller"}, "jjk_oc_maker": {"title": "JJK OC-Ersteller"}, "jujutsu_kaisen_oc_maker": {"title": "<PERSON><PERSON><PERSON> OC-Ersteller"}, "one_piece_oc_maker": {"title": "One Piece OC-Ersteller"}, "one_piece_characters_generator": {"title": "One Piece Charaktere-Generator"}, "dragon_ball_oc_maker": {"title": "Dragon Ball OC-Ersteller"}, "attack_on_titan_oc_maker": {"title": "Attack on Titan OC-Ersteller"}, "aot_oc_maker": {"title": "AOT OC-Ersteller"}, "my_hero_academia_oc_maker": {"title": "My Hero Academia OC-Ersteller"}, "spy_x_family_oc_maker": {"title": "Spy x Family OC-Ersteller"}, "sonic_oc_maker": {"title": "Sonic OC-Ersteller"}, "league_of_legends_oc_maker": {"title": "League of Legends OC-Ersteller"}, "nsfw_oc_maker": {"title": "NSFW OC-Ersteller"}, "nsfw_character_creator": {"title": "NSFW Charakter-E<PERSON>eller"}, "disney_oc_maker": {"title": "Disney OC-Ersteller"}, "marvel_oc_maker": {"title": "Marvel OC-Ersteller"}, "ai_marvel_character_generator": {"title": "KI-Marvel-Charakter-Generator"}, "ai_action_figure_generator": {"title": "KI-Actionfigur-Generator"}, "ai_character_sheet_generator": {"title": "KI-Charakterbogen-Generator"}, "ai_doll_generator": {"title": "KI-P<PERSON>pen-Generator"}, "anime_ai_filter": {"title": "Anime-KI-Filter"}, "studio_ghibli_ai_generator": {"title": "Studio Ghibli KI-Generator"}, "studio_ghibli_filter": {"title": "Studio Ghibli Filter"}, "anisora_video_generator": {"title": "AniSora KI-Videogenerator"}, "ai_anime_porn_generator": {"title": "KI-Anime-Erotik-Generator"}, "furry_ai_generator": {"title": "Furry-KI-Generator"}, "ai_art_generator_nsfw": {"title": "NSFW KI-Kunstgenerator"}, "ai_image_generator_to_create_a_webtoon_story": {"title": "KI-Bildgenerator für eine Webtoon-Story"}, "anime_ai_generator_nsfw": {"title": "NSFW Anime KI-Generator"}, "nsfw_anime_ai_generator": {"title": "NSFW Anime KI-Generator"}, "nsfw_ai_art_generator": {"title": "NSFW KI-Kunstgenerator"}, "mlp_ai_generator": {"title": "MLP KI-Generator"}, "my_little_pony_ai_generator": {"title": "My Little Pony KI-Generator"}, "anime_pfp_maker": {"title": "Anime PFP Maker"}, "ai_game_asset_generator": {"title": "KI-Spielassetgenerator"}, "mlp_oc_maker": {"title": "MLP OC Maker"}, "my_little_pony_oc_maker": {"title": "My Little Pony OC Maker"}, "visual_novel_character_generator": {"title": "Generator für visuelle Romanfiguren"}, "random_video_game_character_generator": {"title": "Generator für zufällige Videospielcharaktere"}, "moonvalley_ai_video_generator": {"title": "Moonvalley KI-Videogenerator"}, "marey_ai_video_generator": {"title": "<PERSON>y K<PERSON>-Videogenerator"}, "lego_ai_filter": {"title": "Lego AI-Filter"}, "photo_to_line_art": {"title": "Foto zu Strichzeichnung"}, "photo_to_simpsons": {"title": "<PERSON>oto zu <PERSON>"}, "naruto_ai_filter": {"title": "<PERSON><PERSON>to AI-Filter"}, "watercolor_ai_filter": {"title": "Aquarell AI-Filter"}, "cyberpunk_filter": {"title": "Cyberpunk-Filter"}, "emote_maker": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ai_sprite_sheet_generator": {"title": "AI-Sprite-Sheet-Generator"}, "ai_emoji_generator": {"title": "AI-Em<PERSON>ji-Generator"}, "ai_emoticon_generator": {"title": "AI-Emoticon-Generator"}, "anime_sticker_maker": {"title": "Anime-<PERSON><PERSON>-<PERSON><PERSON><PERSON>"}, "ai_sticker_generator": {"title": "AI-Sticker-Generator"}, "ai_sprite_generator": {"title": "AI-Sprite-Generator"}, "ai_plush_generator": {"title": "AI-Plüsch-Generator"}, "ai_badge_generator": {"title": "AI-Abzeichen-Generator"}, "ai_clay_filter": {"title": "AI-<PERSON>n-<PERSON><PERSON>"}, "photo_to_minecraft": {"title": "Foto zu Minecraft"}, "ai_style_transfer": {"title": "AI-Stiltransfer"}, "photo_to_pixel_art": {"title": "Foto zu Pixel-Art"}, "ai_cosplay_generator": {"title": "AI-Cosplay-Generator"}}, "animation": {"title": "Animationsvideos erstellen", "image_to_animation": {"title": "<PERSON><PERSON><PERSON> zu <PERSON>", "content": "Erzeuge flüssige Animationsvideos aus Bildern."}, "inbetweening": {"title": "In-Betweening", "content": "Erzeuge Animationsbilder zwischen Schlüsselbildern."}, "video_upscaling": {"title": "Video-Upscaling", "content": "Erhöhe die Auflösung deiner Videos."}, "video_interpolation": {"title": "Videointerpolation", "content": "Mache <PERSON>s flüssiger, indem du zusätzliche Bilder einfügst."}, "layer_splitter": {"content": "<PERSON><PERSON> in separate Komponenten auf.", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ai_talking_head": {"title": "KI-Sprechender Kopf", "content": "<PERSON><PERSON><PERSON>n Sie animierte sprechende Charaktere aus Standbildern."}, "video_to_video": {"title": "Video zu Video", "content": "Konvertieren Sie Ihre Videos mithilfe von KI in verschiedene Stile."}}, "more": {"title": "UND MEHR", "content": "Entdecke unsere komplette Suite von KI-Tools für Comic, Illustration und Animation."}}, "gridIndex": "<PERSON>ster {{index}}", "popularity": "{{count}} Beliebtheit", "roleplay": "Rollenspiel", "likes": "{{count}} Likes", "following": "Folgen", "follow": "Folgen", "post_card": {"following": "Folgen", "follow": "Folgen", "the_end": "—<PERSON> <PERSON><PERSON>—", "discover": "Entdecken", "more_stories": "Mehr Geschichten", "or_start": "oder starte damit,", "creating_your_own": "deine eigenen zu erstellen", "comments": "Kommentare", "the_end_dash": "- Ende -", "prompts": "Prompts", "prompt_copied": "Prompt in die Zwischenablage kopiert", "copy_prompt": "Prompt kopieren", "generate_more": "<PERSON><PERSON> gene<PERSON>", "no_prompts": "Keine Prompts verfügbar😢", "join_discussion": "Nimm an der Diskussion teil!", "sharing_link_copied": "Freigabelink kopiert! <PERSON><PERSON> ihn jetzt mit deinen Freunden!", "image_downloaded": "Bild erfolgreich heruntergeladen!", "download_failed": "Das Herunterladen des Bildes ist fehlgeschlagen. Bitte versuche es später noch einmal.", "download_image": "Bild her<PERSON><PERSON><PERSON>n", "copy_link": "<PERSON>", "more": "<PERSON><PERSON>", "share_text": "Habe gerade diesen fantastischen Comic \"{{title}}\" gefunden! Du musst ihn dir ansehen!", "delete": "Löschen", "confirm_delete": "Löschvorgang bestätigen", "confirm_delete_message": "Möchten Sie diesen Beitrag wirklich löschen?", "cancel": "Abbrechen", "delete_success": "Beitrag wurde erfolgreich gelö<PERSON>t", "delete_failed": "Beitrag konnte nicht gelöscht werden"}, "uploadFile": {"tapToUpload": "Zum Hochladen tippen oder dein Bild hierher ziehen", "errors": {"invalidExtension": "<PERSON>te wähle eine Datei mit der Erweiterung: {{accept}}", "selectImage": "<PERSON>te wähle ein Bild aus", "selectVideo": "<PERSON>te wähle ein Video aus", "fileSize": "<PERSON> Dateigröße muss kleiner als {{limit}} MB sein.", "maxDuration": "Maximale Dauer: {{duration}} Minuten", "safariVideoTooLong": "<PERSON>ür das beste Erlebnis in Safari verwenden Sie bitte Videos von maximal 10 Sekunden oder versuchen Sie es bei längeren Videos mit Chrome!", "audioSize": "Die Audiodateigröße darf {{limit}}MB nicht überschreiten", "selectAudio": "Bitte wählen Sie eine Audiodatei aus"}, "warnings": {"imageWillCompress": "Das Bild ist größer als {{limit}}MB und wird automatisch komprimiert", "audioTooLarge": "Die Audiodatei ist größer als {{limit}}MB - dies kann die Verarbeitungsqualität beeinträchtigen"}, "dragAudio": "Ziehen Sie Audio hierher oder klicken Si<PERSON>, um es hochzuladen", "supportedAudio": "Unterstützte Formate: MP3, WAV"}, "home": {"create_story": {"title": "Neuer Comic", "subtitle": "Bilder generieren und Geschichten erstellen", "generate": {"title": "<PERSON><PERSON><PERSON>", "description": "Generiere in Sekundenschnelle einen Comic aus einem einzeiligen Prompt"}, "from_scratch": {"title": "<PERSON> auf neu", "description": "Beginne mit einer leeren Leinwand und lasse deiner Kreativität freien <PERSON>"}}, "create_character": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "<PERSON><PERSON>elle Charaktere für deine Geschichten"}, "create_animation": {"title": "<PERSON>eues Rollenspiel"}}, "result_card": {"generating": "Wird gene<PERSON>t", "video_not_supported": "<PERSON><PERSON> Browser unterstützt das Video-Tag nicht.", "more_ai_tools": {"title": "Weitere KI-Tools", "description": "Erweitern Sie Ihre kreativen Möglichkeiten mit zusätzlichen KI-Tools.", "view_more": "Weitere KI-Tools anzeigen"}, "generated_by": "<PERSON><PERSON><PERSON><PERSON> von", "tools": {"remove_bg": "Hintergrund entfernen", "upscale": "Hochskalieren", "anime_style": "Anime-Stil", "animate": "<PERSON><PERSON><PERSON><PERSON>", "video_upscale": "Video hochskalieren", "video_interpolate": "Video interpolieren", "character_sheet": "Charakterblatt", "oc-maker": "OC-Ersteller", "line_art_colorization": "Kolorierung von Strichzeichnungen", "title": "Werkzeuge", "character": "<PERSON><PERSON><PERSON>", "restyle": "Umgestalten", "add_text": "Text hinzufügen"}}, "logout": "Ausloggen", "create_character": "<PERSON><PERSON><PERSON>", "generate_image": "<PERSON><PERSON><PERSON> gene<PERSON>", "generate": "<PERSON><PERSON><PERSON>", "create_comic": "Comic erstellen", "generate_animation": "Animation erstellen", "actions": {"download": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "delete": "Löschen", "publish": "Veröffentlichen", "cancel": "Abbrechen", "confirm": "Bestätigen", "post": "Veröffentlichen", "share": "Teilen", "apply": "<PERSON><PERSON><PERSON>", "applying": "Wird angewendet...", "generate": "<PERSON><PERSON><PERSON><PERSON>", "generating": "Wird erstellt...", "upload": "Hochladen", "uploading": "Wird hochgeladen...", "extract": "Extrahieren", "extracting": "Wird extrahiert..."}, "tools": {"image-animation-generator": "Bildanimations-Generator", "video-upscaling": "Video hochskalieren", "video_upscaling": "Video hochskalieren", "photo-to-anime": "Foto zu Anime", "background-removal": "Hintergrundentfernung", "image-upscaling": "Bild hochskalieren", "video-interpolation": "Video-Interpolation", "video_interpolation": "Video-Interpolation", "ai-character-sheet-generator": "Charak<PERSON>blatt-Generator", "oc-maker": "OC-Ersteller", "oc_maker": "OC-Ersteller", "line_art_colorization": "Kolorierung von Strichzeichnungen", "character": "<PERSON><PERSON><PERSON>", "restyle": "Umgestalten", "layer_splitter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "video_to_video": "Video-zu-Video KI", "ai-talking-head": "KI-Sprechender Kopf", "video-to-video": "Video-zu-Video KI"}, "toasts": {"fetchFailed": "Abrufen fehlgeschlagen", "deleteSuccess": "Erfolg<PERSON><PERSON>", "deleteFailed": "Löschen fehlgeschlagen", "downloadFailed": "Herunterladen fehlgeschlagen", "processingSuccess": "Verarbeitung erfolgreich", "processingFailed": "Verarbeitung fehlgeschlagen", "uploadFailed": "Hochladen fehlgeschlagen", "noVideoUrl": "Keine Video-URL angegeben", "noZaps": "<PERSON><PERSON> kaufen Sie mehr Zaps", "mediaReceived": "Medien von einem anderen Tool empfangen", "extractFailed": "Extraktion des Rahmens fehlgeschlagen. Bitte versuchen Sie es erneut.", "invalidResponse": "Ungültige Antwort von der API.", "timeout": "Die Operation hat das Zeitlimit überschritten.", "missingData": "Erforderliche Daten fehlen.", "unexpectedFormat": "Unerwartetes Datenformat."}, "from_other_tools": "{{input_type}} von {{tools}} empfangen", "input_types": {"image": "Bild", "character": "<PERSON><PERSON><PERSON>", "video": "Video"}, "alt": {"generated_image": "Generiertes Bild"}, "success": {"copy": "In die Zwischenablage kopiert"}, "unknown": "Unbekannt", "error": {"code": "Fehlercode: {{code}}", "requestId": "Anfrage-ID: {{id}}", "downloadFailed": "Download fehlgeschlagen. Bitte versuchen Sie es erneut."}, "video_tabs": {"image_to_animation": "<PERSON><PERSON><PERSON> zu <PERSON>", "video_to_video": "Video zu Video", "talking_head": "Sprechender Kopf", "image_to_video_sub": "Bild zum Video", "character_to_video_sub": "Charakter zum Video"}, "audioRecorder": {"duration": "<PERSON><PERSON>", "recording": "Aufnah<PERSON> l<PERSON>", "preparing": "Wird vorbereitet...", "instructions": "<PERSON>licken Si<PERSON> unten auf die Schaltfläche, um die Aufnahme zu starten", "stop": "<PERSON><PERSON><PERSON><PERSON>den", "start": "Aufnah<PERSON> starten", "record_new": "Neue Audioaufnahme"}, "sampleImage": "Beispielbild", "sampleVideo": "Beispielvideo", "exampleResult": "Beispielergebnis", "loading": "Laden..."}