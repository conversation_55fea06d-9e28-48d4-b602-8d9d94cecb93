{"auth": {"email": "E-Mail", "email_placeholder": "<EMAIL>", "signin_email": "Einloggen mit E-Mail", "signin_others": "Oder weiter mit", "google": "Google", "error": {"title": "Da ist etwas schiefgelaufen.", "description": "Ihre Login-Anfrage ist fehlgeschlagen. Bitte versuchen Sie es erneut."}, "success": {"title": "Bitte prüfen Sie Ihre E-Mail", "description": "Wir haben Ihnen einen Anmeldelink gesendet. Schauen Sie auch in Ihrem Spam-Ordner nach."}, "invitation_code": "Einladungscode", "invitation_code_placeholder": "Einladungscode (optional)"}, "image": {"generation": {"failed": "Bildgenerierung fehlgeschlagen"}, "upscale": {"fetchFailed": "Bildabruf fehlgeschlagen", "deleteSuccess": "Erfolg<PERSON><PERSON>", "deleteFailed": "Löschen fehlgeschlagen", "downloadFailed": "Download fehlgeschlagen", "purchaseMoreZaps": "<PERSON><PERSON> kaufen Sie mehr Zaps", "upscaleFailed": "Bildvergrößerung fehlgeschlagen", "upscaleSuccess": "Bildvergrößerung erfolgreich", "noZaps": "<PERSON><PERSON>"}}, "text": {"generation": {"failed": "Textgenerierung fehlgeschlagen"}}, "character": {"creation": {"failed": "Charaktererstellung fehlgeschlagen", "characterLimitExceeded": "Sie haben das Limit erreicht! Bitte aktualisieren Sie Ihren Plan, um mehr Charaktere zu erstellen."}}, "common": {"fetchFailed": "Bildabruf fehlgeschlagen", "deleteSuccess": "Erfolg<PERSON><PERSON>", "deleteFailed": "Löschen fehlgeschlagen", "downloadFailed": "Download fehlgeschlagen", "noZaps": "<PERSON><PERSON>", "rateLimitExceeded": "Sie haben das Limit erreicht! Bitte aktualisieren Sie Ihren Plan, um mehr auf einmal zu erstellen.", "extractFailed": "Extraktion des Rahmens fehlgeschlagen", "processingFailed": "Verarbeitung fehlgeschlagen", "invalidImage": "Bitte wählen Sie eine gültige Bilddatei"}, "backgroundRemoval": {"purchaseZaps": "Bitte kaufen Sie mehr Zaps, um den Hintergrundentferner zu nutzen", "failed": "Hintergrundentfernung fehlgeschlagen", "success": "Hintergrundentfernung erfolgreich", "mediaReceived": "Medien erfolgreich empfangen"}, "imageToVideo": {"purchaseMoreZaps": "<PERSON><PERSON> kaufen Sie mehr Zaps", "generateSuccess": "Animation erfolgreich erstellt", "generateFailed": "Animationserstellung fehlgeschlagen", "resourceExhausted": "Ressourcen erschöpft", "noZaps": "<PERSON><PERSON>", "fetchVideosFailed": "Videoabruf feh<PERSON>schlagen", "deleteSuccess": "Erfolg<PERSON><PERSON>", "deleteFailed": "Löschen fehlgeschlagen", "downloadFailed": "Download fehlgeschlagen"}, "lineArtColorize": {"fetchFailed": "Bildabruf fehlgeschlagen", "deleteSuccess": "Erfolg<PERSON><PERSON>", "deleteFailed": "Löschen fehlgeschlagen", "downloadFailed": "Download fehlgeschlagen", "purchaseMoreZaps": "<PERSON><PERSON> kaufen Sie mehr Zaps", "colorizeSuccess": "Bild erfolgreich kolo<PERSON>t", "colorizeFailed": "Bildkolorierung fehlgeschlagen", "noZaps": "<PERSON><PERSON>", "createFailed": "Bilderstellung fehlgeschlagen", "mediaReceived": "Medien erfolgreich empfangen"}, "error": {"fetchImages": "Bildabruf fehlgeschlagen", "delete": "Löschen fehlgeschlagen", "download": "Download fehlgeschlagen", "createImage": "Bilderstellung fehlgeschlagen", "noZaps": "<PERSON><PERSON>", "insufficientZaps": "<PERSON><PERSON> kaufen Sie mehr Zaps", "colorizeImage": "Bildkolorierung fehlgeschlagen", "fetchVideos": "Videos konnten nicht geladen werden", "upscaleFailed": "Video-Upscaling fehlgeschlagen", "generateComicFailed": "Comicerstellung fehlgeschlagen, bitte versuchen Sie es erneut.", "failedPost": "Veröffentlichung fehlgeschlagen", "noImageError": "<PERSON><PERSON> uns damit beginnen, e<PERSON><PERSON> Bilder zu erstellen, um voranzukommen", "uploadImageFailed": "Bildhochladen fehlgeschlagen", "videoInterpolationFailed": "Video-Interpolation ist fehlgeschlagen", "failedToGeneratePrompt": "Eingabeaufforderungserstellung fehlgeschlagen", "invalidVideoUrl": "Ungültige Video-URL", "generationModelNotFound": "Generierungsmodell nicht vorhanden", "generationTaskNotFound": "Generierungsaufgabe nicht vorhanden", "invalidParams": "Ungültige Parameter", "generationResultFailed": "Generierungsergebnis konnte nicht abgerufen werden", "videoGenerationFailed": "Ups! Die Videogenerierung ist fehlgeschlagen. Vielleicht versuchen Si<PERSON> ein anderes Modell oder passen Ihren Prompt an – einige Modelle können empfindliche Inhalte blockieren.", "imageGenerationFailed": "Ups! Die Bildgenerierung ist fehlgeschlagen. Vielleicht versuchen Si<PERSON> ein anderes Modell oder passen Ihren Prompt an – einige Modelle können empfindliche Inhalte blockieren.", "sensitiveContent": "Generierung fehlgeschlagen: Der Inhalt wurde als sensibel eingestuft. Bitte versuchen Sie es mit einer anderen Eingabe erneut.", "imageExportFailed": "Das Bild konnte nicht exportiert werden. Bitte versuche es erneut.", "autoPostFailed": "Das automatische Posten ist fehlgeschlagen. Bitte versuchen Sie, manuell zu posten.", "uploadSuccess": "Upload erfolgreich!", "uploadFailed": "Hochladen fehlgeschlagen", "invalidFileType": "Ungültiger Dateityp. Bitte laden Sie nur Bilder oder Videos hoch.", "unsupportedFileType": "<PERSON><PERSON> wird nicht unterstützt. Bitte laden Si<PERSON> nur Bilder oder Videos hoch.", "failedToProcessFile": "Die Datei konnte nicht verarbeitet werden. Bitte versuche es erneut.", "failedToPost": "Der Beitrag konnte nicht erstellt werden. Bitte versuche es erneut.", "someImagesGenerationFailed": "<PERSON><PERSON>n einiger Bilder ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut."}, "success": {"delete": "Erfolg<PERSON><PERSON>", "colorizeImage": "Bildkolorierung erfolgreich", "upscaleVideo": "Video-Upscaling er<PERSON><PERSON><PERSON><PERSON><PERSON>", "downloaded_and_share": "Bild erfolgreich heruntergeladen! Teilen Si<PERSON> es jetzt mit Ihren Freunden!", "download": "Download erfolgreich", "copy": "Erfolgreich k<PERSON>", "videoInterpolationSuccess": "Video-Interpolation war erfolgreich", "publish": "Veröffentlichung erfolgreich", "share": "Inhalt erfolgreich geteilt!", "shareLinkCopied": "Link zum Inhalt in die Zwischenablage kopiert! <PERSON><PERSON> ihn jetzt mit deinen Freunden!", "uploadSuccess": "Ho<PERSON>laden erfolgreich!"}, "video": {"upscale": {"fetchFailed": "Videoabruf feh<PERSON>schlagen", "deleteSuccess": "Erfolg<PERSON><PERSON>", "deleteFailed": "Löschen fehlgeschlagen", "downloadFailed": "Download fehlgeschlagen", "purchaseMoreZaps": "<PERSON><PERSON> kaufen Sie mehr Zaps", "upscaleFailed": "Videovergrößerung fehlgeschlagen", "upscaleSuccess": "Video erfolgreich vergrößert!", "noZaps": "<PERSON><PERSON>", "invalidVideoUrl": "Ungültige Video-URL", "uploadFailed": "Hochladen fehlgeschlagen"}, "interpolation": {"success": "Videointerpolation erfolgreich", "failed": "Videointerpolation fehlgeschlagen"}, "styleTransfer": {"success": "Video erfolgreich erstellt!", "successWithTime": "Video in {{duration}} erstellt!", "failed": "Videoerstellung fehlgeschlagen. Bitte versuchen Sie es erneut.", "frameExtracted": "<PERSON>rstes Bild erfolgreich extrahiert!", "styleApplied": "Stil erfolgreich angewendet!", "referenceApplied": "Referenzbild erfolgreich angewendet!", "useReferenceFailed": "Verwendung des Referenzbildes fehlgeschlagen", "trimmed": "Video auf {{duration}}s g<PERSON><PERSON><PERSON><PERSON>", "processedFirstSeconds": "Video wird nur mit den ersten {{duration}}s verarbeitet", "timeout": "Videoerstellung nach 10 Minuten abgelaufen", "processingFailed": "Videobearbeitung fehlgeschlagen, Originaldatei wird verwendet", "videoDeleted": "Video erfolgreich gelöscht!", "styleTransferFailed": "Stilübertragung fehlgeschlagen. Bitte versuchen Sie es erneut.", "invalidStyleResponse": "Ungültige Antwort von der Stilübertragungs-API", "uploadExtractedFrameFailed": "Hochladen des extrahierten Rahmens fehlgeschlagen", "uploadReferenceFailed": "Hochladen des Referenzbildes fehlgeschlagen", "uploadVideoFailed": "Videoupload fehlgeschlagen", "videoGenerationFailed": "Videoerstellung fehlgeschlagen. Bitte versuchen Sie es erneut.", "videoGenerationTimeout": "Videoerstellung nach 10 Minuten abgelaufen", "noPredictionId": "<PERSON><PERSON>-<PERSON> von der Videoerstellungs-API erhalten", "unexpectedOutputFormat": "Unerwartetes Ausgabeformat von der Videoerstellung", "noVideoUrlFound": "Keine Video-URL in der Erstellungsausgabe gefunden", "missingVideoOrFrame": "Fehlendes Video oder gestylter Rahmen für die Erstellung", "downloadFailed": "Download fehlgeschlagen. Bitte versuchen Sie es erneut.", "deleteFailed": "Löschen fehlgeschlagen. Bitte versuchen Sie es erneut.", "durationWarning": "Achtung: Es werden nur die ersten 5 Sekunden verarbeitet.", "uploadVideoFirst": "Bitte laden Si<PERSON> zu<PERSON>t ein <PERSON> hoch, um den ersten Rahmen zu extrahieren", "extractingFrame": "<PERSON><PERSON><PERSON> Rahmen aus dem Video extrahieren...", "durationInfo": "Originalvideo: {{original}}s, die ersten {{selected}}s werden für die Erstellung verwendet", "videoTrimmed": "Video auf {{duration}}s für die Erstellung gekürzt", "trimFailedUsingOriginal": "Videokürzung fehlgeschlagen, Originalvideo wird verwendet", "videoGenerationStarted": "Erstellung des gestylten Videos gestartet...", "videoGeneratedWithTime": "Video erfolgreich in {{duration}} erstellt!", "referenceImageCropped": "Referenzbild automatisch zugeschnitten, um den Rahmendimensionen zu entsprechen", "autoCropFailed": "Automatisches Zuschneiden fehlgeschlagen, Originalbild wird verwendet. Bitte stellen <PERSON> sicher, dass Ihre Referenz mit der Rahmendimension übereinstimmt.", "frameExtractionEmpty": "Das Extraktionsergebnis der Frames ist leer.", "frameExtractionAllRetries": "Nach allen Versuchen konnten keine Frames aus dem Video extrahiert werden.", "retryingFrameExtraction": "Wiederholung der Frame-Extraktion (Versuch {{attempt}})...", "safariTimeoutAdvice": "Safari-Timeout festgestellt. Bitte versuchen Sie ein kürzeres Video zu verwenden oder nutzen Si<PERSON> einen anderen Browser wie Chrome für eine verbesserte Videobearbeitung.", "safariVideoTooLong": "Das Video ist länger als 10 Sekunden. Bitte kürzen Sie Ihr Video auf maximal 10 Sekunden oder verwenden Sie den Chrome-Browser für das automatische Kürzen.", "safariDurationCheckFailed": "Fehler bei der Überprüfung der Videodauer. Bitte erneut versuchen."}}, "warnings": {"durationWarning": "Das Video ist länger als 5 Sekunden und wird gekürzt.", "uploadVideoFirst": "<PERSON>te laden <PERSON> zu<PERSON>t ein Video hoch", "extractingFrame": "<PERSON><PERSON><PERSON> aus Video extrahieren..."}, "talking-head": {"invalidAudio": "Ungültiges Audioformat. Bitte verwenden Sie MP3 oder WAV", "modelsFetchFailed": "Modellabruf fehlgeschlagen", "login": "Bitte melden Sie sich an, um diese Funktion zu nutzen", "imageRequired": "Bitte wählen Sie ein Bild", "audioRequired": "Bitte wählen Sie eine Audiodatei", "noCredit": "Nicht genügend Guthaben", "success": "Sprechendes Kopfvideo erfolgreich erstellt", "failure": "Videoerstellung fehlgeschlagen", "audioDurationExceeded": "Audiodauer überschreitet das 2-Minuten-Limit. Es werden nur die ersten 2 Minuten verwendet.", "imageTooLarge": "Die Bilddatei ist zu groß. Maximal zulässige Größe: {{maxSize}}MB", "audioTooLarge": "Die Audiodatei ist zu groß. Maximal zulässige Größe: {{maxSize}}MB", "filesSizeTooLarge": "Dateien sind zu groß. Bitte verwenden Sie kleinere Bild- und Audiodateien.", "compressingImage": "Das Bild wird für einen optimalen Upload komprimiert...", "imageCompressionFailed": "Die Komprimierung des Bildes ist fehlgeschlagen. Bitte verwenden Sie eine kleinere Bilddatei.", "requestTooLarge": "Anfrage ist zu groß. Bitte verwenden Sie kleinere Dateien."}, "info": {"compressing": "Die Datei wird komprimiert...", "uploading": "Die Datei wird hochgeladen..."}}