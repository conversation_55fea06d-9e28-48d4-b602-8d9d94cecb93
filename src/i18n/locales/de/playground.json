{"meta": {"title": "AI Playground - <PERSON><PERSON><PERSON>", "description": "Der ultimative KI-Spielplatz für Anime, Manga, Manhwa und Comic-Kunst. Generiere und transformiere deine Fotos, Skizzen und 3D-Renderings mühelos in professionelle Anime- oder Comic-Kunstwerke – mit dem fortschrittlichsten KI-Toolkit.", "keywords": "KI-Spielplatz, Anime-Kunst-Generierung, Manga-Stil-Transformation, Manhwa-Comic-Erstellung, Comic-Stil-KI-Tools, Anime-Charakter-Generator, Manga-Panel-Ersteller, Manhwa-Coloring-KI, Webtoon-Maker, Batch-Comic-Konverter, KI-Anime-Avatar-Ersteller, Sticker-Art-Generator, Comic-Panel-Animation, Illustrations-Stilübertragung, animationsfertige Assets, KI-gestütztes Comic-Toolkit, kreatives KI-Studio, digitale Anime-Kunst-Plattform"}, "hero": {"title": "AI Playground", "description": "Die KI-gestützte All-in-One-Plattform für Künstler, Manga-<PERSON><PERSON><PERSON>, Comiczeichner und Animatoren. Erstelle, konvertiere und transformiere deine Bilder, Fotos, Illustrationen und 3D-Renderings im Handumdrehen in Anime, Manga, Manhwa, Comics, Webtoons und mehr! Nutze fortschrittliche KI-Modelle für nahtlose Stiltransformationen und professionelle Arbeitsabläufe."}, "howItWorks": {"title": "So verwendest du AI Playground", "step1": {"title": "<PERSON><PERSON><PERSON>, Renderings oder Kunstwerke hochladen", "content": "Lade deine Ausgangsmaterialien hoch (JPG, PNG, WEBP Fotos, 3D-Renderings, Charakterillustrationen, Manga-/Comic-Panels oder handgezeichnete Skizzen). Unser KI-Spielplatz analysiert Komposition, Struktur und visuellen Stil für optimale Transformationsergebnisse."}, "step2": {"title": "Kunststil auswählen & anpassen", "content": "<PERSON><PERSON><PERSON>e aus Dutzenden von Anime-, <PERSON><PERSON>-, <PERSON><PERSON>-, <PERSON>-, Webtoon- und digitalen Kunststilen wie Tuschzeichnung, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Pixel Art oder Chibi. Passe Farbpaletten, Linienstärken und mehr an, um deine Vision zu verwirklichen."}, "step3": {"title": "Sofortige KI-gesteuerte Stiltransformation", "content": "Leistungsstarke KI-Modelle wenden automatisch einen authentischen Anime-, Manga- oder Comic-Stil an. Betrachte eine Vorschau und konvertiere deine Bilder mit fortschrittlichen neuronalen Netzen, die auf verschiedene Kunststile und visuelle Inhalte trainiert sind."}, "step4": {"title": "Krea<PERSON>en herunter<PERSON>n, teilen & verwalten", "content": "Lade dein hochauflösendes Kunstwerk herunter, bereit für den Druck, die digitale Comic-Veröffentlichung oder das Teilen in sozialen Medien. Organisiere Assets, erstelle Webtoon-Episoden, Stickerpacks oder Charakterbögen mit intuitiven Verwaltungstools."}}, "examples": {"title": "AI Playground Beispiele", "inputAlt": "Bild vor der KI-Transformation", "outputAlt": "Bild nach der Anime/Comic-KI-Transformation", "inputLabel": "Originalbild", "outputLabel": "KI-konvertiertes Kunstwerk", "style": {"anime": "Anime-Stil", "manga": "Manga-Stil", "manhwa": "Manhwa-Stil", "webtoon": "Webtoon/Comic-Stil"}}, "benefits": {"title": "Warum AI Playground nutzen?", "feature1": {"title": "🎨 Fortschrittlicher KI-Stiltransfer", "content": "Branchenführende neuronale Netze für Anime-, Manga-, Manhwa-, Webtoon- und Cartoon-Stile. Erziele eine hochwertige Stiltransformation, die die Komposition und Emotion deines Kunstwerks bewahrt und gleichzeitig authentische Strichzeichnungen, Cel-Shading und Farbarbeiten hinzufügt."}, "feature2": {"title": "🗂️ Effiziente Bildverarbeitung", "content": "<PERSON><PERSON> Bilder, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Comic-Panels oder Kunstwerke einfach hoch, konvertiere und verwalte sie. Entwickelt für einen optimierten Workflow und hohen Komfort."}, "feature3": {"title": "💎 <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, professionelle Ausgaben", "content": "Lade gest<PERSON>en scharfe, hochauflösende PNG-, JPG- oder WEBP-Bilder herunter – ideal für professionelle Comic-Produktion, Druck, digitale Veröffentlichung oder Animations-Assets."}, "feature4": {"title": "🖌️ Vielseitige KI-Kunstbibliothek & Anpassung", "content": "Entdecke eine riesige Sammlung von KI-gestützten Stilen: <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Cartoon, Chibi, Actionfigur, Pixel Art und mehr. Passe deine Ausgabe vollständig an deine kreative Vision an."}, "feature5": {"title": "🚀 Immer aktuell mit den neuesten Trends", "content": "Kontinuierlich aktualisiert mit neuen KI-Modellen, die aktuelle Trends in Anime, Manga, Manhwa und digitalen Comics widerspiegeln. So bleiben deine Kreationen auf dem neuesten Stand der beliebtesten Kunststile."}, "feature6": {"title": "🤝 Asset-Management & Community-Zusammenarbeit", "content": "<PERSON><PERSON><PERSON><PERSON>, organisiere und teile deine Anime- und Comic-Kunst sicher. Schließe dich kollaborativen kreativen Projekten an oder präsentiere deine Arbeit einer leidenschaftlichen Community von Anime-, Manga- und Comic-Schöpfern."}}, "faqSection": {"title": "AI Playground FAQ", "description": "<PERSON><PERSON>, was du über das Generieren von Anime, Manga, Manhwa, Comics, Stickern und animierten Assets aus deinen Fotos oder Kunstwerken mit unserem hochmodernen KI-gestützten kreativen Spielplatz wissen musst."}, "faq": {"question1": "Welche Bildformate unterstützt der KI-Spielplatz?", "answer1": "Unterstützt die Eingabeformate JPG, PNG und WEBP mit hochauflösenden PNG- und JPG-Ausgaben – perfekt für Comics, Webtoons und druckfertige Kunstwerke.", "question2": "Kann die KI verschiedene Animations-, Manga- und Comic-Stile verarbeiten?", "answer2": "Ja! Die KI-Modelle dieses Spielplatzes sind für eine riesige Vielfalt von Kunststilen trainiert: Japanischer Anime, klassischer Manga, vollfarbiger Manhwa, Webtoons, Chibi-Sticker, Cel-Shading-Comics, Pixel Art und mehr.", "question3": "Wie genau ist die Stiltransformation der KI?", "answer3": "Dank fortschrittlicher neuronaler Netze, die auf verschiedene Kunststile trainiert sind, liefert unsere KI eine hochpräzise Stiltransformation für Anime, Manga und Manhwa – und erzeugt authentische, hochwertige Ergebnisse.", "question4": "Ist die Plattform für professionelle Künstler oder Studios geeignet?", "answer4": "Ja – Profi-Ausgaben, effiziente Verarbeitung, anpassbare Arbeitsabläufe und hochauflösende Downloads machen es ideal für professionelle Manga-<PERSON><PERSON><PERSON>pfer, Comiczeichner, Animationsstudios und Indie-Illustratoren.", "question5": "Welche zusätzlichen kreativen Funktionen sind verfügbar?", "answer5": "Über die Anime-Konvertierung hinaus kann<PERSON> du <PERSON>, <PERSON>rak<PERSON>bögen, Avatare, Hintergründe, Sprite Sheets, Comic-Panels, Actionfiguren und mehr generieren. Verwalte und teile deine Kreationen mit integrierten Asset-Tools und Community-Funktionen.", "question6": "Was kostet die Nutzung von AI Playground?", "answer6": "Die Umwandlung von Kunstwerken kostet 'Zaps'. Ein neues Konto erhält eine Gutschrift von 'Zaps', weitere 'Zaps' können erworben werden."}, "title": "AI Playground: In Anime, Manga, Manhwa oder Comic konvertieren", "infoTooltip": "Unterstützt Drag-and-Drop-Upload für Fotos, 3D-Renderings, Comic-Panels, Skizzen und mehr.", "styleSelection": {"label": "Kunststil auswählen"}, "button": {"convert": "Konvertieren in {{style}}", "zaps": "-{{cost}}/{{credit}}"}, "results": {"title": "<PERSON><PERSON>-Anime-, Manga-, Manhwa- & Comic-Konvertierungen", "empty": "Konvertierte Kunstwerke werden hier angezeigt. Generiere im Handumdrehen atemberaubende Anime-, Manga-, Manhwa- und Comic-Kunst!"}, "deleteModal": {"title": "Kunstwerk löschen", "message": "B<PERSON> du sicher, dass du dieses Bild löschen möchtest? Diese Aktion kann nicht rückgängig gemacht werden.", "cancel": "Abbrechen", "delete": "Löschen"}, "toast": {"fetchFailed": "Das Laden der Bilder ist fehlgeschlagen. Bitte überprüfe deine Netzwerkverbindung und versuche es erneut.", "deleteSuccess": "Kunstwerk erfolgreich gelöscht.", "deleteFailed": "Löschen fehlgeschlagen. Versuche es erneut.", "downloadFailed": "Download fehlgeschlagen. Bitte aktualisiere die Seite und versuche es erneut.", "purchaseZaps": "<PERSON>te kaufe mehr Zaps, um fortzufahren.", "conversionDone": "KI-Kunst-Konvertierung abgeschlossen!", "conversionFailed": "Konvertierung fehlgeschlagen. Bitte versuche es erneut.", "noZaps": "Unzureichende Zaps", "generateFailed": "Generierung der Animation fehlgeschlagen.", "mediaReceived": "Bild hochgeladen und bereit zur Konvertierung."}, "errors": {"generateImageFailed": "Bildgenerierung fehlgeschlagen. Versuche eine andere Eingabe.", "resourceExhausted": "Ressourcennutzung erschöpft. Bitte aktualisiere deinen Plan."}, "styles": {"anime": "Anime", "ghibliAnime": "<PERSON><PERSON><PERSON><PERSON>", "koreanManhwa": "Koreanischer Manhwa", "cartoon": "Cartoon", "manga": "Manga", "inkWash": "Tuschemalerei", "chibi": "<PERSON><PERSON>-<PERSON><PERSON>", "spriteSheet": "Sprite Sheet", "simpsons": "Simpsons", "rickAndMorty": "<PERSON> and <PERSON><PERSON><PERSON>", "southPark": "South Park", "naruto": "<PERSON><PERSON><PERSON>", "onePiece": "One Piece", "myLittlePony": "My Little Pony", "actionFigure": "Actionfigur", "figureInBox": "Figurenbox", "sticker": "<PERSON>er", "watercolor": "<PERSON><PERSON><PERSON>", "lineArt": "Strichzeichnung", "origamiPaperArt": "Origami-Papierkunst", "lego": "Lego", "lowPoly": "Low Poly", "clay": "Knetanimation", "pixelArt": "Pixel Art", "vaporwave": "Vaporwave", "cyberpunk": "Cyberpunk", "dollBox": "Figurenbox 2", "barbieDoll": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "characterSheet": "Charakterbogen", "plushie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "badge": "Abzeichen", "standee": "<PERSON><PERSON><PERSON><PERSON>", "bodyPillow": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "styleDescriptions": {"anime": "Japanischer Anime mit lebendigen Farben, dynamischer Beleuchtung und ausdrucksstarken Zügen.", "ghibliAnime": "<PERSON><PERSON><PERSON><PERSON>, s<PERSON><PERSON><PERSON>, inspiriert von Studio Ghiblis charakteristischer Animation.", "koreanManhwa": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Webtoon-inspirierter Manhwa-Stil mit sanften Farbverläufen und dramatischen Panels.", "cartoon": "Moderner Cartoon-Stil mit kräftigen Konturen und verspielten Ausdrücken.", "manga": "<PERSON><PERSON><PERSON>-weißer japanischer Manga mit filigranen Strichzeichnungen und einzigartiger Panelgestaltung.", "inkWash": "Traditionelle asiatische Tuschemalerei mit weichen Farbverläufen und zarten Pinselstrichen.", "chibi": "<PERSON><PERSON><PERSON>, stilisierte Chibi-Charaktere – perfekt für Sticker, Abzeichen und Emojis.", "simpsons": "Klassischer Simpsons-Look: gel<PERSON>, kräftige Konturen und cartoonhafte Züge.", "rickAndMorty": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, exzentrischer Look aus der berühmten Zeichentrickserie.", "southPark": "<PERSON><PERSON><PERSON>, einfache Formen mit leuchtenden Farben – eine Anspielung auf den South Park-Stil.", "naruto": "Anime inspi<PERSON><PERSON> von <PERSON><PERSON>, Farbpalette und dramatischen Effekten.", "onePiece": "Klassischer One Piece Manga/Anime-Stil – a<PERSON><PERSON><PERSON><PERSON>, energiegeladen, einzigartige Gesichtszüge.", "myLittlePony": "<PERSON><PERSON><PERSON><PERSON>, pastell<PERSON>bener und magischer Look nach dem Vorbild der My Little Pony-Serie.", "actionFigure": "Glänzende 3D-Actionfigurenkunst, ideal für Sammlerstücke und Display-Mockups.", "figureInBox": "Präsentiere deine Kunst wie eine verpackte Figur – bereit für Sammler.", "sticker": "<PERSON><PERSON><PERSON><PERSON><PERSON>, lebendiger Stil für auffällige Anime-/Comic-Sticker & Abziehbilder.", "watercolor": "<PERSON><PERSON>, sanfte Aquarellfarben für einen gemalten Märchenbucheffekt.", "lineArt": "Saubere, monochrome Strichzeichnungen – optimal für Comics oder Tattoo-Design.", "origamiPaperArt": "Geometrische Faltungen, Papiertextur – eine moderne Origami-Variante.", "lego": "Stelle Charaktere als verspielte Lego-Minifiguren dar.", "lowPoly": "<PERSON><PERSON><PERSON>, polygonaler Stil, der an klassische 3D-Animation erinnert.", "clay": "Knetanimations-Look – handgefertigt und taktil, nostalgische Animationsstimmung.", "pixelArt": "Retro-Pixeldesigns – nostalgische, spielinspirierte Optik.", "vaporwave": "Pastellfarbene, neonfarbene und retro-futuristische Stile – Vaporwave-inspiriert.", "cyberpunk": "<PERSON><PERSON><PERSON>, le<PERSON><PERSON>e, technologiegetriebene Cyberpunk-Kunst.", "dollBox": "Verpackte Figurenpräsentation, perfekt für Mockups und Sammlerstücke.", "barbieDoll": "Stylische, Barbie-inspirierte digitale Puppen, glänzend und modisch.", "characterSheet": "Charakter-Turnaround-Sheets für Animations-/Spielekünstler.", "plushie": "<PERSON><PERSON>, plüschiger Spielzeugstil für niedliche und knuddelige Kunst.", "badge": "Kreisförmiger Abzeichenstil – ideal für Avatare, Pins und Soziale Medien.", "standee": "Freistehende Ausschnittkunst, wie Anime-Convention-Merch.", "bodyPillow": "Benutzerdefinierte Kunst, die perfekt für Dakimakura-/Körperkissen formatiert ist."}}