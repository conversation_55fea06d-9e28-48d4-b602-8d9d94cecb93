{"auth": {"email": "ईमेल", "email_placeholder": "<EMAIL>", "signin_email": "ईमेल से लॉगिन करें", "signin_others": "या जारी रखें", "google": "गूगल", "error": {"title": "कुछ गलत हो गया।", "description": "आपका लॉगिन अनुरोध विफल रहा। कृपया पुनः प्रयास करें।"}, "success": {"title": "अपना ईमेल चेक करें", "description": "हमने आपको लॉगिन लिंक भेजा है। कृपया स्पैम भी चेक करें।"}, "invitation_code": "आमंत्रण कोड", "invitation_code_placeholder": "आमंत्रण कोड (वैकल्पिक)"}, "image": {"generation": {"failed": "छवि बनाने में विफल"}, "upscale": {"fetchFailed": "चित्र लाने में असफल", "deleteSuccess": "हटाने में सफलता", "deleteFailed": "हटाने में असफल", "downloadFailed": "डाउनलोड में असफल", "purchaseMoreZaps": "कृपया और जॅप्स खरीदें", "upscaleFailed": "छवि का आकार बढ़ाने में असफल", "upscaleSuccess": "छवि का आकार बढ़ाने में सफलता", "noZaps": "कोई जॅप्स उपलब्ध नहीं"}}, "text": {"generation": {"failed": "पाठ बनाने में असफल"}}, "character": {"creation": {"failed": "चरित्र बनाने में असफल", "characterLimitExceeded": "आपने सीमा पार कर ली है! अधिक चरित्र बनाने के लिए अपनी योजना अपडेट करें।"}}, "common": {"fetchFailed": "छवियाँ लाने में असफल", "deleteSuccess": "हटाने में सफलता", "deleteFailed": "हटाने में असफल", "downloadFailed": "डाउनलोड में असफल", "noZaps": "कोई जॅप्स उपलब्ध नहीं", "rateLimitExceeded": "आपने सीमा पार कर ली है! एक बार में अधिक बनाने के लिए अपनी योजना अपडेट करें।", "extractFailed": "फ्रेम निकालने में असफल", "processingFailed": "प्रक्रिया में असफल", "invalidImage": "कृपया एक मान्य चित्र फ़ाइल चुनें"}, "backgroundRemoval": {"purchaseZaps": "पृष्ठभूमि हटाने के लिए कृपया और जॅप्स खरीदें", "failed": "पृष्ठभूमि हटाने में असफल", "success": "पृष्ठभूमि सफलतापूर्वक हटाई गई", "mediaReceived": "मीडिया सफलतापूर्वक प्राप्त हुआ"}, "imageToVideo": {"purchaseMoreZaps": "कृपया और जॅप्स खरीदें", "generateSuccess": "एनीमेशन बनाने में सफलता", "generateFailed": "एनीमेशन बनाने में असफल", "resourceExhausted": "संसाधन समाप्त हो गया है", "noZaps": "कोई जॅप्स उपलब्ध नहीं", "fetchVideosFailed": "वीडियो लाने में असफल", "deleteSuccess": "हटाने में सफलता", "deleteFailed": "हटाने में असफल", "downloadFailed": "डाउनलोड में असफल"}, "lineArtColorize": {"fetchFailed": "छवियाँ लाने में असफल", "deleteSuccess": "हटाने में सफलता", "deleteFailed": "हटाने में असफल", "downloadFailed": "डाउनलोड में असफल", "purchaseMoreZaps": "कृपया और जॅप्स खरीदें", "colorizeSuccess": "चित्र को रंगने में सफलता", "colorizeFailed": "चित्र को रंगने में असफल", "noZaps": "कोई जॅप्स उपलब्ध नहीं", "createFailed": "चित्र बनाने में असफल", "mediaReceived": "मीडिया सफलतापूर्वक प्राप्त हुआ"}, "error": {"fetchImages": "छवियाँ लाने में असफल", "delete": "हटाने में असफल", "download": "डाउनलोड में असफल", "createImage": "चित्र बनाने में असफल", "noZaps": "कोई जॅप्स उपलब्ध नहीं", "insufficientZaps": "कृपया और जॅप्स खरीदें", "colorizeImage": "चित्र को रंगने में असफल", "fetchVideos": "वीडियो प्राप्त करने में विफल", "upscaleFailed": "वीडियो का अपस्केल विफल", "generateComicFailed": "कॉमिक बनाने में असफल, कृपया पुनः प्रयास करें।", "failedPost": "पोस्ट करने में असफल", "noImageError": "आगे बढ़ने के लिए कुछ चित्र बनाएं", "uploadImageFailed": "चित्र अपलोड करने में असफल", "videoInterpolationFailed": "वीडियो इंटरपोलेशन असफल", "failedToGeneratePrompt": "प्रॉम्प्ट बनाने में असफल", "invalidVideoUrl": "अमान्य वीडियो यूआरएल", "generationModelNotFound": "उत्पादन मॉडल उपलब्ध नहीं है", "generationTaskNotFound": "उत्पादन कार्य उपलब्ध नहीं है", "invalidParams": "अवैध पैरामीटर", "generationResultFailed": "उत्पादन परिणाम प्राप्त करने में असफल", "videoGenerationFailed": "अरे! वीडियो बनाना असफल रहा। आप किसी और मॉडल को आज़मा सकते हैं या अपने प्रोम्प्ट में बदलाव कर सकते हैं—कुछ मॉडल संवेदनशील सामग्री को ब्लॉक कर सकते हैं।", "imageGenerationFailed": "अरे! इमेज बनाना असफल रहा। आप किसी और मॉडल को आज़मा सकते हैं या अपने प्रोम्प्ट में बदलाव कर सकते हैं—कुछ मॉडल संवेदनशील सामग्री को ब्लॉक कर सकते हैं।", "sensitiveContent": "निर्माण विफल: इनपुट या आउटपुट संवेदनशील सामग्री के रूप में चिह्नित है। कृपया विभिन्न इनपुट के साथ पुनः प्रयास करें।", "imageExportFailed": "चित्र निर्यात करने में असफल। कृपया पुनः प्रयास करें।", "autoPostFailed": "स्वचालित पोस्ट नहीं हो सका। कृपया मैन्युअल रूप से पोस्ट करने का प्रयास करें।", "uploadSuccess": "अपलोड सफल हुआ!", "uploadFailed": "अपलोड असफल हो गया", "invalidFileType": "फ़ाइल प्रकार अमान्य है। कृपया केवल चित्र या वीडियो अपलोड करें।", "unsupportedFileType": "फाइल प्रकार समर्थित नहीं है। कृपया केवल चित्र या वीडियो अपलोड करें।", "failedToProcessFile": "फ़ाइल संसाधित करने में विफल। कृपया फिर से प्रयास करें।", "failedToPost": "पोस्ट करने में विफल। कृपया फिर से प्रयास करें।", "someImagesGenerationFailed": "कुछ छवियों का निर्माण नहीं हो पाया। कृपया पुनः प्रयास करें।"}, "success": {"delete": "हटाने में सफलता", "colorizeImage": "चित्र को रंगने में सफलता", "upscaleVideo": "वीडियो का अपस्केल सफल रहा", "downloaded_and_share": "चित्र सफलतापूर्वक डाउनलोड हुआ! इसे अब अपने दोस्तों के साथ साझा करें!", "download": "डाउनलोड में सफलता", "copy": "प्रतिलिपि में सफलता", "videoInterpolationSuccess": "वीडियो इंटरपोलेशन सफल हुआ", "publish": "प्रकाशन में सफलता", "share": "सामग्री सफलतापूर्वक साझा की गई!", "shareLinkCopied": "सामग्री क्लिपबोर्ड पर कॉपी कर ली गई है! इसे अभी अपने मित्रों के साथ साझा करें!", "uploadSuccess": "अपलोड सफल रहा!"}, "video": {"upscale": {"fetchFailed": "वीडियो लाने में असफल", "deleteSuccess": "हटाने में सफलता", "deleteFailed": "हटाने में असफल", "downloadFailed": "डाउनलोड में असफल", "purchaseMoreZaps": "कृपया और जॅप्स खरीदें", "upscaleFailed": "वीडियो का आकार बढ़ाने में असफल", "upscaleSuccess": "वीडियो का आकार बढ़ाने में सफलता!", "noZaps": "कोई जॅप्स उपलब्ध नहीं", "invalidVideoUrl": "अमान्य वीडियो URL", "uploadFailed": "अपलोड में असफल"}, "interpolation": {"success": "वीडियो इंटरपोलेशन में सफलता", "failed": "वीडियो इंटरपोलेशन में असफल"}, "styleTransfer": {"success": "वीडियो सफलतापूर्वक उत्पन्न!", "successWithTime": "वीडियो {{duration}} में उत्पन्न!", "failed": "वीडियो बनाने में असफल। कृपया पुनः प्रयास करें।", "frameExtracted": "पहला फ्रेम सफलतापूर्वक निकाला गया!", "styleApplied": "स्टाइल सफलतापूर्वक लगाया!", "referenceApplied": "संदर्भ चित्र सफलतापूर्वक लागू!", "useReferenceFailed": "संदर्भ चित्र का उपयोग करने में असफल", "trimmed": "वीडियो {{duration}} सेकंड तक ट्रिम किया गया", "processedFirstSeconds": "केवल पहले {{duration}} सेकंड का उपयोग करके वीडियो प्रोसेस किया जाएगा", "timeout": "10 मिनट के बाद वीडियो निर्माण की समय सीमा समाप्त", "processingFailed": "वीडियो प्रोसेसिंग असफल, मूल फाइल का उपयोग करेंगे", "videoDeleted": "वीडियो सफलतापूर्वक हटा दिया गया!", "styleTransferFailed": "स्टाइल ट्रांसफर असफल। कृपया पुनः प्रयास करें।", "invalidStyleResponse": "स्टाइल ट्रांसफर एपीआई से अमान्य प्रतिक्रिया", "uploadExtractedFrameFailed": "निकाला गया फ्रेम अपलोड करने में असफल", "uploadReferenceFailed": "संदर्भ चित्र अपलोड करने में असफल", "uploadVideoFailed": "वीडियो अपलोड करने में असफल", "videoGenerationFailed": "वीडियो निर्माण असफल। कृपया पुनः प्रयास करें।", "videoGenerationTimeout": "10 मिनट के बाद वीडियो निर्माण की समय सीमा समाप्त", "noPredictionId": "वीडियो निर्माण एपीआई से कोई भविष्यवाणी आईडी प्राप्त नहीं हुई", "unexpectedOutputFormat": "वीडियो निर्माण से अप्रत्याशित आउटपुट प्रारूप", "noVideoUrlFound": "निर्माण आउटपुट में कोई वीडियो URL नहीं मिला", "missingVideoOrFrame": "निर्माण के लिए वीडियो या स्टाइलिश फ्रेम गायब", "downloadFailed": "डाउनलोड में असफल। कृपया पुनः प्रयास करें।", "deleteFailed": "हटाने में असफल। कृपया पुनः प्रयास करें।", "durationWarning": "केवल पहले 5 सेकंड का प्रसंस्करण होगा।", "uploadVideoFirst": "पहले वीडियो अपलोड करें ताकि पहला फ्रेम निकाला जा सके", "extractingFrame": "वीडियो से पहला फ्रेम निकाल रहा है...", "durationInfo": "मूल वीडियो: {{original}} सेकंड, निर्माण के लिए पहले {{selected}} सेकंड का उपयोग होगा", "videoTrimmed": "निर्माण के लिए वीडियो {{duration}} सेकंड तक ट्रिम किया गया", "trimFailedUsingOriginal": "वीडियो ट्रिमिंग असफल, मूल वीडियो का उपयोग करेंगे", "videoGenerationStarted": "आपका स्टाइलिश वीडियो बनाना शुरू हुआ...", "videoGeneratedWithTime": "{{duration}} में वीडियो सफलतापूर्वक उत्पन्न!", "referenceImageCropped": "फ्रेम आयामों से मिलाने के लिए संदर्भ चित्र स्वतः क्रॉप किया गया", "autoCropFailed": "स्वतः क्रॉपिंग असफल, मूल चित्र का उपयोग कर रहा है। कृपया सुनिश्चित करें कि आपका संदर्भ फ्रेम संरचना से मेल खाता है।", "frameExtractionEmpty": "फ्रेम निष्कर्षण का परिणाम खाली है", "frameExtractionAllRetries": "सभी प्रयासों के बाद भी वीडियो से फ्रेम निकालने में विफल रहा", "retryingFrameExtraction": "फ्रेम निष्कर्षण का पुनः प्रयास किया जा रहा है (प्रयास {{attempt}})...", "safariTimeoutAdvice": "Safari टाइमआउट का पता चला। बेहतर वीडियो प्रोसेसिंग के लिए छोटे वीडियो का उपयोग करने का प्रयास करें या Chrome जैसे किसी अन्य ब्राउज़र का उपयोग करें।", "safariVideoTooLong": "वीडियो 10 सेकंड से अधिक लंबा है। कृपया अपने वीडियो को 10 सेकंड तक छोटा करें या स्वतः छोटा करने के लिए क्रोम ब्राउज़र का उपयोग करें।", "safariDurationCheckFailed": "वीडियो की अवधि की जाँच करने में विफल। कृपया पुनः प्रयास करें।"}}, "warnings": {"durationWarning": "वीडियो 5 सेकंड से लंबा है और इसे ट्रिम किया जाएगा।", "uploadVideoFirst": "कृपया पहले एक वीडियो अपलोड करें", "extractingFrame": "वीडियो से फ्रेम निकाल रहा है..."}, "talking-head": {"invalidAudio": "अमान्य ऑडियो प्रारूप। कृपया MP3 या WAV का उपयोग करें", "modelsFetchFailed": "मॉडल लाने में असफल", "login": "इस सुविधा का उपयोग करने के लिए कृपया साइन इन करें", "imageRequired": "कृपया एक चित्र चुनें", "audioRequired": "कृपया एक ऑडियो फ़ाइल चुनें", "noCredit": "पर्याप्त क्रेडिट नहीं", "success": "टॉकिंग हेड वीडियो सफलतापूर्वक उत्पन्न हुआ", "failure": "वीडियो बनाने में असफल", "audioDurationExceeded": "ऑडियो की अवधि 2 मिनट की सीमा से अधिक है। केवल पहले 2 मिनट का उपयोग किया जाएगा।", "imageTooLarge": "चित्र फ़ाइल का आकार बहुत बड़ा है। अधिकतम आकार: {{maxSize}}MB", "audioTooLarge": "ऑडियो फ़ाइल का आकार ज्यादा बड़ा है। अधिकतम आकार: {{maxSize}}MB", "filesSizeTooLarge": "फ़ाइलें बहुत बड़ी हैं। कृपया छोटी छवि और ऑडियो फ़ाइलों का उपयोग करें।", "compressingImage": "उपयुक्त अपलोड के लिए छवि को संपीड़ित किया जा रहा है...", "imageCompressionFailed": "छवि संपीड़न विफल हुआ। कृपया छोटी छवि फ़ाइल का उपयोग करें।", "requestTooLarge": "अनुरोध बहुत बड़ा है। कृपया छोटी फाइलों का उपयोग करें।"}, "info": {"compressing": "फ़ाइल को संपीड़ित किया जा रहा है...", "uploading": "फ़ाइल को अपलोड किया जा रहा है..."}}