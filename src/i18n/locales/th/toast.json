{"auth": {"email": "อีเมล", "email_placeholder": "<EMAIL>", "signin_email": "เข้าสู่ระบบด้วยอีเมล", "signin_others": "หรือดำเนินการต่อด้วย", "google": "Google", "error": {"title": "เกิดข้อผิดพลาด", "description": "คำขอเข้าสู่ระบบของคุณล้มเหลว กรุณาลองใหม่อีกครั้ง"}, "success": {"title": "โปรดตรวจสอบอีเมลของคุณ", "description": "เราได้ส่งลิงก์เพื่อเข้าสู่ระบบให้คุณแล้ว อย่าลืมตรวจสอบในกล่องจดหมายขยะด้วย"}, "invitation_code": "รหัสคำเชิญ", "invitation_code_placeholder": "รหัสคำเชิญ (ไม่บังคับ)"}, "image": {"generation": {"failed": "ไม่สามารถสร้างภาพได้"}, "upscale": {"fetchFailed": "ไม่สามารถเรียกรูปภาพได้", "deleteSuccess": "ลบเรียบร้อยแล้ว", "deleteFailed": "ลบไม่สำเร็จ", "downloadFailed": "ดาวน์โหลดไม่สำเร็จ", "purchaseMoreZaps": "กรุณาซื้อ zaps เพิ่มเติม", "upscaleFailed": "การขยายภาพไม่สำเร็จ", "upscaleSuccess": "ขยายภาพสำเร็จ", "noZaps": "ไม่มี zaps ใช้งานได้"}}, "text": {"generation": {"failed": "การสร้างข้อความล้มเหลว"}}, "character": {"creation": {"failed": "การสร้างตัวละครล้มเหลว", "characterLimitExceeded": "คุณถึงขีดจำกัดแล้ว! กรุณาอัปเกรดแผนการใช้งานเพื่อสร้างตัวละครเพิ่มเติม"}}, "common": {"fetchFailed": "ไม่สามารถเรียกรูปภาพได้", "deleteSuccess": "ลบสำเร็จ", "deleteFailed": "ลบไม่สำเร็จ", "downloadFailed": "ดาวน์โหลดไม่สำเร็จ", "noZaps": "ไม่มี zaps ใช้งานได้", "rateLimitExceeded": "คุณถึงขีดจำกัดแล้ว! กรุณาอัปเกรดแผนการใช้งานเพื่อสร้างเพิ่มในครั้งเดียว", "extractFailed": "การดึงเฟรมล้มเหลว", "processingFailed": "การประมวลผลล้มเหลว", "invalidImage": "โปรดเลือกไฟล์ภาพที่ถูกต้อง"}, "backgroundRemoval": {"purchaseZaps": "กรุณาซื้อ zaps เพิ่มเติมเพื่อใช้ในการลบพื้นหลัง", "failed": "การลบพื้นหลังล้มเหลว", "success": "การลบพื้นหลังสำเร็จ", "mediaReceived": "รับสื่อสำเร็จ"}, "imageToVideo": {"purchaseMoreZaps": "กรุณาซื้อ zaps เพิ่มเติม", "generateSuccess": "สร้างแอนิเมชันสำเร็จ", "generateFailed": "สร้างแอนิเมชันล้มเหลว", "resourceExhausted": "ทรัพยากรหมด", "noZaps": "ไม่มี zaps ใช้งานได้", "fetchVideosFailed": "ไม่สามารถเรียกวิดีโอได้", "deleteSuccess": "ลบสำเร็จ", "deleteFailed": "ลบไม่สำเร็จ", "downloadFailed": "ดาวน์โหลดไม่สำเร็จ"}, "lineArtColorize": {"fetchFailed": "ไม่สามารถเรียกรูปภาพได้", "deleteSuccess": "ลบสำเร็จ", "deleteFailed": "ลบไม่สำเร็จ", "downloadFailed": "ดาวน์โหลดไม่สำเร็จ", "purchaseMoreZaps": "กรุณาซื้อ zaps เพิ่มเติม", "colorizeSuccess": "ลงสีภาพสำเร็จ", "colorizeFailed": "ลงสีภาพล้มเหลว", "noZaps": "ไม่มี zaps ใช้งานได้", "createFailed": "การสร้างภาพล้มเหลว", "mediaReceived": "รับสื่อสำเร็จ"}, "error": {"fetchImages": "ไม่สามารถเรียกรูปภาพได้", "delete": "ลบไม่สำเร็จ", "download": "ดาวน์โหลดไม่สำเร็จ", "createImage": "การสร้างภาพล้มเหลว", "noZaps": "ไม่มี zaps ใช้งานได้", "insufficientZaps": "กรุณาซื้อ zaps เพิ่มเติม", "colorizeImage": "การทำให้ภาพมีสีล้มเหลว", "fetchVideos": "ไม่สามารถดึงข้อมูลวิดีโอได้", "upscaleFailed": "การปรับขนาดวิดีโอไม่สำเร็จ", "generateComicFailed": "การสร้างการ์ตูนล้มเหลว กรุณาลองใหม่อีกครั้ง", "failedPost": "การโพสต์ล้มเหลว", "noImageError": "เริ่มต้นด้วยการสร้างภาพสองสามภาพเพื่อดำเนินการต่อ", "uploadImageFailed": "การอัปโหลดภาพล้มเหลว", "videoInterpolationFailed": "การประมวลผลวิดีโอล้มเหลว", "failedToGeneratePrompt": "การสร้างพรอมต์ล้มเหลว", "invalidVideoUrl": "URL ของวิดีโอไม่ถูกต้อง", "generationModelNotFound": "ไม่พบโมเดลการสร้าง", "generationTaskNotFound": "ไม่พบทาสก์การสร้าง", "invalidParams": "พารามิเตอร์ไม่ถูกต้อง", "generationResultFailed": "การดึงผลลัพธ์การสร้างล้มเหลว", "videoGenerationFailed": "โอ๊ะ! การสร้างวิดีโอล้มเหลว คุณสามารถลองใช้โมเดลอื่นหรือปรับข้อความแจ้งของคุณ — บางโมเดลอาจบล็อกเนื้อหาที่อ่อนไหว", "imageGenerationFailed": "โอ๊ะ! การสร้างภาพล้มเหลว คุณสามารถลองใช้โมเดลอื่นหรือปรับข้อความแจ้งของคุณ — บางโมเดลอาจบล็อกเนื้อหาที่อ่อนไหว", "sensitiveContent": "ไม่สามารถสร้างได้: ข้อมูลที่ป้อนหรือผลลัพธ์มีเนื้อหาที่ละเอียดอ่อน กรุณาลองใหม่อีกครั้งด้วยข้อมูลที่ต่างออกไป.", "imageExportFailed": "ไม่สามารถส่งออกภาพได้ กรุณาลองใหม่อีกครั้ง", "autoPostFailed": "การโพสต์อัตโนมัติล้มเหลว กรุณาโพสต์ด้วยตัวเอง", "uploadSuccess": "อัปโหลดเรียบร้อยแล้ว!", "uploadFailed": "การอัปโหลดล้มเหลว", "invalidFileType": "ประเภทไฟล์ไม่ถูกต้อง กรุณาอัปโหลดเฉพาะรูปภาพหรือวิดีโอเท่านั้น", "unsupportedFileType": "ประเภทไฟล์นี้ไม่รองรับ กรุณาอัปโหลดเฉพาะภาพหรือวิดีโอเท่านั้น", "failedToProcessFile": "ไม่สามารถประมวลผลไฟล์ได้ กรุณาลองอีกครั้ง", "failedToPost": "ไม่สามารถโพสต์ได้ กรุณาลองอีกครั้ง", "someImagesGenerationFailed": "การสร้างรูปภาพบางรูปไม่สำเร็จ กรุณาลองใหม่อีกครั้ง"}, "success": {"delete": "ลบสำเร็จ", "colorizeImage": "การทำให้ภาพมีสีสำเร็จ", "upscaleVideo": "ปรับขนาดวิดีโอเรียบร้อย", "downloaded_and_share": "ดาวน์โหลดภาพสำเร็จ! แบ่งปันกับเพื่อนของคุณได้เลย!", "download": "ดาวน์โหลดสำเร็จ", "copy": "คัดลอกสำเร็จ", "videoInterpolationSuccess": "การประมวลผลวิดีโอสำเร็จ", "publish": "เผยแพร่สำเร็จ", "share": "แชร์เนื้อหาสำเร็จ!", "shareLinkCopied": "คัดลอกลิงก์ไปยังคลิปบอร์ดแล้ว! แชร์กับเพื่อนของคุณเลย!", "uploadSuccess": "อัปโหลดสำเร็จ!"}, "video": {"upscale": {"fetchFailed": "ไม่สามารถเรียกวิดีโอได้", "deleteSuccess": "ลบสำเร็จ", "deleteFailed": "ลบไม่สำเร็จ", "downloadFailed": "ดาวน์โหลดไม่สำเร็จ", "purchaseMoreZaps": "กรุณาซื้อ zaps เพิ่มเติม", "upscaleFailed": "การขยายวิดีโอไม่สำเร็จ", "upscaleSuccess": "ขยายวิดีโอสำเร็จ!", "noZaps": "ไม่มี zaps ใช้งานได้", "invalidVideoUrl": "URL วิดีโอไม่ถูกต้อง", "uploadFailed": "อัปโหลดไม่สำเร็จ"}, "interpolation": {"success": "แทรกวิดีโอสำเร็จ", "failed": "แทรกวิดีโอไม่สำเร็จ"}, "styleTransfer": {"success": "สร้างวิดีโอสำเร็จ!", "successWithTime": "สร้างวิดีโอเสร็จสิ้นใน {{duration}}!", "failed": "การสร้างวิดีโอล้มเหลว กรุณาลองใหม่อีกครั้ง", "frameExtracted": "ดึงเฟรมแรกสำเร็จ!", "styleApplied": "ใช้สไตล์สำเร็จ!", "referenceApplied": "ใช้ภาพอ้างอิงสำเร็จ!", "useReferenceFailed": "ไม่สามารถใช้ภาพอ้างอิงได้", "trimmed": "ตัดวิดีโอให้เหลือ {{duration}} วินาที", "processedFirstSeconds": "จะประมวลผลเพียง {{duration}} วินาทีแรกเท่านั้น", "timeout": "หมดเวลาการสร้างวิดีโอหลังจากผ่านไป 10 นาที", "processingFailed": "การประมวลผลวิดีโอล้มเหลว จะใช้ไฟล์ต้นฉบับแทน", "videoDeleted": "ลบวิดีโอสำเร็จ!", "styleTransferFailed": "การถ่ายโอนสไตล์ล้มเหลว กรุณาลองใหม่อีกครั้ง", "invalidStyleResponse": "การตอบสนองจาก API การถ่ายโอนสไตล์ไม่ถูกต้อง", "uploadExtractedFrameFailed": "การอัปโหลดเฟรมที่ดึงออกมาล้มเหลว", "uploadReferenceFailed": "การอัปโหลดภาพอ้างอิงล้มเหลว", "uploadVideoFailed": "การอัปโหลดวิดีโอล้มเหลว", "videoGenerationFailed": "การสร้างวิดีโอล้มเหลว กรุณาลองใหม่อีกครั้ง", "videoGenerationTimeout": "หมดเวลาการสร้างวิดีโอหลังจากผ่านไป 10 นาที", "noPredictionId": "ไม่พบการทำนาย ID จาก API การสร้างวิดีโอ", "unexpectedOutputFormat": "รูปแบบผลลัพธ์จากการสร้างวิดีโอไม่คาดคิด", "noVideoUrlFound": "ไม่พบ URL วิดีโอในผลลัพธ์การสร้าง", "missingVideoOrFrame": "ขาดวิดีโอหรือเฟรมที่จำเป็นสำหรับการสร้าง", "downloadFailed": "ดาวน์โหลดไม่สำเร็จ กรุณาลองใหม่อีกครั้ง", "deleteFailed": "ลบไม่สำเร็จ กรุณาลองใหม่อีกครั้ง", "durationWarning": "จะประมวลผลเฉพาะ 5 วินาทีแรกเท่านั้น", "uploadVideoFirst": "กรุณาอัปโหลดวิดีโอก่อนเพื่อดึงเฟรมแรก", "extractingFrame": "กำลังดึงเฟรมแรกจากวิดีโอ...", "durationInfo": "วิดีโอต้นฉบับ: {{original}} วินาที จะใช้แค่ {{selected}} วินาทีแรกเพื่อการสร้าง", "videoTrimmed": "ตัดวิดีโอให้เหลือ {{duration}} วินาทีเพื่อการสร้าง", "trimFailedUsingOriginal": "การตัดวิดีโอล้มเหลว ใช้วิดีโอต้นฉบับแทน", "videoGenerationStarted": "เริ่มสร้างวิดีโอตามสไตล์ของคุณ...", "videoGeneratedWithTime": "สร้างวิดีโอสำเร็จในเวลา {{duration}}!", "referenceImageCropped": "ภาพอ้างอิงถูกครอบโดยอัตโนมัติเพื่อให้เข้ากับขนาดเฟรม", "autoCropFailed": "การครอบอัตโนมัติล้มเหลว ใช้ภาพต้นฉบับแทน โปรดตรวจสอบว่าภาพอ้างอิงเหมาะสมกับขนาดเฟรม", "frameExtractionEmpty": "ไม่มีเฟรมที่ถูกสกัดออกมา", "frameExtractionAllRetries": "ไม่สามารถสกัดเฟรมจากวิดีโอได้หลังจากพยายามหลายครั้ง", "retryingFrameExtraction": "กำลังพยายามสกัดเฟรมอีกครั้ง (ครั้งที่ {{attempt}})...", "safariTimeoutAdvice": "ตรวจพบการหมดเวลาบน Safari แนะนำให้ใช้วิดีโอที่สั้นลงหรือใช้เบราว์เซอร์อื่น เช่น Chrome เพื่อประมวลผลวิดีโอให้ดียิ่งขึ้น", "safariVideoTooLong": "วิดีโอยาวเกิน 10 วินาที กรุณาตัดวิดีโอให้เหลือไม่เกิน 10 วินาที หรือใช้เบราว์เซอร์ Chrome เพื่อให้ระบบตัดให้โดยอัตโนมัติ", "safariDurationCheckFailed": "ไม่สามารถตรวจสอบระยะเวลาวิดีโอได้ กรุณาลองใหม่อีกครั้ง"}}, "warnings": {"durationWarning": "วิดีโอยาวกว่า 5 วินาทีและจะถูกตัด", "uploadVideoFirst": "กรุณาอัปโหลดวิดีโอก่อน", "extractingFrame": "กำลังดึงเฟรมจากวิดีโอ..."}, "talking-head": {"invalidAudio": "รูปแบบเสียงไม่ถูกต้อง กรุณาใช้ MP3 หรือ WAV", "modelsFetchFailed": "ไม่สามารถเรียกรุ่นได้", "login": "กรุณาลงชื่อเข้าใช้เพื่อใช้ฟีเจอร์นี้", "imageRequired": "กรุณาเลือกภาพ", "audioRequired": "กรุณาเลือกไฟล์เสียง", "noCredit": "เครดิตไม่เพียงพอ", "success": "สร้างวิดีโอตัวพูดสำเร็จ", "failure": "การสร้างวิดีโอล้มเหลว", "audioDurationExceeded": "ความยาวเสียงเกินขีดจำกัดที่ 2 นาที จะใช้เพียง 2 นาทีแรกเท่านั้น", "imageTooLarge": "ขนาดไฟล์รูปภาพใหญ่เกินไป ขนาดสูงสุด: {{maxSize}}MB", "audioTooLarge": "ขนาดไฟล์เสียงใหญ่เกินไป ขนาดสูงสุด: {{maxSize}}MB", "filesSizeTooLarge": "ขนาดไฟล์ใหญ่เกินไป กรุณาใช้ไฟล์รูปภาพและเสียงที่ขนาดเล็กกว่า", "compressingImage": "กำลังบีบอัดรูปภาพเพื่อให้ขนาดไฟล์พอดีกับการอัปโหลด...", "imageCompressionFailed": "ไม่สามารถบีบอัดรูปภาพได้ กรุณาใช้ไฟล์รูปภาพที่ขนาดเล็กกว่า", "requestTooLarge": "ขนาดคำขอใหญ่เกินไป กรุณาใช้ไฟล์ที่ขนาดเล็กกว่า"}, "info": {"compressing": "กำลังบีบอัดไฟล์...", "uploading": "กำลังอัปโหลดไฟล์..."}}