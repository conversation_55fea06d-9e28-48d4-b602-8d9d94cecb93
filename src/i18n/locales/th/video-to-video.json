{"title": "AI เปลี่ยนวิดีโอเป็นวิดีโอ", "description": "เปลี่ยนวิดีโอของคุณให้เป็นสไตล์ต่างๆ ด้วยเทคโนโลยี AI ขั้นสูงของเรา ไม่ว่าจะเป็นอนิเมะ การ์ตูน มังงะ หรือมันฮวา พร้อมคงการเคลื่อนไหวและจังหวะเดิมไว้", "meta": {"title": "AI เปลี่ยนวิดีโอเป็นวิดีโอ | สุดยอดการแปลงสไตล์วิดีโอ", "description": "เปลี่ยนวิดีโอของคุณให้เป็นสไตล์ต่างๆ ด้วยเทคโนโลยี AI ขั้นสูงของเรา ไม่ว่าจะเป็นอนิเมะ การ์ตูน มังงะ หรือมันฮวา พร้อมคงการเคลื่อนไหวและจังหวะเดิมไว้", "fullDescription": "เครื่องมือ AI สุดล้ำที่เปลี่ยนวิดีโอธรรมดาให้กลายเป็นอนิเมะ การ์ตูน มังงะ และมันฮวาที่สวยงาม เทคโนโลยีของเราคงความสอดคล้องของการเคลื่อนไหว พร้อมใส่สไตล์ศิลปะต่างๆ เช่น อนิเมะ Studio Ghibli, มันฮวาเกาหลี, มังงะญี่ปุ่น, สีน้ำ และการ์ตูน เหมาะสำหรับครีเอเตอร์ นักสร้างแอนิเมชั่น และศิลปินที่ต้องการแปลงสไตล์วิดีโอแบบมืออาชีพ", "keywords": "AI เปลี่ยนวิดีโอเป็นวิดีโอ, การแปลงสไตล์วิดีโอ, สร้างวิดีโออนิเมะ, ฟิลเตอร์วิดีโอการ์ตูน, แปลงสไตล์มังงะ, แอนิเมชั่นมันฮวา, ตัวแปลงวิดีโอเป็นอนิเมะ, เครื่องมือสร้างแอนิเมชั่น, สร้างวิดีโอการ์ตูน, วิดีโอสไตล์อนิเมะ, สร้างวิดีโอมังงะ, ปรับสไตล์วิดีโอ, เครื่องสร้างวิดีโอแอนิเมชั่น, ฟิลเตอร์วิดีโอการ์ตูน, แปลงเป็นอนิเมะ, แปลงเป็นสไตล์การ์ตูน"}, "ui": {"title": "AI เปลี่ยนวิดีโอเป็นวิดีโอ", "tooltip": "แปลงวิดีโอของคุณด้วย AI และสไตล์ต่างๆ", "generatedVideos": "วิดีโอที่สร้างแล้ว", "emptyState": "วิดีโออนิเมะและการ์ตูนของคุณจะแสดงที่นี่", "steps": {"uploadVideo": "1. อัปโหลดวิดีโอ", "styleSelection": "เลือกสไตล์", "generateVideo": "3. สร้างวิดีโอ"}, "upload": {"dragText": "แตะเพื่ออัปโหลด หรือลากวิดีโอที่นี่", "formatInfo": "รองรับ MP4, MOV, AVI • สูงสุด 5 วินาที • สูงสุด 50MB", "extractFrameManually": "เลือกเฟรมแรกเอง", "bestResultsTip": "เพื่อให้ได้ผลลัพธ์ดีที่สุด ใช้วิดีโอฉากเดียว ถ่ายต่อเนื่อง", "safariFormatInfo": "รองรับไฟล์ MP4, MOV, AVI • ความยาวไม่เกิน 10 วินาที • ขนาดไฟล์สูงสุด 50MB", "safariNotice": "ประกาศสำหรับผู้ใช้ Safari", "safariLimitWarning": "ความยาววิดีโอจำกัดที่ 10 วินาทีหรือน้อยกว่า หากต้องการควบคุมความยาววิดีโอด้วยตัวเอง กรุณาใช้เบราว์เซอร์ Chrome", "bestResultsTip1": "เพื่อให้ได้ผลลัพธ์ที่ดีที่สุด ควรใช้วิดีโอที่ถ่ายแบบต่อเนื่องในช็อตเดียว", "bestResultsTip2": "หากมีนักแสดงที่เป็นมนุษย์ เฟรมแรกควรแสดงหน้าตรง ควรจำกัดวิดีโอให้มีนักแสดงหลักเพียงคนเดียว และรวมถึงอย่างน้อยครึ่งบนของตัวเต็ม"}, "duration": {"title": "ระยะเวลา", "description": "เลือกระยะเวลาของวิดีโอ ยิ่งนานยิ่งใช้ Zaps มากขึ้น", "originalDuration": "เดิม: {{duration}} วินาที", "tooLong": "ยาวเกินไป", "willBeTrimmed": "จะตัดจาก {{original}} วินาที เหลือ {{target}} วินาที", "originalLength": "ความยาวต้นฉบับ", "safariNote": "ตรวจพบว่าคุณกำลังใช้ Safari: ควรใช้ความยาววิดีโอต้นฉบับเพื่อความเข้ากันได้ดีที่สุด", "chromeAdvice": "หากต้องการควบคุมระยะเวลาวิดีโอด้วยตัวเอง กรุณาใช้เบราว์เซอร์ Chrome", "safariUseOriginal": "สำหรับผู้ใช้ Safari: วิดีโอจะมีระยะเวลาตามต้นฉบับเพื่อความเข้ากันได้ที่ดีที่สุด"}, "videoMode": {"title": "โหมดการสร้าง", "human": "โหมดสำหรับคน", "humanDescription": "เหมาะสำหรับวิดีโอที่มีคนและภาพบุคคล", "general": "โหมดทั่วไป", "generalDescription": "ใช้ได้กับทุกวัตถุและฉาก"}, "videoPrompt": {"title": "คำแนะนำ (เพิ่มเติม)", "placeholder": "เช่น เด็กผู้หญิงอนิเมะกำลังเต้น", "description": "ใส่รายละเอียดเพิ่มเติมเพื่อแนะนำการสร้างวิดีโอ"}, "framePreview": {"original": "ต้นฉบับ", "styled": "ใส่สไตล์แล้ว", "applyingStyle": "กำลังใส่สไตล์...", "awaitingStyle": "รอใส่สไตล์", "selectStyleBelow": "เลือกสไตล์ด้านล่าง", "beforeAfterComparison": "เปรียบเทียบก่อนและหลัง", "applyingStyleToFrame": "กำลังใส่สไตล์ที่คุณเลือก...", "frameReferenceText": "เฟรมนี้ใช้อ้างอิงสำหรับการแปลงสไตล์วิดีโอ", "styleTooltip": "เฟรมที่ใส่สไตล์นี้จะนำทางการแปลงวิดีโอทั้งหมด"}, "styleModes": {"templates": "เทมเพลต", "prompt": "คำแนะนำ", "reference": "อ้างอิง"}, "styleTemplates": {"anime": "อนิเมะ", "ghibliAnime": "อนิเมะ <PERSON><PERSON><PERSON><PERSON>", "koreanManhwa": "มันฮวาเกาหลี", "cartoon": "การ์ตูน", "manga": "มังงะ", "inkWash": "ภาพหมึก", "watercolor": "สีน้ำ", "lineArt": "ลายเส้น", "lowPoly": "Low Poly", "clay": "Claymation", "pixelArt": "Pixel Art", "origami": "กระดาษพับ", "lego": "Lego", "vaporwave": "Vaporwave", "rickAndMorty": "<PERSON> and <PERSON><PERSON><PERSON>", "southPark": "South Park", "simpsons": "Simpsons", "naruto": "<PERSON><PERSON><PERSON>", "onePiece": "One Piece", "myLittlePony": "My Little Pony", "comic": "การ์ตูน", "miku": "มิกุ", "barbie": "บาร์บี้", "goku": "โกคู (ดราก้อนบอล)", "trump": "โดนัลด์ ทรัมป์", "princess": "เจ้าหญิง / เจ้าชาย", "kimono": "กิโมโน / ยูกาตะ", "superhero": "ซูเปอร์ฮีโร่", "magicalGirl": "สาวน้อยเวทมนตร์", "hogwarts": "ฮอกวอตส์", "cowboy": "คาวบอย", "sailorUniform": "ชุดเครื่องแบบนาวิกโยธิน", "pixar": "พิกซาร์", "apocalypse": "วันสิ้นโลก", "magicalWorld": "โลกมหัศจรรย์", "dreamland": "ดินแดนในฝัน", "cyberpunk": "ไซเบอร์พังค์", "kpopIdol": "ไอดอลเคป็อป", "cloud": "เมฆ", "mars": "ดาวอังคาร", "outerSpace": "อวกาศ", "sailorMoon": "เซเลอร์มูน"}, "prompt": {"placeholder": "อธิบายสไตล์ที่คุณต้องการ...", "example": "ตัวอย่าง: \"เปลี่ยนผู้ชายเป็น Naruto\", \"ทำให้เป็นอนิเมะยุค 90\", \"ให้เด็กผู้หญิงใส่ชุดลายดอกไม้\""}, "reference": {"uploadText": "อัปโหลดเฟรมที่ใส่สไตล์แล้วเพื่อใช้อ้างอิง", "formatInfo": "รองรับ JPG, PNG, JPEG, WEBP • สูงสุด 10MB", "compositionWarning": "ตรวจสอบให้แน่ใจว่าภาพอ้างอิงมีองค์ประกอบเหมือนกับเฟรมแรกของวิดีโอ"}, "buttons": {"applying": "กำลังใช้...", "useNewReference": "ใช้อ้างอิงใหม่", "applyNewStyle": "ใช้สไตล์ใหม่", "useReference": "ใช้อ้างอิง", "applyStyle": "ใช้สไตล์", "generateVideo": "สร้างวิดีโอ", "generatingVideo": "กำลังสร้างวิดีโอ...", "generateMore": "สร้างวิดีโอเพิ่มเติม", "createAnother": "สร้างวิดีโออื่น"}, "separators": {"readyToGenerate": "พร้อมสร้างวิดีโอ"}}, "whatIs": {"title": "AI เปลี่ยนวิดีโอเป็นวิดีโอ คืออะไร", "description": "AI เปลี่ยนวิดีโอเป็นวิดีโอ แปลงวิดีโอธรรมดาให้เป็นอนิเมะ การ์ตูน มังงะ และมันฮวา ด้วยเทคโนโลยีขั้นสูงของเรา เราจะใส่สไตล์ที่คุณเลือกให้กับเฟรมอ้างอิงก่อน จากนั้นใช้เฟรมนี้แปลงวิดีโอทั้งหมดของคุณ พร้อมคงการเคลื่อนไหวและจังหวะเดิมไว้ เลือกจากกว่า 20 สไตล์ เช่น อนิเมะ Studio Ghibli, มันฮวาเกาหลี, มังงะญี่ปุ่น และการ์ตูนยอดนิยม"}, "examples": {"title": "ตัวอย่าง AI เปลี่ยนวิดีโอเป็นวิดีโอ", "description": "ดูว่า AI ของเราเปลี่ยนวิดีโอให้เป็นอนิเมะ การ์ตูน มังงะ และมันฮวาที่น่าทึ่งได้อย่างไร พร้อมคงความสอดคล้องของการเคลื่อนไหว", "description1": "เปลี่ยนตัวละคร | คำสั่ง: เปลี่ยนเด็กผู้หญิงให้เป็นโดนัลด์ ทรัมป์", "description2": "เปลี่ยนตัวละคร | คำสั่ง: เปลี่ยนเด็กผู้หญิงให้เป็นเซเลอร์มูน", "description3": "เปลี่ยนแปลงสไตล์ | แปลงวิดีโอเต้นจริงให้ดูเป็นสไตล์อนิเมะ", "originalVideo": "วิดีโอต้นฉบับ", "animeVideo": "วิดีโอสไตล์อนิเมะ", "watercolorVideo": "วิดีโอสไตล์สีน้ำ", "style": "สไตล์ที่ใช้", "prompt": "ใช้คำแนะนำ", "description5": "เปลี่ยนแปลงสไตล์ | แปลงวิดีโอเต้นจริงให้ดูเป็นสไตล์การ์ตูน", "description6": "เปลี่ยนแปลงฉาก | คำสั่ง: แปลงภาพให้เห็นนักแสดงเดินในบรรยากาศไซเบอร์พังก์—สไตล์แนวไซไฟที่ล้ำสมัย", "animeStyle": "สไตล์อนิเมะ", "comicStyle": "สไตล์การ์ตูน", "promptUsed": "คำสั่งสไตล์ที่ใช้", "animeTransformation": "แปลงร่างแบบอนิเมะ", "description7": "แปลงสุนัขจริงให้เป็นสไตล์อนิเมะ แสดงถึงการเปลี่ยนสัตว์เลี้ยงที่ดูสมจริงให้กลายเป็นตัวละครในแอนิเมชัน", "description8": "มือที่กำลังถักไหมพรมแปลงเป็นสไตล์อนิเมะ แสดงถึงการรักษาความละเอียดอ่อนของการเคลื่อนไหวในกิจกรรมงานฝีมือ"}, "howTo": {"title": "วิธีใช้ AI เปลี่ยนวิดีโอเป็นวิดีโอ"}, "steps": {"step1": {"title": "อัปโหลดวิดีโอ", "content": "อัปโหลดไฟล์วิดีโอ (MP4, MOV, AVI) สูงสุด 5 วินาทีและ 50MB วิดีโอที่ยาวกว่า 5 วินาทีจะถูกตัดโดยอัตโนมัติ"}, "step2": {"title": "เลือกเฟรมอ้างอิง", "content": "เราจะเลือกเฟรมแรกและใส่สไตล์อนิเมะ การ์ตูน หรือมังงะที่คุณเลือก เพื่อใช้เป็นแนวทางในการแปลงสไตล์วิดีโอ"}, "step3": {"title": "เลือกสไตล์", "content": "เลือกจากกว่า 20 สไตล์ เช่น อนิเมะ Studio Ghibli, มันฮวาเกาหลี, มังงะญี่ปุ่น หรือสร้างสไตล์ของคุณเองด้วยคำแนะนำและภาพอ้างอิง"}, "step4": {"title": "สร้างวิดีโอ", "content": "AI ของเราจะแปลงวิดีโอทั้งหมดของคุณ โดยใช้เฟรมอ้างอิงที่ใส่สไตล์แล้ว พร้อมคงการเคลื่อนไหว การแสดงออก และจังหวะเดิมไว้"}}, "benefits": {"title": "ทำไมต้องใช้ AI เปลี่ยนวิดีโอเป็นวิดีโอ", "description": "AI ของเรามีการแปลงสไตล์วิดีโอขั้นสูง คงการเคลื่อนไหว มีสไตล์ให้เลือกหลากหลาย และราคาโปร่งใส"}, "features": {"feature1": {"title": "คงการเคลื่อนไหวได้สมบูรณ์แบบ", "content": "AI ของเราคงทุกรายละเอียดของการเคลื่อนไหว การแสดงออก และจังหวะเดิมไว้ พร้อมใส่สไตล์อนิเมะ การ์ตูน หรือมังงะ ทำให้ทุกเฟรมสอดคล้องกัน"}, "feature2": {"title": "มีสไตล์ให้เลือกกว่า 20 แบบ", "content": "เลือกจากอนิเมะ Studio Ghibli, มันฮวาเกาหลี, มังงะญี่ปุ่น, การ์ตูนดิสนีย์, สไตล์ Naruto และอีกมากมาย หรือสร้างสไตล์ของคุณเองด้วยคำแนะนำหรือภาพอ้างอิง"}, "feature3": {"title": "ผลลัพธ์คุณภาพมืออาชีพ", "content": "สร้างวิดีโออนิเมะและการ์ตูนความละเอียดสูง ด้วยการแปลงสไตล์ที่สอดคล้องกัน การเปลี่ยนภาพที่ราบรื่น และไม่มีสิ่งผิดปกติ"}, "feature4": {"title": "ระบบราคาที่ชาญฉลาด", "content": "ราคาโปร่งใส โดยคิดค่าบริการแยกสำหรับการแปลงสไตล์และการสร้างวิดีโอ ทดลองกับสไตล์ต่างๆ โดยไม่ต้องเสียค่าใช้จ่ายเพิ่มเติม"}, "feature5": {"title": "ขั้นตอนง่ายๆ สองขั้นตอน", "content": "ขั้นตอนการทำงานง่ายๆ: อัปโหลดวิดีโอ ใส่สไตล์ให้กับเฟรมอ้างอิง สร้างวิดีโอทั้งหมด ไม่จำเป็นต้องมีความเชี่ยวชาญทางเทคนิค และติดตามความคืบหน้าได้ตลอดเวลา"}, "feature6": {"title": "ปรับแต่งอัตโนมัติ", "content": "ประมวลผลวิดีโออย่างชาญฉลาด พร้อมตัดแต่งอัตโนมัติ รองรับรูปแบบ (MP4, MOV, AVI) และคำนวณค่าใช้จ่ายตามระยะเวลา"}}, "faq": {"title": "คำถามที่พบบ่อย", "description": "คำถามทั่วไปเกี่ยวกับเครื่องมือ กระบวนการ ราคา และแนวทางปฏิบัติที่ดีที่สุด", "q1": "AI เปลี่ยนวิดีโอเป็นวิดีโอ ทำงานอย่างไร", "a1": "AI ของเราใช้กระบวนการสองขั้นตอน: 1) เลือกเฟรมอ้างอิงและใส่สไตล์อนิเมะ/การ์ตูนที่คุณเลือก 2) แปลงวิดีโอทั้งหมด โดยใช้เฟรมนี้ พร้อมคงการเคลื่อนไหวและจังหวะเดิมไว้ ทำให้ทุกเฟรมสอดคล้องกัน", "q2": "รูปแบบวิดีโอที่รองรับคืออะไร", "a2": "เรารองรับ MP4, MOV และ AVI ขนาดไฟล์ไม่เกิน 50MB วิดีโอต้องมีความยาวไม่เกิน 5 วินาที และจะถูกตัดโดยอัตโนมัติหากยาวกว่านั้น", "q3": "ใช้เวลานานเท่าไหร่", "a3": "ใช้เวลา 5-10 นาที: การแปลงสไตล์ (1-3 นาที) และการสร้างวิดีโอ (3-7 นาที) คุณสามารถตรวจสอบความคืบหน้าได้ตลอดเวลา", "q4": "ค่าใช้จ่ายเท่าไหร่", "a4": "ค่าใช้จ่ายแยกสำหรับการแปลงสไตล์และการสร้างวิดีโอตามระยะเวลา ค่าใช้จ่ายจะแสดงให้เห็นก่อนประมวลผล และเครดิตจะถูกหักหลังจากเสร็จสิ้นแล้วเท่านั้น", "q5": "สร้างสไตล์เองได้ไหม", "a5": "ได้! เลือกจากกว่า 20 เทมเพลต หรือเขียนคำแนะนำ หรืออัปโหลดภาพอ้างอิง เพื่อสร้างสไตล์ที่เป็นเอกลักษณ์ของคุณ", "q6": "อะไรคือวิดีโอที่ดีสำหรับ AI นี้", "a6": "เพื่อให้ได้ผลลัพธ์ที่ดีที่สุด ควรมีวัตถุที่ชัดเจน แสงที่ดี การเคลื่อนไหวที่เสถียร และรายละเอียดที่ชัดเจน หลีกเลี่ยงการเคลื่อนไหวที่เร็ว ฟุตเทจที่มืด หรือเบลอ วิดีโอที่มีความยาวน้อยกว่า 5 วินาที และมีคนหรือวัตถุที่ชัดเจน จะได้ผลลัพธ์ที่ดีที่สุด"}}