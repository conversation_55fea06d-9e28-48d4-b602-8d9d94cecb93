{"auth": {"email": "Email", "email_placeholder": "<EMAIL>", "signin_email": "<PERSON><PERSON><PERSON> nhập bằng Email", "signin_others": "Hoặc tiếp tục với", "google": "Google", "error": {"title": "<PERSON>ó lỗi xảy ra.", "description": "<PERSON><PERSON><PERSON> cầu đăng nhập của bạn không thành công. <PERSON><PERSON> lòng thử lại."}, "success": {"title": "<PERSON><PERSON><PERSON> tra <PERSON> c<PERSON><PERSON> b<PERSON>n", "description": "<PERSON><PERSON>g tôi đã gửi cho bạn một liên kết đăng nhập. <PERSON><PERSON><PERSON> đảm bảo kiểm tra cả thư mục spam."}, "invitation_code": "Mã mời", "invitation_code_placeholder": "<PERSON><PERSON> (<PERSON><PERSON><PERSON>)"}, "image": {"generation": {"failed": "<PERSON><PERSON><PERSON><PERSON> thể tạo hình <PERSON>nh"}, "upscale": {"fetchFailed": "<PERSON><PERSON><PERSON><PERSON> thể l<PERSON>y <PERSON>nh", "deleteSuccess": "<PERSON><PERSON><PERSON> thành công", "deleteFailed": "<PERSON><PERSON><PERSON> không thành công", "downloadFailed": "<PERSON><PERSON><PERSON> xuống không thành công", "purchaseMoreZaps": "<PERSON>ui lòng mua thêm zaps", "upscaleFailed": "<PERSON><PERSON><PERSON> chất lư<PERSON><PERSON> hình <PERSON>nh không thành công", "upscaleSuccess": "<PERSON><PERSON><PERSON> cấp hình <PERSON>nh thành công", "noZaps": "Không có zaps nào có sẵn"}}, "text": {"generation": {"failed": "<PERSON><PERSON><PERSON><PERSON> thể tạo văn bản"}}, "character": {"creation": {"failed": "<PERSON><PERSON><PERSON><PERSON> thể tạo nhân vật", "characterLimitExceeded": "Bạn đã đạt đến giới hạn! Nâng cấp kế hoạch của bạn để tạo thêm nhân vật."}}, "common": {"fetchFailed": "<PERSON><PERSON><PERSON><PERSON> thể l<PERSON>y <PERSON>nh", "deleteSuccess": "<PERSON><PERSON><PERSON> thành công", "deleteFailed": "<PERSON><PERSON><PERSON> không thành công", "downloadFailed": "<PERSON><PERSON><PERSON> xuống không thành công", "noZaps": "Không có zaps nào có sẵn", "rateLimitExceeded": "Bạn đã đạt đến giới hạn! Nâng cấp kế hoạch của bạn để tạo nhiều hơn cùng lúc.", "extractFailed": "<PERSON><PERSON><PERSON><PERSON> thể trích xuất khung hình", "processingFailed": "<PERSON><PERSON> lý không thành công", "invalidImage": "<PERSON><PERSON> lòng chọn một tệp hình <PERSON>nh hợp lệ"}, "backgroundRemoval": {"purchaseZaps": "<PERSON><PERSON> lòng mua thêm zaps để sử dụng <PERSON> cụ <PERSON>ng", "failed": "<PERSON>óa phông không thành công", "success": "<PERSON><PERSON><PERSON> phông thành công", "mediaReceived": "<PERSON><PERSON><PERSON><PERSON> phư<PERSON>ng tiện thành công"}, "imageToVideo": {"purchaseMoreZaps": "<PERSON>ui lòng mua thêm zaps", "generateSuccess": "<PERSON><PERSON><PERSON> ho<PERSON>t hình thành công", "generateFailed": "<PERSON><PERSON><PERSON> ho<PERSON>t hình không thành công", "resourceExhausted": "<PERSON><PERSON><PERSON> nguyên đã bị cạn kiệt", "noZaps": "Không có zaps nào có sẵn", "fetchVideosFailed": "<PERSON><PERSON><PERSON><PERSON> thể lấy video", "deleteSuccess": "<PERSON><PERSON><PERSON> thành công", "deleteFailed": "<PERSON><PERSON><PERSON> không thành công", "downloadFailed": "<PERSON><PERSON><PERSON> xuống không thành công"}, "lineArtColorize": {"fetchFailed": "<PERSON><PERSON><PERSON><PERSON> thể l<PERSON>y <PERSON>nh", "deleteSuccess": "<PERSON><PERSON><PERSON> thành công", "deleteFailed": "<PERSON><PERSON><PERSON> không thành công", "downloadFailed": "<PERSON><PERSON><PERSON> xuống không thành công", "purchaseMoreZaps": "<PERSON>ui lòng mua thêm zaps", "colorizeSuccess": "Tô màu hình ảnh thành công", "colorizeFailed": "Tô màu hình <PERSON>nh không thành công", "noZaps": "Không có zaps nào có sẵn", "createFailed": "<PERSON><PERSON><PERSON><PERSON> thể tạo hình <PERSON>nh", "mediaReceived": "<PERSON><PERSON><PERSON><PERSON> phư<PERSON>ng tiện thành công"}, "error": {"fetchImages": "<PERSON><PERSON><PERSON><PERSON> thể l<PERSON>y <PERSON>nh", "delete": "<PERSON><PERSON><PERSON> không thành công", "download": "<PERSON><PERSON><PERSON> xuống không thành công", "createImage": "<PERSON><PERSON><PERSON><PERSON> thể tạo hình <PERSON>nh", "noZaps": "Không có zaps nào có sẵn", "insufficientZaps": "<PERSON>ui lòng mua thêm zaps", "colorizeImage": "Tô màu hình <PERSON>nh không thành công", "fetchVideos": "<PERSON><PERSON><PERSON><PERSON> thể tải video", "upscaleFailed": "<PERSON><PERSON><PERSON> cấp video không thành công", "generateComicFailed": "<PERSON><PERSON><PERSON><PERSON> thể tạo tru<PERSON> tranh, vui lòng thử lại.", "failedPost": "<PERSON><PERSON><PERSON><PERSON> thể đăng tải", "noImageError": "<PERSON><PERSON><PERSON> bắt đầu bằng việc tạo một vài hình ảnh để tiếp tục", "uploadImageFailed": "<PERSON><PERSON><PERSON> lên hình <PERSON>nh không thành công", "videoInterpolationFailed": "<PERSON><PERSON><PERSON><PERSON> thể nội suy video", "failedToGeneratePrompt": "<PERSON><PERSON><PERSON><PERSON> thể tạo gợi ý", "invalidVideoUrl": "URL video không đúng định dạng", "generationModelNotFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy mô hình tạo.", "generationTaskNotFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy nhiệm vụ tạo.", "invalidParams": "<PERSON><PERSON> s<PERSON> không hợp lệ.", "generationResultFailed": "<PERSON><PERSON><PERSON><PERSON> thể lấy kết quả tạo.", "videoGenerationFailed": "Rất tiếc! Quá trình tạo video đã thất bại. <PERSON><PERSON> lòng thử sử dụng mô hình khác hoặc điều chỉnh lời nhắc của bạn—một số mô hình có thể chặn nội dung nhạy cảm.", "imageGenerationFailed": "Rất tiếc! Quá trình tạo hình ảnh đã thất bại. <PERSON><PERSON> lòng thử sử dụng mô hình khác hoặc điều chỉnh lời nhắc của bạn—một số mô hình có thể chặn nội dung nhạy cảm.", "sensitiveContent": "Không thể tạo: Nội dung đầu vào hoặc đầu ra bị đánh dấu là nhạy cảm. <PERSON>ui lòng thử lại với nội dung khác.", "imageExportFailed": "<PERSON><PERSON><PERSON> hình <PERSON>nh không thành công. <PERSON><PERSON> lòng thử lại.", "autoPostFailed": "<PERSON><PERSON><PERSON>ng thể đăng tự động. <PERSON><PERSON> lòng thử đăng thủ công.", "uploadSuccess": "<PERSON><PERSON><PERSON> lên thành công!", "uploadFailed": "<PERSON><PERSON><PERSON><PERSON> thể tải lên", "invalidFileType": "<PERSON><PERSON><PERSON> dạng tập tin không hợp lệ. <PERSON><PERSON> lòng tải lên hình ảnh hoặc video.", "unsupportedFileType": "<PERSON>h<PERSON>ng hỗ trợ loại tệp này. <PERSON><PERSON> lòng chỉ tải lên hình ảnh hoặc video.", "failedToProcessFile": "<PERSON><PERSON><PERSON><PERSON> thể xử lý tệp. <PERSON><PERSON> lòng thử lại.", "failedToPost": "<PERSON><PERSON><PERSON><PERSON> thể đăng. <PERSON><PERSON> lòng thử lại.", "someImagesGenerationFailed": "Một số hình <PERSON>nh không thể tạo ra được. <PERSON><PERSON> lòng thử lại."}, "success": {"delete": "<PERSON><PERSON><PERSON> thành công", "colorizeImage": "Tô màu hình ảnh thành công", "upscaleVideo": "<PERSON><PERSON><PERSON> cấp video thành công", "downloaded_and_share": "Hình ảnh đã được tải xuống thành công! Chia sẻ nó với bạn bè ngay!", "download": "<PERSON><PERSON><PERSON> xu<PERSON>ng thành công", "copy": "<PERSON><PERSON> chép thành công", "videoInterpolationSuccess": "Nội suy video thành công", "publish": "<PERSON><PERSON><PERSON> bố thành công", "share": "Nội dung đã được chia sẻ thành công!", "shareLinkCopied": "Liên kết đã được sao chép vào khay nhớ tạm! Hãy chia sẻ ngay với bạn bè của bạn!", "uploadSuccess": "<PERSON><PERSON><PERSON> lên thành công!"}, "video": {"upscale": {"fetchFailed": "<PERSON><PERSON><PERSON><PERSON> thể lấy video", "deleteSuccess": "<PERSON><PERSON><PERSON> thành công", "deleteFailed": "<PERSON><PERSON><PERSON> không thành công", "downloadFailed": "<PERSON><PERSON><PERSON> xuống không thành công", "purchaseMoreZaps": "<PERSON>ui lòng mua thêm zaps", "upscaleFailed": "<PERSON><PERSON><PERSON> chất l<PERSON> video không thành công", "upscaleSuccess": "<PERSON><PERSON><PERSON> cấp video thành công!", "noZaps": "Không có zaps nào có sẵn", "invalidVideoUrl": "URL video không hợp lệ", "uploadFailed": "<PERSON><PERSON><PERSON> lên không thành công"}, "interpolation": {"success": "Nội suy video thành công", "failed": "Nội suy video không thành công"}, "styleTransfer": {"success": "Tạo video thành công!", "successWithTime": "Video được tạo thành công trong {{duration}}!", "failed": "Tạo video không thành công. <PERSON><PERSON> lòng thử lại.", "frameExtracted": "<PERSON><PERSON>g hình đầu tiên đã đư<PERSON>c trích xuất thành công!", "styleApplied": "<PERSON><PERSON> cách đã đư<PERSON>c áp dụng thành công!", "referenceApplied": "<PERSON><PERSON><PERSON> ảnh tham chiếu đã được áp dụng thành công!", "useReferenceFailed": "<PERSON><PERSON><PERSON>ng thể sử dụng hình <PERSON>nh tham chiếu", "trimmed": "Video đã đ<PERSON><PERSON><PERSON> c<PERSON>t xuống còn {{duration}}s", "processedFirstSeconds": "Video sẽ được xử lý sử dụng {{duration}}s đầu tiên", "timeout": "Tạo video v<PERSON><PERSON><PERSON> quá thời gian sau 10 phút", "processingFailed": "Xử lý video không thành công, sẽ sử dụng tệp gốc", "videoDeleted": "Video đã bị xóa thành công!", "styleTransferFailed": "Chuyển đổi phong cách không thành công. <PERSON><PERSON> lòng thử lại.", "invalidStyleResponse": "<PERSON><PERSON><PERSON> hồi không hợp lệ từ API chuyển đổi phong cách", "uploadExtractedFrameFailed": "<PERSON><PERSON><PERSON><PERSON> thể tải lên khung hình đã trích xuất", "uploadReferenceFailed": "<PERSON><PERSON><PERSON><PERSON> thể tải lên hình <PERSON>nh tham chiếu", "uploadVideoFailed": "<PERSON><PERSON><PERSON><PERSON> thể tải lên video", "videoGenerationFailed": "Tạo video không thành công. <PERSON><PERSON> lòng thử lại.", "videoGenerationTimeout": "Tạo video v<PERSON><PERSON><PERSON> quá thời gian sau 10 phút", "noPredictionId": "<PERSON><PERSON><PERSON><PERSON> nhận đư<PERSON>c ID dự đoán từ API tạo video", "unexpectedOutputFormat": "<PERSON><PERSON><PERSON> dạng đầu ra không mong đợi từ việc tạo video", "noVideoUrlFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy URL video trong kết quả tạo", "missingVideoOrFrame": "Thiếu video hoặc khung hình phong cách để tạo", "downloadFailed": "<PERSON><PERSON><PERSON> xuống không thành công. <PERSON><PERSON> lòng thử lại.", "deleteFailed": "<PERSON><PERSON><PERSON> không thành công. <PERSON><PERSON> lòng thử lại.", "durationWarning": "Chỉ xử lý 5 giây đầu tiên.", "uploadVideoFirst": "<PERSON><PERSON> lòng tải lên một video trước để trích xuất khung hình đầu tiên", "extractingFrame": "<PERSON><PERSON> trích xuất khung hình đầu tiên từ video...", "durationInfo": "Video gốc: {{original}}s, sẽ sử dụng {{selected}}s đầu tiên để tạo", "videoTrimmed": "Video đã đư<PERSON><PERSON> cắt xuống còn {{duration}}s để tạo", "trimFailedUsingOriginal": "Cắt video không thành công, sử dụng video gốc", "videoGenerationStarted": "<PERSON><PERSON><PERSON> đ<PERSON>u tạo video có phong cách của bạn...", "videoGeneratedWithTime": "Video được tạo thành công trong {{duration}}!", "referenceImageCropped": "<PERSON><PERSON><PERSON>nh tham chiếu đã được cắt tự động để phù hợp với kích thước khung hình", "autoCropFailed": "Tự động cắt không thành công, sử dụng hình ảnh gốc. <PERSON><PERSON> lòng đảm bảo tham chiếu của bạn khớp với bố cục khung hình.", "frameExtractionEmpty": "<PERSON><PERSON><PERSON> quả trích xuất khung hình bị trống", "frameExtractionAllRetries": "<PERSON><PERSON><PERSON><PERSON> thể trích xuất khung hình từ video sau mọi lần thử", "retryingFrameExtraction": "<PERSON><PERSON> thử lại việc tr<PERSON>ch xuất khung hình (lần {{attempt}})...", "safariTimeoutAdvice": "<PERSON><PERSON><PERSON> hiện thời gian chờ trên Safari. <PERSON><PERSON>y thử sử dụng một video ngắn hơn hoặc chuyển sang trình duyệt khác như Chrome để xử lý video tốt hơn.", "safariVideoTooLong": "Video dài quá 10 giây. Bạn vui lòng cắt video của mình xuống còn 10 giây hoặc sử dụng trình duyệt Chrome để tự động rút ngắn.", "safariDurationCheckFailed": "<PERSON><PERSON><PERSON><PERSON> thể kiểm tra thời lượng video. <PERSON><PERSON> lòng thử lại."}}, "talking-head": {"invalidAudio": "<PERSON>ị<PERSON> dạng âm thanh không hợp lệ. <PERSON><PERSON> lòng sử dụng MP3 hoặc WAV", "modelsFetchFailed": "<PERSON><PERSON><PERSON><PERSON> thể lấy mẫu", "login": "<PERSON>ui lòng đăng nhập để sử dụng tính năng này", "imageRequired": "<PERSON><PERSON> lòng chọn một hình <PERSON>nh", "audioRequired": "<PERSON><PERSON> lòng chọn một tệp âm thanh", "noCredit": "Không đủ tín dụng", "success": "Video nói chuyện đầu đã đư<PERSON><PERSON> tạo thành công", "failure": "<PERSON><PERSON><PERSON><PERSON> thể tạo video", "audioDurationExceeded": "Thời lượng âm thanh vượt quá giới hạn 2 phút. Chỉ sử dụng 2 phút đầu tiên.", "imageTooLarge": "<PERSON><PERSON><PERSON> hình quá lớn. <PERSON><PERSON><PERSON> thư<PERSON>c tối đa: {{maxSize}}MB", "audioTooLarge": "Tệ<PERSON> âm thanh quá lớn. <PERSON><PERSON><PERSON> thư<PERSON>c tối đa: {{maxSize}}MB", "filesSizeTooLarge": "Tệp quá lớn. <PERSON><PERSON> lòng sử dụng tệp hình và âm thanh nhỏ hơn.", "compressingImage": "<PERSON><PERSON> nén hình để tải lên tối ưu...", "imageCompressionFailed": "<PERSON><PERSON><PERSON><PERSON> thể nén hình. <PERSON>ui lòng dùng tệp hình nhỏ hơn.", "requestTooLarge": "<PERSON><PERSON><PERSON> cầu quá lớn. <PERSON><PERSON> lòng sử dụng tệp nhỏ hơn."}, "warnings": {"durationWarning": "Video dài hơn 5 gi<PERSON>y và sẽ bị cắt ngắn", "uploadVideoFirst": "<PERSON><PERSON> lòng tải lên một video trước", "extractingFrame": "<PERSON><PERSON> trích xu<PERSON>t khung hình từ video..."}, "info": {"compressing": "<PERSON><PERSON> n<PERSON> tệp...", "uploading": "<PERSON><PERSON> tải tệp lên..."}}