{"meta": {"title": "AI Playground - <PERSON><PERSON><PERSON>", "description": "Platform AI kreatif terlengkap untuk membuat anime, manga, manhwa, seni komik, dan transformasi gaya yang mulus. Hasil<PERSON>, transformasikan, dan tata gaya foto, sketsa, dan render 3D Anda dengan mudah menjadi karya seni anime atau komik berkualitas profesional menggunakan toolkit bertenaga AI tercanggih.", "keywords": "AI playground, pembuatan seni anime, transformasi gaya manga, kreasi komik manhwa, alat AI gaya komik, generator karakter anime, pembuat panel manga, AI pewarna manhwa, pembuat webtoon, konverter komik batch, pembuat avatar anime AI, generator seni stiker, animasi panel komik, transfer gaya ilustrasi, aset siap animasi, toolkit komik bertenaga AI, studio AI kreatif, platform seni anime digital"}, "hero": {"title": "AI Playground", "description": "Platform bertenaga AI all-in-one untuk seniman, pembuat manga, komikus, dan animator. <PERSON><PERSON><PERSON>, kon<PERSON><PERSON>, dan transformasikan gambar, foto, ilust<PERSON>i, dan render 3D Anda secara instan menjadi anime, manga, manhwa, komik, webtoon, dan lainnya! Manfaatkan model AI canggih untuk transformasi gaya yang mulus dan alur kerja seni profesional."}, "howItWorks": {"title": "Cara Menggunakan AI Playground", "step1": {"title": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>", "content": "Unggah materi sumber Anda (foto JPG, PNG, WEBP, render 3D, ilustrasi karakter, panel manga/komik, atau sketsa yang digambar tangan). AI playground kami menganalisis komposisi, struk<PERSON>, dan gaya visual untuk hasil transformasi yang optimal."}, "step2": {"title": "Pilih & Sesuaikan Gaya Seni", "content": "<PERSON>lih dari puluhan gaya anime, manga, manhwa, kartun, webtoon, dan seni digital seperti inkwork, cel-shading, cat air, seni piksel, atau chibi. Sesuaikan palet warna, kete<PERSON>an garis, dan elemen lainnya sesuai dengan visi Anda."}, "step3": {"title": "Transformasi Gaya Berbasis AI Instan", "content": "Biarkan model <PERSON> yang kuat secara otomatis menerapkan gaya anime, manga, atau komik yang autentik. Pratinjau dan konversi gambar Anda menggunakan jaringan saraf canggih yang dilatih pada beragam gaya seni dan konten visual."}, "step4": {"title": "Unduh, Bagikan, & <PERSON><PERSON><PERSON>", "content": "<PERSON>duh karya seni beresolusi tinggi <PERSON>, siap untuk dicetak, penerbitan komik digital, atau dibagikan di media sosial. Atur aset, buat episode webtoon, paket stiker, atau lembar karakter dengan alat manajemen yang intuitif."}}, "examples": {"title": "Contoh AI Playground", "inputAlt": "Gambar sebelum transformasi AI", "outputAlt": "Gambar set<PERSON>h transformasi AI anime/komik", "inputLabel": "<PERSON><PERSON><PERSON>", "outputLabel": "<PERSON><PERSON>i Konversi AI", "style": {"anime": "<PERSON><PERSON>", "manga": "<PERSON><PERSON>", "manhwa": "<PERSON><PERSON>", "webtoon": "Gaya Webtoon/Komik"}}, "benefits": {"title": "Mengapa Menggunakan AI Playground?", "feature1": {"title": "🎨 Transfer Gaya AI Tingkat Lanjut", "content": "<PERSON><PERSON>an saraf terdepan di industri untuk gaya anime, manga, manhwa, webtoon, dan kartun. Dapatkan transformasi gaya berkualitas tinggi, pertahankan komposisi dan emosi seni Anda sambil menambahkan seni garis, cel-shading, dan pewar<PERSON><PERSON> yang autentik."}, "feature2": {"title": "🗂️ <PERSON><PERSON><PERSON><PERSON> ya<PERSON>", "content": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, dan k<PERSON> g<PERSON>, le<PERSON><PERSON>, panel komik, atau karya seni dengan mudah. Dirancang untuk alur kerja yang efisien dan nyaman."}, "feature3": {"title": "💎 Output Berkualitas Pro, Resolusi <PERSON>", "content": "Unduh g<PERSON>, JPG, atau WEBP yang jernih dan berkualitas tinggi pada resolusi tinggi—ideal untuk produksi komik profesional, pencetakan, penerbitan digital, atau aset animasi."}, "feature4": {"title": "🖌️ Ku<PERSON><PERSON><PERSON> & Perpustakaan Seni AI Serbaguna", "content": "Je<PERSON>jahi koleksi gaya bertenaga AI yang luas: anime, manga, manhwa, kartun, chibi, action figure, seni piksel, dan banyak lagi. Sesuaikan sepenuhnya output Anda agar sesuai dengan visi kreatif Anda."}, "feature5": {"title": "🚀 <PERSON><PERSON><PERSON> dengan Tren Terbaru", "content": "<PERSON><PERSON> diperbarui dengan model <PERSON> baru yang mencerminkan tren terkini dalam anime, manga, manhwa, dan komik digital. Kreasi <PERSON>a akan selalu selaras dengan gaya seni populer."}, "feature6": {"title": "🤝 Manajemen Aset & Kolaborasi <PERSON>tas", "content": "<PERSON><PERSON><PERSON>, atur, dan bagikan seni anime dan komik Anda dengan aman. Bergabunglah dengan proyek kreatif kolaboratif atau pamerkan karya Anda kepada komunitas kreator anime, manga, dan komik yang penuh semangat."}}, "faqSection": {"title": "FAQ AI Playground", "description": "<PERSON><PERSON><PERSON> yang perlu Anda ketahui tentang menghasilkan anime, manga, manhwa, komik, stiker, dan aset animasi dari foto atau karya seni Anda menggunakan playground kreatif bertenaga AI canggih kami."}, "faq": {"question1": "Format gambar apa saja yang didukung oleh AI playground?", "answer1": "Mendukung format input JPG, PNG, dan WEB<PERSON>, dengan output PNG dan JPG resolusi tinggi — sempurna untuk komik, webtoon, dan karya seni siap cetak.", "question2": "Bisakah AI menangani berbagai gaya animasi, manga, dan komik?", "answer2": "Tentu saja! Model AI playground ini dilatih untuk berbagai macam gaya seni: anime <PERSON>pang, manga klasik, manhwa penuh warna, webtoon, stiker chibi, komik cel-shaded, seni pik<PERSON>, dan masih banyak lagi.", "question3": "Seberapa akurat transformasi gaya AI?", "answer3": "Berkat jaringan saraf canggih yang dilatih pada beragam gaya seni, AI kami memberikan transformasi gaya yang sangat akurat untuk anime, manga, dan manhwa — men<PERSON><PERSON><PERSON><PERSON> hasil yang autentik dan berkualitas tinggi.", "question4": "Apakah platform ini cocok untuk seniman atau studio profesional?", "answer4": "Ya — output tingkat pro, pem<PERSON><PERSON> e<PERSON>n, alur kerja yang dapat disesuaikan, dan unduhan resolusi tinggi menjadikannya ideal untuk pembuat manga profesional, komikus, studio animasi, dan ilustrator indie.", "question5": "Fitur kreatif tambahan apa saja yang tersedia?", "answer5": "<PERSON><PERSON> konversi anime, has<PERSON><PERSON> stiker, lembar karakter, avatar, latar belakang, lembar sprite, panel komik, action figure, dan banyak lagi. <PERSON><PERSON>la dan bagikan kreasi Anda menggunakan alat aset terintegrasi dan fitur komunitas."}, "title": "AI Playground: Konversi ke Anime, Manga, Manhwa, atau Komik", "infoTooltip": "Mendukung unggahan drag-and-drop untuk foto, render 3D, panel komik, sketsa, dan la<PERSON>ya.", "styleSelection": {"label": "<PERSON><PERSON><PERSON>"}, "button": {"convert": "Kon<PERSON>i ke {{style}}", "zaps": "-{{cost}}/{{credit}}"}, "results": {"title": "Konversi Anime, Manga, Manhwa & Komik AI Anda", "empty": "<PERSON>rya seni yang dikonversi akan muncul di sini. Hasilkan seni anime, manga, manhwa, dan komik yang menakjubkan secara instan!"}, "deleteModal": {"title": "<PERSON><PERSON>", "message": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus gambar ini? Tindakan ini tidak dapat dibatalkan.", "cancel": "<PERSON><PERSON>", "delete": "Hapus"}, "toast": {"fetchFailed": "Gagal memuat gambar. Harap periksa koneksi internet Anda dan coba lagi.", "deleteSuccess": "<PERSON><PERSON> seni ber<PERSON>.", "deleteFailed": "Penghapusan gagal. Coba lagi.", "downloadFailed": "<PERSON><PERSON><PERSON> gagal. <PERSON><PERSON> se<PERSON>kan halaman dan coba lagi.", "purchaseZaps": "Harap beli lebih banyak zaps untuk melanjutkan.", "conversionDone": "Konversi seni AI selesai!", "conversionFailed": "Konversi gagal. Harap coba lagi.", "noZaps": "Zaps tidak men<PERSON>ku<PERSON>", "generateFailed": "<PERSON><PERSON> an<PERSON>.", "mediaReceived": "<PERSON><PERSON><PERSON> dan siap untuk dikonversi."}, "errors": {"generateImageFailed": "Pembuatan gambar gagal. Coba input lain.", "resourceExhausted": "Penggunaan sumber daya habis. Harap tingkatkan paket Anda."}, "styles": {"anime": "Anime", "ghibliAnime": "<PERSON><PERSON>", "koreanManhwa": "Manhwa Korea", "cartoon": "<PERSON><PERSON><PERSON>", "manga": "Manga", "inkWash": "Lukis<PERSON><PERSON>", "chibi": "<PERSON><PERSON><PERSON>", "spriteSheet": "Sprite Sheet", "simpsons": "Simpsons", "rickAndMorty": "<PERSON> and <PERSON><PERSON><PERSON>", "southPark": "South Park", "naruto": "<PERSON><PERSON><PERSON>", "onePiece": "One Piece", "myLittlePony": "My Little Pony", "actionFigure": "Action Figure", "figureInBox": "Figure Box", "sticker": "Stiker", "watercolor": "Cat Air", "lineArt": "Line Art", "origamiPaperArt": "Seni Kertas Origami", "lego": "Lego", "lowPoly": "Low Poly", "clay": "Claymation", "pixelArt": "Pixel Art", "vaporwave": "Vaporwave", "cyberpunk": "Cyberpunk", "dollBox": "Figure Box 2", "barbieDoll": "<PERSON><PERSON>", "characterSheet": "<PERSON><PERSON><PERSON>", "plushie": "<PERSON><PERSON><PERSON>", "badge": "Lencana", "standee": "Standee", "bodyPillow": "Bantal <PERSON>"}, "styleDescriptions": {"anime": "Anime <PERSON> dengan warna-warna cerah, pen<PERSON><PERSON><PERSON> dinami<PERSON>, dan fitur ekspresif.", "ghibliAnime": "Gaya mendetail dan unik yang terinspirasi oleh animasi khas Studio Ghibli.", "koreanManhwa": "Manhwa penuh warna yang terinspirasi webtoon dengan gradien halus dan panel dramatis.", "cartoon": "Gaya kartun modern dengan garis luar tebal dan ekspresi lucu.", "manga": "<PERSON>ga <PERSON>g hitam putih dengan garis yang rumit dan panel yang unik.", "inkWash": "<PERSON><PERSON>i tinta tradisional Asia, grad<PERSON> le<PERSON>, dan sapuan kuas yang halus.", "chibi": "<PERSON><PERSON><PERSON> chibi bergaya dan imut — cocok untuk stiker, le<PERSON><PERSON>, dan emoji.", "simpsons": "Tampilan Simpsons klasik: warn<PERSON> kulit kuni<PERSON>, garis luar tebal, dan fitur kartun.", "rickAndMorty": "<PERSON><PERSON><PERSON> yang sangat bergaya dan eksentrik dari serial animasi terkenal.", "southPark": "Bentuk datar dan se<PERSON><PERSON> dengan warna-warna cerah — terinspirasi dari gaya South Park.", "naruto": "Anime yang terinspirasi oleh des<PERSON> ka<PERSON>, palet warna, dan efek dramatis Naruto.", "onePiece": "Gaya manga/anime One Piece klasik — pet<PERSON><PERSON>, ener<PERSON><PERSON>, dengan fitur wajah yang unik.", "myLittlePony": "Visual unik, pastel, dan aja<PERSON> yang dimodelkan setelah seri My <PERSON>.", "actionFigure": "Seni action figure 3D mengkilap, ideal untuk koleksi dan mockup tampilan.", "figureInBox": "Pamerkan seni Anda seperti figur dalam kotak — cocok untuk kolektor.", "sticker": "<PERSON>a tebal dan jelas untuk stiker & stiker anime/komik yang menarik perhatian.", "watercolor": "Cat air lembut dan halus untuk efek buku cerita yang dilukis.", "lineArt": "<PERSON>i garis monokrom bersih — optimal untuk komik atau desain tato.", "origamiPaperArt": "Lipatan geometris, tekstur kertas — sentuhan origami modern.", "lego": "Bayangkan kembali karakter sebagai figur mini Lego yang lucu.", "lowPoly": "Gaya poligonal dan balok-balok yang mengingatkan pada animasi 3D klasik.", "clay": "<PERSON><PERSON><PERSON> Claymation — buatan tangan dan taktil, dengan nuansa animasi nostalgia.", "pixelArt": "<PERSON><PERSON> p<PERSON> retro — visual nostalgia yang terinspirasi dari game.", "vaporwave": "Gaya pastel, neon, dan retro-futuris — terinspirasi vaporwave.", "cyberpunk": "<PERSON>i cyberpunk gelap, be<PERSON><PERSON><PERSON>, dan digerakkan oleh teknologi.", "dollBox": "Presentasi figur dalam kotak, sempurna untuk mockup dan koleksi.", "barbieDoll": "Boneka digital yang terinspirasi Barbie yang berga<PERSON>, <PERSON><PERSON><PERSON><PERSON>, dan modis.", "characterSheet": "Lembar turnaround karakter untuk seniman animasi/game.", "plushie": "<PERSON>a mainan mewah yang lembut untuk seni yang lucu dan menggemaskan.", "badge": "<PERSON><PERSON> lenc<PERSON> me<PERSON> — bagus untuk avatar, pin, dan media sosial.", "standee": "Seni potongan yang berdiri bebas, seperti merchandise konvensi anime.", "bodyPillow": "Seni khusus yang diformat sempurna untuk dakimakura/bantal tubuh."}}