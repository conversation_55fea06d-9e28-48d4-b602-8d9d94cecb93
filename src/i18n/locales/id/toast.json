{"auth": {"email": "Email", "email_placeholder": "<EMAIL>", "signin_email": "<PERSON><PERSON><PERSON>", "signin_others": "Atau lanju<PERSON> dengan", "google": "Google", "error": {"title": "<PERSON><PERSON><PERSON><PERSON>.", "description": "<PERSON><PERSON><PERSON><PERSON> masuk <PERSON> gagal. <PERSON>lakan coba lagi."}, "success": {"title": "Cek email Anda", "description": "Tautan masuk telah kami kirimkan. Jangan lupa periksa folder spam juga."}, "invitation_code": "<PERSON><PERSON>", "invitation_code_placeholder": "<PERSON><PERSON> (Opsional)"}, "image": {"generation": {"failed": "<PERSON><PERSON> gambar"}, "upscale": {"fetchFailed": "Gagal mengambil gambar", "deleteSuccess": "<PERSON><PERSON><PERSON><PERSON>", "deleteFailed": "<PERSON><PERSON>", "downloadFailed": "<PERSON><PERSON>", "purchaseMoreZaps": "<PERSON>lakan beli lebih banyak 'zaps'", "upscaleFailed": "<PERSON><PERSON> memperbesar ukuran gambar", "upscaleSuccess": "<PERSON><PERSON><PERSON><PERSON> memperbesar ukuran gambar", "noZaps": "Tidak ada 'zaps' yang tersedia"}}, "text": {"generation": {"failed": "<PERSON><PERSON> teks"}}, "character": {"creation": {"failed": "<PERSON><PERSON> menciptakan karakter", "characterLimitExceeded": "Batas telah tercapai! Upgrade ke paket yang lebih tinggi untuk membuat lebih banyak karakter."}}, "common": {"fetchFailed": "Gagal mengambil gambar", "deleteSuccess": "<PERSON><PERSON><PERSON><PERSON>", "deleteFailed": "<PERSON><PERSON>", "downloadFailed": "<PERSON><PERSON>", "noZaps": "Tidak ada 'zaps' yang tersedia", "rateLimitExceeded": "Anda telah mencapai batas! Upgrade ke paket yang lebih tinggi untuk menghasilkan lebih banyak.", "extractFailed": "<PERSON><PERSON> frame", "processingFailed": "<PERSON><PERSON><PERSON><PERSON> gagal", "invalidImage": "<PERSON>lakan pilih file gambar yang valid"}, "backgroundRemoval": {"purchaseZaps": "<PERSON>lakan beli lebih banyak 'zaps' untuk menggunakan Penghapus Latar Belakang", "failed": "Penghapusan latar belakang gagal", "success": "Penghapusan latar belakang berhasil", "mediaReceived": "Media berhasil diterima"}, "imageToVideo": {"purchaseMoreZaps": "<PERSON>lakan beli lebih banyak 'zaps'", "generateSuccess": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "generateFailed": "<PERSON><PERSON><PERSON> gagal <PERSON>", "resourceExhausted": "Sumber daya telah habis", "noZaps": "Tidak ada 'zaps' yang tersedia", "fetchVideosFailed": "<PERSON><PERSON> video", "deleteSuccess": "<PERSON><PERSON><PERSON><PERSON>", "deleteFailed": "<PERSON><PERSON>", "downloadFailed": "<PERSON><PERSON>"}, "lineArtColorize": {"fetchFailed": "Gagal mengambil gambar", "deleteSuccess": "<PERSON><PERSON><PERSON><PERSON>", "deleteFailed": "<PERSON><PERSON>", "downloadFailed": "<PERSON><PERSON>", "purchaseMoreZaps": "<PERSON>lakan beli lebih banyak 'zaps'", "colorizeSuccess": "<PERSON><PERSON><PERSON><PERSON><PERSON> gambar ber<PERSON>", "colorizeFailed": "<PERSON><PERSON><PERSON><PERSON><PERSON> gambar gagal", "noZaps": "Tidak ada 'zaps' yang tersedia", "createFailed": "<PERSON>l membuat gambar", "mediaReceived": "Media berhasil diterima"}, "error": {"fetchImages": "Gagal mengambil gambar", "delete": "<PERSON><PERSON>", "download": "<PERSON><PERSON>", "createImage": "<PERSON>l membuat gambar", "noZaps": "Tidak ada 'zaps' yang tersedia", "insufficientZaps": "<PERSON>lakan beli lebih banyak 'zaps'", "colorizeImage": "<PERSON><PERSON><PERSON><PERSON><PERSON> gambar gagal", "fetchVideos": "<PERSON><PERSON> video", "upscaleFailed": "Peningkatan resolusi video gagal", "generateComicFailed": "<PERSON><PERSON> komik, silakan coba lagi.", "failedPost": "Gagal memposting", "noImageError": "Mari mulai dengan membuat beberapa gambar untuk melanjutkan", "uploadImageFailed": "Gagal mengunggah gambar", "videoInterpolationFailed": "Interpolasi video tidak berhasil", "failedToGeneratePrompt": "<PERSON><PERSON>", "invalidVideoUrl": "URL video tidak sah", "generationModelNotFound": "Model pembuatan tidak ditemukan", "generationTaskNotFound": "Tugas pembuatan tidak ditemukan", "invalidParams": "Parameter tidak sah", "generationResultFailed": "Gagal mendapatkan hasil pembuatan", "videoGenerationFailed": "Oops! Pembuatan video gagal. Anda bisa mencoba model yang berbeda atau mengubah prompt <PERSON><PERSON><PERSON>beberapa model mungkin memblokir konten yang sensitif.", "imageGenerationFailed": "Oops! Pembuatan gambar gagal. <PERSON>a bisa mencoba model yang berbeda atau mengubah prompt <PERSON><PERSON><PERSON>beberapa model mungkin memblokir konten yang sensitif.", "sensitiveContent": "Gagal membuat: Input atau output dianggap sensitif. Silakan coba lagi dengan input yang berbeda.", "imageExportFailed": "Ekspor gambar gagal. Silakan coba lagi.", "autoPostFailed": "Posting otomatis gagal. Silakan coba memposting secara manual.", "uploadSuccess": "Un<PERSON><PERSON> berhasil!", "uploadFailed": "<PERSON><PERSON><PERSON><PERSON> gagal", "invalidFileType": "Tipe file tidak valid. Harap unggah gambar atau video saja.", "unsupportedFileType": "Jenis file tidak didukung. <PERSON>lakan unggah gambar atau video saja.", "failedToProcessFile": "Gagal memproses file. Silakan coba lagi.", "failedToPost": "Gagal mengirim. Silakan coba lagi.", "someImagesGenerationFailed": "Beberapa pembuatan gambar gagal. <PERSON><PERSON>an coba kembali."}, "success": {"delete": "<PERSON><PERSON><PERSON><PERSON>", "colorizeImage": "<PERSON><PERSON><PERSON><PERSON><PERSON> gambar ber<PERSON>", "upscaleVideo": "Video berhasil ditingkatkan resolusinya", "downloaded_and_share": "<PERSON><PERSON><PERSON> ber<PERSON>il diunduh! Bagikan sekarang dengan teman-teman <PERSON>a!", "download": "<PERSON><PERSON><PERSON><PERSON>", "copy": "<PERSON><PERSON><PERSON><PERSON>", "videoInterpolationSuccess": "Interpolasi video be<PERSON><PERSON><PERSON>", "publish": "<PERSON><PERSON><PERSON><PERSON>", "share": "Konten berhasil di<PERSON>ikan!", "shareLinkCopied": "Konten telah disalin ke papan klip! Bagikan dengan teman-teman Anda sekarang!", "uploadSuccess": "<PERSON><PERSON><PERSON><PERSON> berhasil!"}, "video": {"upscale": {"fetchFailed": "<PERSON><PERSON> video", "deleteSuccess": "<PERSON><PERSON><PERSON><PERSON>", "deleteFailed": "<PERSON><PERSON>", "downloadFailed": "<PERSON><PERSON>", "purchaseMoreZaps": "<PERSON>lakan beli lebih banyak 'zaps'", "upscaleFailed": "<PERSON><PERSON> memperbesar ukuran video", "upscaleSuccess": "<PERSON><PERSON><PERSON><PERSON> memperbesar ukuran video!", "noZaps": "Tidak ada 'zaps' yang tersedia", "invalidVideoUrl": "URL video tidak valid", "uploadFailed": "<PERSON><PERSON>"}, "interpolation": {"success": "Interpolasi video berhasil", "failed": "Interpolasi video gagal"}, "styleTransfer": {"success": "Video berhasil dihasilkan!", "successWithTime": "Video dihasilkan dalam {{duration}}!", "failed": "Pembuatan video gagal. Silakan coba lagi.", "frameExtracted": "Frame pertama berhasil diambil!", "styleApplied": "<PERSON>a ber<PERSON> di<PERSON>!", "referenceApplied": "Gambar referensi berhasil di<PERSON>!", "useReferenceFailed": "Gagal menggunakan gambar referensi", "trimmed": "Video dipotong menjadi {{duration}} detik", "processedFirstSeconds": "Video akan diproses hanya menggunakan {{duration}} detik pertama", "timeout": "Pembuatan video melebihi batas waktu setelah 10 menit", "processingFailed": "P<PERSON>rosesan video gagal, akan menggunakan file asli", "videoDeleted": "Video berhasil dihapus!", "styleTransferFailed": "Transfer gaya gagal. <PERSON>lakan coba lagi.", "invalidStyleResponse": "Respon tidak valid dari API transfer gaya", "uploadExtractedFrameFailed": "<PERSON><PERSON> frame yang di<PERSON>bil", "uploadReferenceFailed": "<PERSON>l mengunggah gambar referensi", "uploadVideoFailed": "<PERSON><PERSON> video", "videoGenerationFailed": "Pembuatan video gagal. Silakan coba lagi.", "videoGenerationTimeout": "Pembuatan video melebihi batas waktu setelah 10 menit", "noPredictionId": "Tidak ada ID prediksi yang diterima dari API pembuatan video", "unexpectedOutputFormat": "Format keluaran yang tidak terduga dari pembuatan video", "noVideoUrlFound": "Tidak ada URL video yang ditemukan dalam keluaran pembuatan", "missingVideoOrFrame": "Kehilangan video atau frame yang bergaya untuk pembuatan", "downloadFailed": "<PERSON><PERSON><PERSON><PERSON> gagal. <PERSON>lakan coba lagi.", "deleteFailed": "<PERSON><PERSON> menghapus. Silakan coba lagi.", "durationWarning": "Hanya 5 detik pertama yang akan diproses.", "uploadVideoFirst": "<PERSON><PERSON><PERSON> un<PERSON>ah video terlebih dahulu untuk mengambil frame pertama", "extractingFrame": "Mengambil frame pertama dari video...", "durationInfo": "Video asli: {{original}} detik, hanya akan menggunakan {{selected}} detik pertama untuk pembuatan", "videoTrimmed": "Video dipotong menjadi {{duration}} detik untuk pembuatan", "trimFailedUsingOriginal": "Pemotongan video gagal, menggunakan video asli", "videoGenerationStarted": "<PERSON><PERSON><PERSON> pem<PERSON>n video bergaya <PERSON>...", "videoGeneratedWithTime": "Video berhasil dihasilkan dalam {{duration}}!", "referenceImageCropped": "Gambar referensi secara otomatis dipotong untuk sesuai dengan dimensi frame", "autoCropFailed": "Pemotongan otomatis gagal, mengg<PERSON>kan gambar asli. Pastikan referensi <PERSON>a sesuai dengan komposisi frame.", "frameExtractionEmpty": "<PERSON><PERSON> frame kosong", "frameExtractionAllRetries": "<PERSON><PERSON> mengekstraksi frame dari video set<PERSON>h semua per<PERSON>an", "retryingFrameExtraction": "Mencoba kembali ekstraksi frame (percobaan {{attempt}})...", "safariTimeoutAdvice": "Timeout pada Safari terdeteksi. Cobalah men<PERSON>unakan video yang lebih pendek atau peramban lain seperti Chrome untuk pemrosesan video yang lebih baik.", "safariVideoTooLong": "Video lebih dari 10 detik. <PERSON><PERSON><PERSON> potong video <PERSON><PERSON> 10 detik atau gunakan browser Chrome untuk pemotongan otomatis.", "safariDurationCheckFailed": "<PERSON>l memerik<PERSON> durasi video. <PERSON><PERSON>an coba lagi."}}, "warnings": {"durationWarning": "Video lebih dari 5 detik dan akan dipotong", "uploadVideoFirst": "<PERSON><PERSON><PERSON> unggah video terle<PERSON>h dahulu", "extractingFrame": "Mengambil frame dari video..."}, "talking-head": {"invalidAudio": "Format audio tidak valid. <PERSON>lakan gunakan MP3 atau WAV", "modelsFetchFailed": "<PERSON><PERSON> model", "login": "<PERSON>lakan masuk untuk menggunakan fitur ini", "imageRequired": "<PERSON><PERSON>an pilih gambar", "audioRequired": "<PERSON><PERSON>an pilih file audio", "noCredit": "Kredit tidak mencukupi", "success": "Video 'talking head' be<PERSON><PERSON><PERSON>", "failure": "<PERSON><PERSON> video", "audioDurationExceeded": "Durasi audio melebihi batas 2 menit. Hanya 2 menit pertama yang akan digunakan.", "imageTooLarge": "Ukuran file gambar terlalu besar. Ukuran maksimum: {{maxSize}}MB", "audioTooLarge": "Ukuran file audio terlalu besar. Ukuran maksimum: {{maxSize}}MB", "filesSizeTooLarge": "Ukuran file terlalu besar. Silakan gunakan file gambar dan audio yang lebih kecil.", "compressingImage": "Sedang mengompresi gambar untuk unggahan optimal...", "imageCompressionFailed": "Gagal mengompresi gambar. Si<PERSON>an gunakan file gambar yang lebih kecil.", "requestTooLarge": "Permintaan terlalu besar. Silakan gunakan file yang lebih kecil."}, "info": {"compressing": "Sedang mengompresi file...", "uploading": "Sedang mengunggah file..."}}