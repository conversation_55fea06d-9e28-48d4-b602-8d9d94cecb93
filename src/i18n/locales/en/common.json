{"copyright": "Copyright © {{currentYear}} Caffelabs. All rights reserved.", "dashboard": {"main_nav_documentation": "Documentation", "main_nav_support": "Support", "sidebar_nav_clusters": "Clusters", "sidebar_nav_billing": "Billing", "sidebar_nav_settings": "Settings", "title_text": "Create and manage clusters."}, "marketing": {"introducing": "Introducing Saasfly", "get_started": "Get Started", "price_features": "Economically efficient for production environments.", "main_nav_features": "Features", "main_nav_pricing": "Pricing", "main_nav_create_comics": "Playground", "main_nav_products": "Products", "main_nav_blog": "Blog", "main_nav_partners": "Partners", "main_nav_documentation": "Documentation", "login": "<PERSON><PERSON>", "signup": "Sign up"}, "price": {"title": "Cheap, transparent prices!", "pricing": "Pricing", "slogan": "Start at full speed !", "monthly_bill": "Monthly Billing", "annual_bill": "Annual Billing", "annual_info": "will be charged when annual", "monthly_info": "when charged monthly", "mo": "/mo", "contact": "for to contact our support team.", "contact_2": "You can test the subscriptions and won&apos;t be charged.", "faq": "Frequently Asked Questions", "faq_detail": "Explore our comprehensive FAQ to find quick answers to common inquiries. If you need further assistance, don&apos;t hesitate to contact us for personalized help.", "signup": "Sign up", "upgrade": "Upgrade", "manage_subscription": "Manage Subscription", "go_to_dashboard": "Go to dashboard"}, "nav": {"features": "Features", "ai_comic_generator": "AI Comic Generator", "ai_anime_generator": "AI Anime Generator", "anigen_competition": "AniGen Competition", "pricing": "Pricing", "faq": "FAQ", "blog": "Blog", "partners": "Partners", "menu": "<PERSON><PERSON>", "video_to_video": "Video to Video AI", "layer_splitter": "Layer Splitter", "ai-talking-head": "AI Talking Head", "professional_artists": "For Artists", "oc_maker": "OC Maker", "playground": "AI Playground"}, "footer": {"pricing": "Pricing", "ai_comic_generator": "AI Comic Generator", "logo_alt": "Komiko AI Logo", "twitter_aria": "Twitter", "twitter_alt": "Twitter", "tiktok_aria": "TikTok", "tiktok_alt": "TikTok", "ai_models": "AI Models", "ai_tools": "AI Tools", "all_tools": "All Tools", "comic_tools": "Comic Tools", "illustration_tools": "Illustration Tools", "animation_tools": "Animation Tools", "description": "Komiko is a one-stop AI platform for comics, manga, manhwa, anime and animation creation.", "learn_more": "Learn More", "languages": "Languages", "carbon_removal": "Carbon Removal", "blog": "Blog", "partners": "Partners"}, "layer_options": {"send_to_back": "Send to back", "send_backward": "Send backward", "send_forward": "Send forward", "send_to_front": "Send to front"}, "ai_tools": {"comic_generation": {"title": "Generate comics, manga and manhwa", "create_character": {"title": "Create Character", "content": "Design original characters for storytelling."}, "create_on_canvas": {"title": "Comics Canvas", "content": "Create your comics from scratch on a blank canvas."}, "ai_comic_generator": {"title": "AI Comic Generator", "content": "Enter an idea and generate a comic in seconds."}, "ai_comic_factory": {"title": "AI Comic Factory"}, "ai_manga_maker": {"title": "AI Manga Maker"}, "ai_manhwa_generator": {"title": "AI Manhwa Generator"}, "ai_manga_generator": {"title": "AI Manga Generator"}, "ai_comic_strip_generator": {"title": "AI Comic Strip Generator"}, "ai_comic_book_generator": {"title": "AI Comic Book Generator"}, "ai_comic_maker": {"title": "AI Comic Maker"}, "ai_storyboard_generator": {"title": "AI Storyboard Generator"}, "free_ai_comic_strip_maker": {"title": "Free AI Comic Strip Maker"}, "nsfw_ai_comic_generator": {"title": "NSFW AI Comic Generator"}, "ai_marvel_comic_generator": {"title": "AI Marvel Comic Generator"}, "batman_comic_ai_generator": {"title": "Batman Comic AI Generator"}, "dc_comic_ai_generator": {"title": "DC Comic AI Generator"}, "ai_porn_comic_generator": {"title": "AI Porn Comic Generator"}, "ai_erotic_comic_generator": {"title": "AI Erotic Comic Generator"}, "boomer_comic_ai_generator": {"title": "Boomer Comic AI Generator"}, "anime_manga_ai_generator": {"title": "Anime Manga AI Generator"}, "ai_webtoon_generator": {"title": "AI Webtoon Generator"}}, "illustration": {"title": "Assist with art, illustration and storyboarding", "ai_anime_generator": {"title": "Anime Art Generator", "content": "Generate anime-style images with characters."}, "line_art_colorization": {"title": "Line Art Colorization", "content": "Add colors to your line art and sketches."}, "sketch_simplification": {"title": "Sketch Simplification", "content": "Convert rough, messy sketches into clean line art."}, "background_removal": {"title": "Background Removal", "content": "Remove background from images with AI."}, "image_upscaling": {"title": "Image Upscaling", "content": "Upscale your images with AI."}, "image_relighting": {"title": "Image Relighting", "content": "Relight your images with AI."}, "photo_to_anime": {"title": "Photo to Anime", "content": "Transform your photo to anime and other styles."}, "ai_playground": {"title": "AI Playground", "content": "Convert photos to anime, manga, cartoon, and 30+ other art styles."}, "ai_action_figure_generator": {"title": "AI Action Figure Generator"}, "ai_character_sheet_generator": {"title": "AI Character Sheet Generator"}, "ai_doll_generator": {"title": "AI Doll Generator"}, "anime_ai_filter": {"title": "Anime AI Filter"}, "studio_ghibli_ai_generator": {"title": "Studio Ghibli AI Generator"}, "studio_ghibli_filter": {"title": "Studio Ghibli Filter"}, "frame_pack_ai_video_generator": {"title": "FramePack AI Video Generator"}, "vidu_ai_video_generator": {"title": "Vidu Q1 Video Generator"}, "magi_1_video_generator": {"title": "Magi-1 Video Generator"}, "moonvalley_ai_video_generator": {"title": "Moonvalley AI Video Generator"}, "marey_ai_video_generator": {"title": "Marey AI Video Generator"}, "anisora_video_generator": {"title": "AniSora AI Video Generator"}, "veo3_video_generator": {"title": "Veo3 AI Video Generator"}, "midjourney_video_generator": {"title": "Midjourney AI Video Generator"}, "ai_waifu_generator": {"title": "AI Waifu Generator"}, "ai_naruto_generator": {"title": "AI Naruto Generator"}, "random_genshin_character_generator": {"title": "Random Genshin Character Generator"}, "dragon_ball_ai_generator": {"title": "Dragon Ball AI Generator"}, "pokemon_ai_generator": {"title": "Pokemon AI Generator"}, "ai_genshin_impact_generator": {"title": "AI Genshin Impact Generator"}, "one_piece_ai_generator": {"title": "One Piece AI Generator"}, "demon_slayer_ai_generator": {"title": "Demon Slayer AI Generator"}, "attack_on_titan_ai_generator": {"title": "Attack on Titan AI Generator"}, "jujutsu_kaisen_ai_generator": {"title": "<PERSON><PERSON><PERSON> AI Generator"}, "my_hero_academia_ai_generator": {"title": "My Hero Academia AI Generator"}, "spy_x_family_ai_generator": {"title": "Spy x Family AI Generator"}, "league_of_legends_ai_generator": {"title": "League of Legends AI Generator"}, "ai_disney_pixar_generator": {"title": "AI Disney Pixar Generator"}, "marvel_ai_generator": {"title": "Marvel AI Generator"}, "sonic_ai_generator": {"title": "Sonic AI Generator"}, "ai_spiderman_generator": {"title": "AI Spiderman Generator"}, "anime_ai_generator": {"title": "Anime AI Generator"}, "anime_art_ai_generator": {"title": "Anime Art AI Generator"}, "studio_ghibli_style_ai_image_generator": {"title": "Studio Ghibli Style AI Image Generator"}, "female_character_generator": {"title": "Female Character Generator"}, "superhero_ai_generator": {"title": "Superhero AI Generator"}, "villain_maker": {"title": "<PERSON><PERSON>"}, "villain_generator": {"title": "Villain Generator"}, "fantasy_character_ai_generator": {"title": "Fantasy Character AI Generator"}, "tim_burton_character_maker": {"title": "<PERSON> Character Maker"}, "dbz_ai_generator": {"title": "DBZ AI Generator"}, "ai_elf_generator": {"title": "AI Elf Generator"}, "ai_princess_generator": {"title": "AI Princess Generator"}, "ai_vampire_generator": {"title": "AI Vampire Generator"}, "ai_pirate_generator": {"title": "AI Pirate Generator"}, "ai_couple_generator": {"title": "AI Couple Generator"}, "chibi_creator": {"title": "Chibi Creator"}, "chibi_ai_generator": {"title": "Chibi AI Generator"}, "ai_character_generator_from_text": {"title": "AI Character Generator From Text"}, "video_game_character_generator": {"title": "Video Game Character Generator"}, "ai_fantasy_art_generator": {"title": "AI Fantasy Art Generator"}, "ai_world_generator": {"title": "AI World Generator"}, "ai_landscape_generator": {"title": "AI Landscape Generator"}, "ai_map_generator": {"title": "AI Map Generator"}, "ai_village_generator": {"title": "AI Village Generator"}, "ai_mansion_builder": {"title": "AI Mansion Builder"}, "ai_castle_generator": {"title": "AI Castle Generator"}, "fantasy_island_generator": {"title": "Fantasy Island Generator"}, "city_map_maker": {"title": "City Map Maker"}, "ai_skybox_generator": {"title": "AI Skybox Generator"}, "pop_art_creator": {"title": "Pop Art Creator"}, "pop_art_generator": {"title": "Pop Art Generator"}, "ai_watercolor_generator": {"title": "AI Watercolor Generator"}, "abstract_ai_art_generator": {"title": "Abstract AI Art Generator"}, "ai_renaissance_painting_generator": {"title": "AI Renaissance Painting Generator"}, "ai_oil_painter": {"title": "AI Oil Painter"}, "ai_dark_art_generator": {"title": "AI Dark Art Generator"}, "ai_cartoon_generator_from_text_free": {"title": "AI Cartoon Generator From Text Free"}, "ai_pencil_drawing_generator": {"title": "AI Pencil Drawing Generator"}, "ai_generated_coloring_pages": {"title": "AI Generated Coloring Pages"}, "ai_generated_collager": {"title": "AI Generated Collager"}, "ai_mural_generator": {"title": "AI Mural Generator"}, "glitch_art_generator": {"title": "Glitch Art Generator"}, "ai_vaporwave_generator": {"title": "AI Vaporwave Generator"}, "ai_funko_pops_creator": {"title": "AI Funko Pops Creator"}, "ai_weapon_generator": {"title": "AI Weapon Generator"}, "sword_generator": {"title": "Sword Generator"}, "ai_monster_generator": {"title": "AI Monster Generator"}, "bratz_ai_generator": {"title": "Bratz AI Generator"}, "ai_creature_generator": {"title": "AI Creature Generator"}, "ai_muppet_generator": {"title": "AI Muppet Generator"}, "ai_clothing_generator": {"title": "AI Clothing Generator"}, "ai_jersey_generator": {"title": "AI Jersey Generator"}, "ai_t_shirt_designer": {"title": "AI T Shirt Designer"}, "anime_character_generator": {"title": "Anime Character Generator"}, "vampire_oc_maker": {"title": "Vampire OC Maker"}, "anime_oc_maker": {"title": "Anime OC Maker"}, "perchance_ai_character_generator": {"title": "Perchance AI Character Generator"}, "genshin_oc_maker": {"title": "Genshin OC Maker"}, "genshin_oc_generator": {"title": "Genshin OC Generator"}, "naruto_oc_maker": {"title": "Naruto OC Maker"}, "demon_slayer_oc_maker": {"title": "Demon Slayer OC Maker"}, "pokemon_oc_maker": {"title": "Pokemon OC Maker"}, "jjk_oc_maker": {"title": "JJK OC Maker"}, "jujutsu_kaisen_oc_maker": {"title": "<PERSON><PERSON><PERSON> OC Maker"}, "one_piece_oc_maker": {"title": "One Piece OC Maker"}, "one_piece_characters_generator": {"title": "One Piece Characters Generator"}, "dragon_ball_oc_maker": {"title": "Dragon Ball OC Maker"}, "attack_on_titan_oc_maker": {"title": "Attack on Titan OC Maker"}, "aot_oc_maker": {"title": "AOT OC Maker"}, "my_hero_academia_oc_maker": {"title": "My Hero Academia OC Maker"}, "spy_x_family_oc_maker": {"title": "Spy x Family OC Maker"}, "sonic_oc_maker": {"title": "Sonic OC Maker"}, "league_of_legends_oc_maker": {"title": "League of Legends OC Maker"}, "nsfw_oc_maker": {"title": "NSFW OC Maker"}, "nsfw_character_creator": {"title": "NSFW Character Creator"}, "disney_oc_maker": {"title": "Disney OC Maker"}, "marvel_oc_maker": {"title": "Marvel OC Maker"}, "ai_marvel_character_generator": {"title": "AI Marvel Character Generator"}, "ai_anime_porn_generator": {"title": "AI Anime Porn Generator"}, "furry_ai_generator": {"title": "Furry AI Generator"}, "ai_art_generator_nsfw": {"title": "AI Art Generator NSFW"}, "ai_image_generator_to_create_a_webtoon_story": {"title": "AI image generator to create a webtoon story"}, "anime_ai_generator_nsfw": {"title": "Anime AI Generator NSFW"}, "nsfw_anime_ai_generator": {"title": "NSFW Anime AI Generator"}, "nsfw_ai_art_generator": {"title": "NSFW AI Art Generator"}, "mlp_ai_generator": {"title": "MLP AI Generator"}, "my_little_pony_ai_generator": {"title": "My Little Pony AI Generator"}, "anime_pfp_maker": {"title": "Anime PFP Maker"}, "ai_game_asset_generator": {"title": "AI Game Asset Generator"}, "mlp_oc_maker": {"title": "MLP OC Maker"}, "my_little_pony_oc_maker": {"title": "My Little Pony OC Maker"}, "visual_novel_character_generator": {"title": "Visual Novel Character Generator"}, "random_video_game_character_generator": {"title": "Random Video Game Character Generator"}, "lego_ai_filter": {"title": "Lego AI Filter"}, "photo_to_line_art": {"title": "Photo to Line Art"}, "photo_to_simpsons": {"title": "Photo to <PERSON>"}, "naruto_ai_filter": {"title": "Naruto AI Filter"}, "watercolor_ai_filter": {"title": "Watercolor AI Filter"}, "cyberpunk_filter": {"title": "Cyberpunk Filter"}, "emote_maker": {"title": "Emote Maker"}, "ai_sprite_sheet_generator": {"title": "AI Sprite Sheet Generator"}, "ai_emoji_generator": {"title": "AI Emoji Generator"}, "ai_emoticon_generator": {"title": "AI Emoticon Generator"}, "anime_sticker_maker": {"title": "Anime Sticker Maker"}, "ai_sticker_generator": {"title": "AI Sticker Generator"}, "ai_sprite_generator": {"title": "AI Sprite Generator"}, "ai_plush_generator": {"title": "AI Plush Generator"}, "ai_badge_generator": {"title": "AI Badge Generator"}, "ai_clay_filter": {"title": "AI <PERSON>"}, "photo_to_minecraft": {"title": "Photo to Minecraft"}, "ai_style_transfer": {"title": "AI Style Transfer"}, "photo_to_pixel_art": {"title": "Photo to Pixel Art"}, "ai_cosplay_generator": {"title": "AI Cosplay Generator"}}, "animation": {"title": "Create animation videos", "image_to_animation": {"title": "Image to Animation", "content": "Generate smooth animation videos from images."}, "inbetweening": {"title": "In-betweening", "content": "Generate animation frames between keyframes."}, "layer_splitter": {"title": "Layer Splitter", "content": "Split character images into separate components=."}, "video_upscaling": {"title": "Video Upscaling", "content": "Increase the resolution of your videos."}, "video_interpolation": {"title": "Frame Interpolation", "content": "Make videos smoother by inserting extra frames."}, "ai_talking_head": {"title": "AI Talking Head", "content": "Generate animated talking characters from still images."}, "video_to_video": {"title": "Video to Video", "content": "Convert your videos to different styles with AI."}}, "more": {"title": "AND MORE", "content": "Explore our complete suite of AI tools for Comic, Illustration and Animation."}}, "gridIndex": "Grid {{index}}", "popularity": "{{count}} popularity", "roleplay": "Roleplay", "likes": "{{count}} likes", "following": "Following", "follow": "Follow", "post_card": {"following": "Following", "follow": "Follow", "the_end": "—— the end ——", "discover": "Discover", "more_stories": "more stories", "or_start": "or start", "creating_your_own": "creating your own", "comments": "Comments", "the_end_dash": "- the end -", "prompts": "Prompts", "prompt_copied": "Prompt copied to clipboard", "copy_prompt": "Copy prompt", "generate_more": "Generate more", "no_prompts": "No prompts available😢", "join_discussion": "Join the discussion!", "sharing_link_copied": "Sharing link copied! Share it with your friends now!", "image_downloaded": "Image downloaded successfully!", "download_failed": "Download image failed, please try again later.", "download_image": "Download Image", "copy_link": "Copy Link", "more": "More", "share_text": "Just found this awesome comic \"{{title}}\"! You've gotta check it out!", "delete": "Delete", "confirm_delete": "Confirm Delete", "confirm_delete_message": "Are you sure you want to delete this post?", "cancel": "Cancel", "delete_success": "Post deleted successfully", "delete_failed": "Failed to delete post"}, "uploadFile": {"tapToUpload": "Tap to upload or drag your image here", "dragAudio": "Drag audio here or click to upload", "supportedAudio": "Supports MP3, WAV", "errors": {"invalidExtension": "Please select a file with extension: {{accept}}", "selectImage": "Please select an image", "selectVideo": "Please select a video", "selectAudio": "Please select an audio file", "fileSize": "File size must be less than {{limit}}MB", "audioSize": "Audio file size must be less than {{limit}}MB", "maxDuration": "Max duration: {{duration}} minutes", "safariVideoTooLong": "For the best experience on Safari, please use videos up to 10 seconds, or try Chrome for longer videos!"}, "warnings": {"imageWillCompress": "Image exceeds {{limit}}MB and will be automatically compressed", "audioTooLarge": "Audio file exceeds {{limit}}MB - may affect processing quality"}}, "audioRecorder": {"duration": "Duration", "recording": "Recording in progress", "preparing": "Preparing...", "instructions": "Click the button below to start recording", "stop": "Stop Recording", "start": "Start Recording", "record_new": "Record New Audio"}, "home": {"create_story": {"title": "New Comic", "subtitle": "Generate images and create stories", "generate": {"title": "Generate", "description": "Generate a comic from a one-line prompt in seconds"}, "from_scratch": {"title": "From Scratch", "description": "Start with a blank canvas and let your creativity flow"}}, "create_character": {"title": "New Character", "subtitle": "Create characters for your stories"}, "create_animation": {"title": "New Roleplay"}}, "result_card": {"generating": "Generating", "video_not_supported": "Your browser does not support the video tag.", "more_ai_tools": {"title": "Explore Popular AI Tools", "description": "Expand your creative horizons with more related AI tools.", "view_more": "View More AI Tools"}, "generated_by": "Generated by", "tools": {"title": "Tools", "remove_bg": "Remove Bg", "upscale": "Upscale", "anime_style": "Restyle", "animate": "Animate", "video_upscale": "Upscale", "video_interpolate": "Interpolate", "character_sheet": "Character Sheet", "oc-maker": "OC Maker", "line_art_colorization": "Line Art Colorization", "character": "Character", "restyle": "Restyle", "add_text": "Add Text"}}, "actions": {"download": "Download", "delete": "Delete", "post": "Post", "cancel": "Cancel", "confirm": "Confirm", "share": "Share", "apply": "Apply", "applying": "Applying...", "generate": "Generate", "generating": "Generating...", "upload": "Upload", "uploading": "Uploading...", "extract": "Extract", "extracting": "Extracting..."}, "tools": {"image-animation-generator": "Image Animation Generator", "video-upscaling": "Video Upscaling", "video_upscaling": "Video Upscaling", "photo-to-anime": "Photo to Anime", "background-removal": "Background Removal", "image-upscaling": "Image Upscaling", "video-interpolation": "Video Interpolation", "video_interpolation": "Video Interpolation", "ai-character-sheet-generator": "Character Sheet Generator", "oc-maker": "OC Maker", "line_art_colorization": "Line Art Colorization", "character": "Character", "restyle": "Restyle", "layer_splitter": "Layer Splitter", "video-to-video": "Video to Video AI", "video_to_video": "Video to Video AI", "ai-talking-head": "AI Talking Head"}, "video_tabs": {"image_to_animation": "Image to Video", "video_to_video": "Video to Video", "talking_head": "Talking Head", "image_to_video_sub": "Image to Video", "character_to_video_sub": "Character to Video"}, "toasts": {"fetchFailed": "<PERSON><PERSON> failed", "deleteSuccess": "Delete success", "deleteFailed": "Delete failed", "downloadFailed": "Download failed", "processingSuccess": "Processing success", "processingFailed": "Processing failed", "uploadFailed": "Upload failed", "noVideoUrl": "No video URL provided", "noZaps": "Please purchase more zaps", "mediaReceived": "Media received from another tool", "extractFailed": "Failed to extract frame. Please try again.", "invalidResponse": "Invalid response from API", "timeout": "Operation timed out", "missingData": "Missing required data", "unexpectedFormat": "Unexpected data format"}, "create_character": "Create Character", "generate_image": "Generate Image", "generate": "Generate", "create_comic": "Create Comic", "generate_animation": "Generate Video", "logout": "Logout", "from_other_tools": "{{input_type}} received from {{tools}}", "input_types": {"image": "Image", "character": "Character", "video": "Video"}, "alt": {"generated_image": "Generated image"}, "success": {"copy": "Copied to clipboard"}, "unknown": "Unknown", "sampleImage": "Sample Image", "sampleVideo": "Sample Video", "exampleResult": "Example Result", "error": {"code": "Error Code: {{code}}", "requestId": "Request ID: {{id}}", "downloadFailed": "Download failed. Please try again."}, "loading": "Loading..."}