{"auth": {"email": "Email", "email_placeholder": "<EMAIL>", "signin_email": "Sign In with <PERSON><PERSON>", "signin_others": "Or continue with", "google": "Google", "invitation_code": "Invitation Code", "invitation_code_placeholder": "Invitation Code (Optional)", "error": {"title": "Something went wrong.", "description": "Your sign in request failed. Please try again."}, "success": {"title": "Check your email", "description": "We sent you a login link. Be sure to check your spam too."}}, "image": {"generation": {"failed": "Failed to generate image"}, "upscale": {"fetchFailed": "Fetch images failed", "deleteSuccess": "Delete success", "deleteFailed": "Delete failed", "downloadFailed": "Download failed", "purchaseMoreZaps": "Please purchase more zaps", "upscaleFailed": "Image upscaling failed", "upscaleSuccess": "Image upscale success", "noZaps": "No zaps available"}}, "video": {"upscale": {"fetchFailed": "Fetch videos failed", "deleteSuccess": "Delete success", "deleteFailed": "Delete failed", "downloadFailed": "Download failed", "purchaseMoreZaps": "Please purchase more zaps", "upscaleFailed": "Video upscaling failed", "upscaleSuccess": "Video upscaled successfully!", "noZaps": "No zaps available", "invalidVideoUrl": "Invalid video URL", "uploadFailed": "Upload failed"}, "interpolation": {"success": "Video interpolation success", "failed": "Video interpolation failed"}, "styleTransfer": {"success": "Video generated successfully!", "successWithTime": "Video generated in {{duration}}!", "failed": "Video generation failed. Please try again.", "frameExtracted": "First frame extracted successfully!", "styleApplied": "Style applied successfully!", "referenceApplied": "Reference image applied successfully!", "useReferenceFailed": "Failed to use reference image", "trimmed": "Video trimmed to {{duration}}s", "durationInfo": "Original video: {{original}}s, will use first {{selected}}s for generation", "videoTrimmed": "Video trimmed to {{duration}}s for generation", "trimFailedUsingOriginal": "Video trimming failed, using original video", "videoGenerationStarted": "Started generating your styled video...", "videoGeneratedWithTime": "Video generated successfully in {{duration}}!", "processedFirstSeconds": "Video will be processed using first {{duration}}s only", "timeout": "Video generation timed out after 10 minutes", "processingFailed": "Video processing failed, will use original file", "videoDeleted": "Video deleted successfully!", "styleTransferFailed": "Style transfer failed. Please try again.", "invalidStyleResponse": "Invalid response from style transfer API", "uploadExtractedFrameFailed": "Failed to upload extracted frame", "uploadReferenceFailed": "Failed to upload reference image", "uploadVideoFailed": "Failed to upload video", "videoGenerationFailed": "Video generation failed. Please try again.", "videoGenerationTimeout": "Video generation timed out after 10 minutes", "noPredictionId": "No prediction ID received from video generation API", "unexpectedOutputFormat": "Unexpected output format from video generation", "noVideoUrlFound": "No video URL found in generation output", "missingVideoOrFrame": "Missing video or styled frame for generation", "downloadFailed": "Download failed. Please try again.", "deleteFailed": "Delete failed. Please try again.", "uploadVideoFirst": "Please upload a video first to extract the first frame", "extractingFrame": "Extracting first frame from video...", "referenceImageCropped": "Reference image automatically cropped to match frame dimensions", "autoCropFailed": "Auto-cropping failed, using original image. Please ensure your reference matches the frame composition.", "frameExtractionEmpty": "Frame extraction result is empty", "frameExtractionAllRetries": "Failed to extract frame from video after all retries", "retryingFrameExtraction": "Retrying frame extraction (attempt {{attempt}})...", "safariTimeoutAdvice": "Safari timeout detected. Try using a shorter video or a different browser like Chrome for better video processing.", "safariVideoTooLong": "Video is longer than 10 seconds. Please trim your video to 10 seconds or use Chrome browser for auto-trimming.", "safariDurationCheckFailed": "Failed to check video duration. Please try again."}}, "text": {"generation": {"failed": "Failed to generate text"}}, "character": {"creation": {"failed": "Failed to create character", "characterLimitExceeded": "You've hit the limit! Upgrade your plan to create more characters."}}, "common": {"fetchFailed": "Fetch images failed", "deleteSuccess": "Delete success", "deleteFailed": "Delete failed", "downloadFailed": "Download failed", "noZaps": "No zaps available", "rateLimitExceeded": "You've hit the limit! Upgrade your plan to generate more at once.", "invalidImage": "Please select a valid image file", "extractFailed": "Failed to extract frame", "processingFailed": "Processing failed"}, "info": {"compressing": "Compressing file...", "uploading": "Uploading file..."}, "warnings": {"uploadVideoFirst": "Please upload a video first", "extractingFrame": "Extracting frame from video..."}, "backgroundRemoval": {"purchaseZaps": "Please purchase more zaps to use the Background Remover", "failed": "Background removal failed", "success": "Background removal successful", "mediaReceived": "Media received successfully"}, "imageToVideo": {"purchaseMoreZaps": "Please purchase more zaps", "generateSuccess": "Generate Animation success", "generateFailed": "Generate Animation failed", "resourceExhausted": "Resource has been exhausted", "noZaps": "No zaps available", "fetchVideosFailed": "Fetch videos failed", "deleteSuccess": "Delete success", "deleteFailed": "Delete failed", "downloadFailed": "Download failed"}, "lineArtColorize": {"fetchFailed": "Fetch images failed", "deleteSuccess": "Delete success", "deleteFailed": "Delete failed", "downloadFailed": "Download failed", "purchaseMoreZaps": "Please purchase more zaps", "colorizeSuccess": "Colorize image success", "colorizeFailed": "Colorize image failed", "noZaps": "No zaps available", "createFailed": "Create image failed", "mediaReceived": "Media received successfully"}, "error": {"fetchImages": "Fetch images failed", "delete": "Delete failed", "download": "Download failed", "createImage": "create image failed", "noZaps": "no zaps available", "insufficientZaps": "Please purchase more zaps", "colorizeImage": "colorize image failed", "generateComicFailed": "Failed to generate comic, please try again.", "failedPost": "Failed to post", "noImageError": "Let's start by creating a few images to move forward", "uploadImageFailed": "Upload image failed", "uploadFailed": "Upload failed", "invalidFileType": "Invalid file type. Please upload images or videos only.", "failedToGeneratePrompt": "Failed to generate prompt", "generationModelNotFound": "Generation model not found", "generationTaskNotFound": "Generation task not found", "invalidParams": "Invalid parameters", "generationResultFailed": "Failed to get generation result", "videoGenerationFailed": "Oops! Video generation failed. You can try a different model or tweak your prompt—some models may block sensitive content.", "imageGenerationFailed": "Oops! Image generation failed. You can try a different model or tweak your prompt—some models may block sensitive content.", "sensitiveContent": "Generation failed: The input or output was flagged as sensitive. Please try again with different inputs. ", "imageExportFailed": "Failed to export image. Please try again.", "unsupportedFileType": "Unsupported file type. Please upload images or videos only.", "failedToProcessFile": "Failed to process file. Please try again.", "failedToPost": "Failed to post. Please try again.", "someImagesGenerationFailed": "Some images generation failed. Please try again."}, "success": {"delete": "Delete success", "colorizeImage": "colorize image success", "downloaded_and_share": "Image downloaded successfully! Share it with your friends now!", "download": "Download success", "copy": "Copy success", "publish": "Publish success", "share": "Content shared successfully!", "shareLinkCopied": "Content copied to clipboard! Share it with your friends now!", "uploadSuccess": "Upload successful!"}, "talking-head": {"invalidAudio": "Invalid audio format. Please use MP3 or WAV", "modelsFetchFailed": "Failed to fetch models", "login": "Please sign in to use this feature", "imageRequired": "Please select an image", "audioRequired": "Please select an audio file", "noCredit": "Insufficient credits", "success": "Talking head video generated successfully", "failure": "Failed to generate video", "audioDurationExceeded": "Audio duration exceeds 2 minutes limit. Only the first 2 minutes will be used.", "imageTooLarge": "Image file too large. Maximum size: {{maxSize}}MB", "audioTooLarge": "Audio file too large. Maximum size: {{maxSize}}MB", "filesSizeTooLarge": "Files too large. Please use smaller image and audio files.", "compressingImage": "Compressing image for optimal upload...", "imageCompressionFailed": "Failed to compress image. Please use a smaller image file.", "requestTooLarge": "Request too large. Please use smaller files."}}