{"auth": {"email": "Correo electrónico", "email_placeholder": "<EMAIL>", "signin_email": "Iniciar sesión con correo electrónico", "signin_others": "O continúa con", "google": "Google", "error": {"title": "Algo salió mal.", "description": "Tu solicitud de inicio de sesión falló. Inténtalo nuevamente, por favor."}, "success": {"title": "Revisa tu correo", "description": "Te hemos enviado un enlace de inicio de sesión. Recuerda revisar también tu carpeta de spam."}, "invitation_code": "Código de invitación", "invitation_code_placeholder": "Código de invitación (Opcional)"}, "image": {"generation": {"failed": "Error al generar la imagen"}, "upscale": {"fetchFailed": "Error al recuperar las imágenes", "deleteSuccess": "Eliminación exitosa", "deleteFailed": "Error al eliminar", "downloadFailed": "Error al descargar", "purchaseMoreZaps": "Compra más zaps, por favor", "upscaleFailed": "Error al aumentar la resolución de la imagen", "upscaleSuccess": "Resolución de la imagen aumentada exitosamente", "noZaps": "No hay zaps disponibles"}}, "text": {"generation": {"failed": "Error al generar el texto"}}, "character": {"creation": {"failed": "Error al crear el personaje", "characterLimitExceeded": "¡Has alcanzado el límite! Actualiza tu plan para crear más personajes."}}, "common": {"fetchFailed": "Error al recuperar imágenes", "deleteSuccess": "Eliminación exitosa", "deleteFailed": "Error al eliminar", "downloadFailed": "Error al descargar", "noZaps": "No hay zaps disponibles", "rateLimitExceeded": "¡Has alcanzado el límite! Actualiza tu plan para generar más de una vez.", "invalidImage": "Selecciona un archivo de imagen válido, por favor", "extractFailed": "Error al extraer el fotograma", "processingFailed": "Procesamiento fallido"}, "backgroundRemoval": {"purchaseZaps": "Compra más zaps para usar el Eliminador de Fondos", "failed": "Eliminación de fondo fallida", "success": "Eliminación de fondo exitosa", "mediaReceived": "Medios recibidos exitosamente"}, "imageToVideo": {"purchaseMoreZaps": "Compra más zaps, por favor", "generateSuccess": "Generación de animación exitosa", "generateFailed": "Generación de animación fallida", "resourceExhausted": "Recurso agotado", "noZaps": "No hay zaps disponibles", "fetchVideosFailed": "Error al recuperar videos", "deleteSuccess": "Eliminación exitosa", "deleteFailed": "Error al eliminar", "downloadFailed": "Error al descargar"}, "lineArtColorize": {"fetchFailed": "Error al recuperar imágenes", "deleteSuccess": "Eliminación exitosa", "deleteFailed": "Error al eliminar", "downloadFailed": "Error al descargar", "purchaseMoreZaps": "Compra más zaps, por favor", "colorizeSuccess": "Colorización de imagen exitosa", "colorizeFailed": "Error en la colorización de la imagen", "noZaps": "No hay zaps disponibles", "createFailed": "Error al crear la imagen", "mediaReceived": "Medios recibidos exitosamente"}, "error": {"fetchImages": "Error al recuperar imágenes", "delete": "Error al eliminar", "download": "Error al descargar", "createImage": "Error al crear imagen", "noZaps": "No hay zaps disponibles", "insufficientZaps": "Compra más zaps, por favor", "colorizeImage": "Error al colorizar imagen", "fetchVideos": "Error al obtener los videos", "upscaleFailed": "Error al ampliar el video", "generateComicFailed": "Error al generar c<PERSON>, inténtalo nuevamente, por favor.", "failedPost": "<PERSON><PERSON>r al publicar", "noImageError": "Comencemos creando algunas imágenes para avanzar", "uploadImageFailed": "Error al subir imagen", "videoInterpolationFailed": "Error en la interpolación de video", "failedToGeneratePrompt": "Error al generar prompt", "invalidVideoUrl": "URL de video no válida", "generationModelNotFound": "No se encontró el modelo de generación", "generationTaskNotFound": "No se encontró la tarea de generación", "invalidParams": "Parámetros no válidos", "generationResultFailed": "Error al obtener el resultado de generación", "videoGenerationFailed": "¡Vaya! La generación de video falló. Intenta con un modelo diferente o ajusta tu solicitud; algunos modelos pueden bloquear contenido sensible.", "imageGenerationFailed": "¡Vaya! La generación de imagen falló. Intenta con un modelo diferente o ajusta tu solicitud; algunos modelos pueden bloquear contenido sensible.", "sensitiveContent": "Falló la generación: La entrada o salida se ha marcado como sensible. Por favor, inténtalo de nuevo con diferentes entradas.", "imageExportFailed": "La exportación de la imagen ha fallado. Por favor, inténtalo de nuevo.", "autoPostFailed": "Error al publicar automáticamente. Por favor, intenta publicar manualmente.", "uploadSuccess": "¡Carga exitosa!", "uploadFailed": "Error al subir.", "invalidFileType": "Tipo de archivo no válido. Por favor, sube solo imágenes o videos.", "unsupportedFileType": "Tipo de archivo no soportado. Por favor, sube solo imágenes o vídeos.", "failedToProcessFile": "Error al procesar el archivo. Por favor, inténtelo nuevamente.", "failedToPost": "Error al publicar. Por favor, inténtelo nuevamente.", "someImagesGenerationFailed": "La generación de algunas imágenes fracasó. Por favor, inténtelo nuevamente."}, "success": {"delete": "Eliminación exitosa", "colorizeImage": "Colorización de imagen exitosa", "upscaleVideo": "Video ampliado correctamente", "downloaded_and_share": "Imagen descargada exitosamente. ¡Compártela con tus amigos ahora!", "download": "<PERSON><PERSON><PERSON> exitosa", "copy": "<PERSON><PERSON> exitosa", "videoInterpolationSuccess": "Interpolación de video exitosa", "publish": "Publicación exitosa", "share": "¡Contenido compartido con éxito!", "shareLinkCopied": "¡Enlace copiado al portapapeles! ¡Compártelo con tus amigos ahora!", "uploadSuccess": "¡Subida exitosa!"}, "video": {"upscale": {"fetchFailed": "Error al recuperar videos", "deleteSuccess": "Eliminación exitosa", "deleteFailed": "Error al eliminar", "downloadFailed": "Error al descargar", "purchaseMoreZaps": "Compra más zaps, por favor", "upscaleFailed": "Error al aumentar la resolución del video", "upscaleSuccess": "¡Video mejorado con éxito!", "noZaps": "No hay zaps disponibles", "invalidVideoUrl": "URL de video no válida", "uploadFailed": "Error al subir"}, "interpolation": {"success": "Interpolación de video exitosa", "failed": "Error en la interpolación del video"}, "styleTransfer": {"success": "¡Video generado con éxito!", "successWithTime": "Video generado en {{duration}}!", "failed": "Error al generar el video. Inténtalo de nuevo, por favor.", "frameExtracted": "¡Primer fotograma extraído exitosamente!", "styleApplied": "¡Estilo aplicado con éxito!", "referenceApplied": "¡Imagen de referencia aplicada exitosamente!", "useReferenceFailed": "Error al usar la imagen de referencia", "trimmed": "Video recortado a {{duration}}s", "processedFirstSeconds": "El video será procesado usando solo los primeros {{duration}}s", "timeout": "La generación del video superó el tiempo límite de 10 minutos", "processingFailed": "Procesamiento de video fallido, se utilizará el archivo original", "videoDeleted": "Video eliminado exitosamente!", "styleTransferFailed": "Transferencia de estilo fallida. Inténtalo otra vez, por favor.", "invalidStyleResponse": "Respuesta no válida de la API de transferencia de estilo", "uploadExtractedFrameFailed": "Error al subir el fotograma extraído", "uploadReferenceFailed": "Error al subir la imagen de referencia", "uploadVideoFailed": "Error al subir el video", "videoGenerationFailed": "La generación del video falló. Inténtalo nuevamente, por favor.", "videoGenerationTimeout": "La generación del video superó el tiempo límite de 10 minutos", "noPredictionId": "No se recibió ID de predicción de la API de generación de video", "unexpectedOutputFormat": "Formato de salida inesperado de la generación de video", "noVideoUrlFound": "No se encontró URL de video en la salida de la generación", "missingVideoOrFrame": "Falta el video o el fotograma con estilo para la generación", "downloadFailed": "Descarga fallida. Inténtalo nuevamente, por favor.", "deleteFailed": "Eliminación fallida. Inténtalo otra vez, por favor.", "durationWarning": "Solo se procesarán los primeros 5 segundos.", "uploadVideoFirst": "Por favor, sube un video primero para extraer el primer fotograma", "extractingFrame": "Extrayendo primer fotograma del video...", "durationInfo": "Video original: {{original}}s, se utilizarán los primeros {{selected}}s para la generación", "videoTrimmed": "Video recortado a {{duration}}s para la generación", "trimFailedUsingOriginal": "Error al recortar el video, usando el video original", "videoGenerationStarted": "Comenzó la generación de tu video con estilo...", "videoGeneratedWithTime": "Video generado exitosamente en {{duration}}!", "referenceImageCropped": "Imagen de referencia recortada automáticamente para coincidir con las dimensiones del fotograma", "autoCropFailed": "El recorte automático falló, utilizando imagen original. Por favor, asegúrate de que tu referencia coincide con la composición del fotograma.", "frameExtractionEmpty": "El resultado de la extracción de fotogramas está vacío", "frameExtractionAllRetries": "No se pudo extraer el fotograma del video tras todos los intentos", "retryingFrameExtraction": "Reintentando la extracción de fotogramas (intento {{attempt}})...", "safariTimeoutAdvice": "Se detectó un tiempo de espera en Safari. Intenta usar un video más corto o un navegador diferente como Chrome para un mejor procesamiento de videos.", "safariVideoTooLong": "El video dura más de 10 segundos. Recorta tu video a 10 segundos o utiliza el navegador Chrome para recortarlo automáticamente.", "safariDurationCheckFailed": "Error al comprobar la duración del video. Por favor, inténtalo de nuevo."}}, "warnings": {"durationWarning": "El video supera los 5 segundos y será recortado", "uploadVideoFirst": "Por favor sube un video primero", "extractingFrame": "Extrayendo fotograma del video..."}, "talking-head": {"invalidAudio": "Formato de audio no válido. Por favor, usa MP3 o WAV", "modelsFetchFailed": "Error al recuperar modelos", "login": "Por favor inicia sesión para usar esta función", "imageRequired": "Por favor selecciona una imagen", "audioRequired": "Por favor selecciona un archivo de audio", "noCredit": "Créditos insuficientes", "success": "Video de cabeza parlante generado exitosamente", "failure": "Error al generar video", "audioDurationExceeded": "La duración del audio supera el límite de 2 minutos. Solo se usarán los primeros 2 minutos.", "imageTooLarge": "El archivo de imagen es demasiado grande. Tamaño máximo: {{maxSize}}MB.", "audioTooLarge": "El archivo de audio es demasiado grande. Tamaño máximo: {{maxSize}}MB.", "filesSizeTooLarge": "Los archivos son demasiado grandes. <PERSON><PERSON> favor, utiliza archivos de imagen y audio más pequeños.", "compressingImage": "Comprimiendo imagen para optimizar la carga...", "imageCompressionFailed": "Error al comprimir la imagen. Por favor, utiliza un archivo de imagen más pequeño.", "requestTooLarge": "La solicitud es demasiado grande. Utiliza archivos más pequeños, por favor."}, "info": {"compressing": "Comprimiendo el archivo...", "uploading": "Subiendo el archivo..."}}