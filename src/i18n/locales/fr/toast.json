{"auth": {"email": "E-mail", "email_placeholder": "<EMAIL>", "signin_email": "Se connecter avec l'e-mail", "signin_others": "Ou continuez avec", "google": "Google", "error": {"title": "Une erreur est survenue.", "description": "Votre demande de connexion a échoué. Veuillez réessayer."}, "success": {"title": "Vérifiez votre e-mail", "description": "Nous vous avons envoyé un lien de connexion. Vérifiez également votre dossier de spam."}, "invitation_code": "Code d'invitation", "invitation_code_placeholder": "Code d'invitation (facultatif)"}, "image": {"generation": {"failed": "Échec de la génération de l'image"}, "upscale": {"fetchFailed": "Échec de la récupération des images", "deleteSuccess": "Suppression réussie", "deleteFailed": "Échec de la suppression", "downloadFailed": "Échec du téléchargement", "purchaseMoreZaps": "Veuillez acheter plus de zaps", "upscaleFailed": "Échec de l'agrandissement de l'image", "upscaleSuccess": "Agrandissement de l'image réussi", "noZaps": "Pas de zaps disponibles"}}, "text": {"generation": {"failed": "Échec de la génération de texte"}}, "character": {"creation": {"failed": "Échec de la création de personnage", "characterLimitExceeded": "Vous avez atteint la limite ! Mettez à jour votre plan pour créer plus de personnages."}}, "common": {"fetchFailed": "Échec de la récupération des images", "deleteSuccess": "Suppression réussie", "deleteFailed": "Échec de la suppression", "downloadFailed": "Échec du téléchargement", "noZaps": "Pas de zaps disponibles", "rateLimitExceeded": "Vous avez atteint la limite ! Mettez à jour votre plan pour générer plus à la fois.", "extractFailed": "Échec de l'extraction du cadre", "processingFailed": "Échec du traitement", "invalidImage": "Veuillez sélectionner un fichier d'image valide"}, "backgroundRemoval": {"purchaseZaps": "Veuillez acheter plus de zaps pour utiliser le Suppresseur de fond", "failed": "Échec de la suppression du fond", "success": "Suppression du fond réussie", "mediaReceived": "Média reçu avec succès"}, "imageToVideo": {"purchaseMoreZaps": "Veuillez acheter plus de zaps", "generateSuccess": "Animation générée avec succès", "generateFailed": "Échec de la génération de l'animation", "resourceExhausted": "La ressource est épuisée", "noZaps": "Pas de zaps disponibles", "fetchVideosFailed": "Échec de la récupération des vidéos", "deleteSuccess": "Suppression réussie", "deleteFailed": "Échec de la suppression", "downloadFailed": "Échec du téléchargement"}, "lineArtColorize": {"fetchFailed": "Échec de la récupération des images", "deleteSuccess": "Suppression réussie", "deleteFailed": "Échec de la suppression", "downloadFailed": "Échec du téléchargement", "purchaseMoreZaps": "Veuillez acheter plus de zaps", "colorizeSuccess": "Image colorisée avec succès", "colorizeFailed": "Échec de la colorisation de l'image", "noZaps": "Pas de zaps disponibles", "createFailed": "Échec de la création de l'image", "mediaReceived": "Média reçu avec succès"}, "error": {"fetchImages": "Échec de la récupération des images", "delete": "Échec de la suppression", "download": "Échec du téléchargement", "createImage": "Échec de la création de l'image", "noZaps": "Pas de zaps disponibles", "insufficientZaps": "Veuillez acheter plus de zaps", "colorizeImage": "Échec de la colorisation de l'image", "fetchVideos": "La récupération des vidéos a échoué", "upscaleFailed": "L'amélioration de la vidéo a échoué", "generateComicFailed": "Échec de la génération de la bande dessinée, veuillez réessayer.", "failedPost": "Échec de la publication", "noImageError": "Commençons par créer quelques images pour aller de l'avant", "uploadImageFailed": "Échec du téléchargement de l'image", "videoInterpolationFailed": "Échec de l'interpolation de la vidéo", "failedToGeneratePrompt": "Échec de la génération de l'invite", "invalidVideoUrl": "URL de la vidéo non valide", "generationModelNotFound": "Modèle de génération introuvable", "generationTaskNotFound": "Tâche de génération introuvable", "invalidParams": "Paramètres non valides", "generationResultFailed": "Échec de la récupération du résultat de la génération", "videoGenerationFailed": "Oups ! La création de la vidéo a échoué. Vous pouvez essayer un modèle différent ou ajuster votre prompt—certains modèles peuvent bloquer le contenu sensible.", "imageGenerationFailed": "Oups ! La création d'image a échoué. Vous pouvez essayer un modèle différent ou ajuster votre prompt—certains modèles peuvent bloquer le contenu sensible.", "sensitiveContent": "Échec de la génération : l'entrée ou la sortie a été signalée comme contenant un contenu sensible. Veuillez réessayer avec des entrées différentes.", "imageExportFailed": "L'exportation de l'image a échoué. Veuillez réessayer.", "autoPostFailed": "La publication automatique a échoué. Veuillez essayer de publier manuellement.", "uploadSuccess": "Téléchargement réussi !", "uploadFailed": "Téléversement échoué", "invalidFileType": "Type de fichier non valide. Veuillez téléverser uniquement des images ou des vidéos.", "unsupportedFileType": "Type de fichier non supporté. Veuillez télécharger uniquement des images ou des vidéos.", "failedToProcessFile": "Échec du traitement du fichier. Veuillez réessayer.", "failedToPost": "Échec de l'envoi de la publication. Veuillez réessayer.", "someImagesGenerationFailed": "Échec de la génération de certaines images. Veuillez réessayer."}, "success": {"delete": "Suppression réussie", "colorizeImage": "Image colorisée avec succès", "upscaleVideo": "Amélioration de la vidéo réussie", "downloaded_and_share": "Image téléchargée avec succès ! Partagez-la maintenant avec vos amis !", "download": "Téléchargement réussi", "copy": "<PERSON><PERSON> r<PERSON>", "videoInterpolationSuccess": "Interpolation de la vidéo réussie", "publish": "Publication réussie", "share": "Contenu partagé avec succès!", "shareLinkCopied": "Lien copié dans le presse-papiers! Partagez-le avec vos amis maintenant!", "uploadSuccess": "Téléversement réussi!"}, "video": {"upscale": {"fetchFailed": "Échec de la récupération des vidéos", "deleteSuccess": "Suppression réussie", "deleteFailed": "Échec de la suppression", "downloadFailed": "Échec du téléchargement", "purchaseMoreZaps": "Veuillez acheter plus de zaps", "upscaleFailed": "Échec de l'agrandissement de la vidéo", "upscaleSuccess": "Vidéo agrandie avec succès !", "noZaps": "Pas de zaps disponibles", "invalidVideoUrl": "URL de la vidéo invalide", "uploadFailed": "Échec du téléchargement"}, "interpolation": {"success": "Interpolation vidéo r<PERSON>ie", "failed": "L'interpolation vidéo a échoué"}, "styleTransfer": {"success": "Vidéo générée avec succès !", "successWithTime": "Vid<PERSON><PERSON> g<PERSON> en {{duration}} !", "failed": "Échec de la génération de la vidéo. Veuillez réessayer.", "frameExtracted": "Premier cadre extrait avec succès !", "styleApplied": "Style appliqué avec succès !", "referenceApplied": "Image de référence appliquée avec succès !", "useReferenceFailed": "Échec de l'utilisation de l'image de référence", "trimmed": "Vidéo coupée à {{duration}}s", "processedFirstSeconds": "La vidéo sera traitée en utilisant uniquement les premières {{duration}}s", "timeout": "Génération de la vidéo expirée après 10 minutes", "processingFailed": "Échec du traitement de la vidéo, le fichier original sera utilisé", "videoDeleted": "Vidéo supprimée avec succès !", "styleTransferFailed": "Échec du transfert de style. Veuillez réessayer.", "invalidStyleResponse": "Réponse invalide de l'API de transfert de style", "uploadExtractedFrameFailed": "Échec du téléchargement du cadre extrait", "uploadReferenceFailed": "Échec du téléchargement de l'image de référence", "uploadVideoFailed": "Échec du téléchargement de la vidéo", "videoGenerationFailed": "Échec de la génération de la vidéo. Veuillez réessayer.", "videoGenerationTimeout": "Génération de la vidéo expirée après 10 minutes", "noPredictionId": "Aucun ID de prédiction reçu de l'API de génération de vidéo", "unexpectedOutputFormat": "Format de sortie inattendu de la génération de vidéo", "noVideoUrlFound": "Aucune URL de vidéo trouvée dans la sortie de génération", "missingVideoOrFrame": "Vidéo ou cadre stylisé manquant pour la génération", "downloadFailed": "Téléchargement échoué. Veuillez réessayer.", "deleteFailed": "Échec de la suppression. Veuillez réessayer.", "durationWarning": "Seules les 5 premières secondes seront traitées.", "uploadVideoFirst": "Veuillez d'abord télécharger une vidéo pour extraire le premier cadre", "extractingFrame": "Extraction du premier cadre de la vidéo...", "durationInfo": "Vidéo originale : {{original}}s, nous utiliserons les premières {{selected}}s pour la génération", "videoTrimmed": "Vidéo coupée à {{duration}}s pour la génération", "trimFailedUsingOriginal": "Échec du découpage de la vidéo, utilisation de la vidéo originale", "videoGenerationStarted": "Démarrage de la génération de votre vidéo stylisée...", "videoGeneratedWithTime": "Vidéo générée avec succès en {{duration}} !", "referenceImageCropped": "Image de référence automatiquement recadrée pour correspondre aux dimensions du cadre", "autoCropFailed": "Échec du recadrage automatique, utilisation de l'image originale. Veuillez vous assurer que votre référence correspond à la composition du cadre.", "frameExtractionEmpty": "Le résultat de l'extraction des images est vide.", "frameExtractionAllRetries": "Échec de l'extraction des images de la vidéo après toutes les tentatives.", "retryingFrameExtraction": "Nouvelle tentative d'extraction des images (essai {{attempt}})...", "safariTimeoutAdvice": "<PERSON><PERSON><PERSON> d'attente sur Safari détecté. Essayez d'utiliser une vidéo plus courte ou un autre navigateur, comme Chrome, pour un meilleur traitement vidéo.", "safariVideoTooLong": "La durée de la vidéo dépasse 10 secondes. Veuillez réduire votre vidéo à 10 secondes ou utiliser le navigateur Chrome pour un découpage automatique.", "safariDurationCheckFailed": "Échec de la vérification de la durée de la vidéo. Veuillez réessayer."}}, "warnings": {"durationWarning": "La vidéo dépasse 5 secondes et sera coupée", "uploadVideoFirst": "Veuillez d'abord télécharger une vidéo", "extractingFrame": "Extraction du cadre de la vidéo..."}, "talking-head": {"invalidAudio": "Format audio invalide. Veuillez utiliser MP3 ou WAV", "modelsFetchFailed": "Échec de la récupération des modèles", "login": "Veuillez vous connecter pour utiliser cette fonctionnalité", "imageRequired": "Veuillez sélectionner une image", "audioRequired": "Veuillez sélectionner un fichier audio", "noCredit": "Crédits insuffisants", "success": "Vidéo de tête parlante générée avec succès", "failure": "Échec de la génération de la vidéo", "audioDurationExceeded": "La durée audio dépasse la limite de 2 minutes. Seules les 2 premières minutes seront utilisées.", "imageTooLarge": "Le fichier image est trop volumineux. <PERSON>lle maximale : {{maxSize}} Mo", "audioTooLarge": "Le fichier audio est trop volumineux. Taille maximale : {{maxSize}} Mo", "filesSizeTooLarge": "Les fichiers sont trop volumineux. Veuillez utiliser des fichiers audio et image plus petits.", "compressingImage": "Compression de l'image pour un téléchargement optimal...", "imageCompressionFailed": "Échec de la compression de l'image. Veuillez utiliser un fichier image plus petit.", "requestTooLarge": "La demande est trop volumineuse. Veuillez utiliser des fichiers plus petits."}, "info": {"compressing": "Compression du fichier en cours...", "uploading": "Téléversement du fichier en cours..."}}