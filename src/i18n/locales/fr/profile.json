{"pageTitle": "Mon Profil | Komiko", "setUsername": "Définir votre nom d'utilisateur", "followers": "{{count}} <PERSON><PERSON><PERSON><PERSON>(e)s", "following": "{{count}} Abonnements", "creativePartner": "💎 Partenaire Créatif", "editProfile": "Modifier mon Profil", "username": "Nom d'utilisateur", "bio": "Bio", "joinDiscussion": "Rejoignez la <PERSON> !", "save": "Enregistrer", "posts": "Publications", "gallery": "Galerie", "createAway": "Créer ailleurs !", "getMoreZaps": "Obtenir plus de Zaps", "lowOnZaps": "Bientôt à court de Zaps, rechargez !", "noZapsLeft": "Plus de Zaps, rechargez !", "referUsers": "Parrainer des Utilisateurs", "referReward": "{{count}} Zaps pour chaque inscription", "referralLinkCopied": "Lien de parrainage copié !", "copyLink": "Copier le lien", "dailyCheckIn": "Check-in quotidien", "completed": "<PERSON><PERSON><PERSON><PERSON>", "complete": "<PERSON><PERSON>", "postStory": "Publier une Story", "likePost": "Aimer une publication", "dailyRewards": "Récompenses Quotidiennes", "joinDiscord": "Re<PERSON><PERSON><PERSON> notre <discord></discord> pour obtenir de l'aide de la communauté", "upgrade": "Mettre à niveau", "rewards": "Récompenses", "videos": "Vid<PERSON><PERSON>", "noVideosFound": "Aucune vidéo n'a été trouvée", "tryAgain": "Essayer à nouveau", "myCreations": "Mes Créations", "images": "Images", "myPosts": "Mes Publications", "referralCodeCopied": "Code de parrainage copié !", "copyCode": "Copier le code", "invitations": "Invitations", "invitedUsers": "Utilisateurs invités", "noInvitations": "Aucune invitation pour l'instant", "modal": {"tool": "Outil", "model": "<PERSON><PERSON><PERSON><PERSON>", "prompt": "Invite", "postButton": "Publier", "shareText": "Découvrez cette vidéo incroyable de Komiko !", "downloadSuccess": "Vidéo téléchargée avec succès !"}, "downloadSuccess": "Téléchargement réalisé avec succès !", "tools": {"image_animation": "Animation d'Image", "video-interpolation": "Interpolation de Vidéo", "talking-head": "Tête Parlante IA", "image-animation-generator": "Générateur d'Animation d'Image", "video-upscaling": "Amélioration de Vidéo", "video_upscaling": "Amélioration de Vidéo", "photo-to-anime": "Photo en Anime", "background-removal": "Suppression de Fond", "image-upscaling": "Amélioration d'Image", "video_interpolation": "Interpolation de Vidéo", "ai-character-sheet-generator": "Générateur de Fiches de Personnage", "oc-maker": "Créateur de Personnages Originaux", "line_art_colorization": "Colorisation de Ligne d'Art", "character": "Personnage", "restyle": "<PERSON><PERSON><PERSON>", "layer_splitter": "Division de Calques", "video-to-video": "Vidéo à Vidéo IA", "video_to_video": "Vidéo à Vidéo IA", "ai-talking-head": "Tête Parlante IA"}, "deleteVideo": "Supprimer la vidéo", "confirmDeleteVideo": "Confirmer la suppression de la vidéo", "confirmDeleteVideoMessage": "Êtes-vous sûr de vouloir supprimer cette vidéo ? Cette action est irréversible.", "deleteVideoSuccess": "Vidéo supprimée avec succès !", "deleteVideoError": "Échec de la suppression de la vidéo. Veuillez réessayer."}