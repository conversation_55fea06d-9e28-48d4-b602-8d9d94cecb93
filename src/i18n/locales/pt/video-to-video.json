{"title": "IA de Vídeo para Vídeo", "description": "Transforme seus vídeos com a inteligência artificial de Vídeo para Vídeo. Nossa tecnologia avançada de Transferência de Estilo de Vídeo converte seus vídeos em estilos de anime, desenho animado, mangá e manhwa, mantendo o movimento e a duração originais.", "meta": {"title": "IA de Vídeo para Vídeo | A Melhor Transferência de Estilo de Vídeo", "description": "Transforme seus vídeos com a inteligência artificial de Vídeo para Vídeo. Nossa tecnologia avançada de Transferência de Estilo de Vídeo converte seus vídeos em estilos de anime, desenho animado, mangá e manhwa, mantendo o movimento e a duração originais.", "fullDescription": "Ferramenta revolucionária de IA de Vídeo para Vídeo que transforma vídeos comuns em obras de arte no estilo anime, animações de desenho animado, visuais no estilo mangá e estéticas de manhwa. Nossa tecnologia avançada de Transferência de Estilo de Vídeo preserva a consistência do movimento ao mesmo tempo em que aplica transformações artísticas, incluindo anime do Studio Ghibli, manhwa coreano, mang<PERSON> japonês, animação em aquarela e estilos de desenho animado. Perfeito para criadores de conteúdo, animadores e artistas que buscam Transferência de Estilo de Vídeo com qualidade profissional.", "keywords": "IA de Vídeo para Vídeo, Transferência de Estilo de Vídeo, gerador de vídeo anime, filtro de vídeo de desenho animado, transferência de estilo mangá, animação manhwa, conversor de vídeo para anime, ferramenta de animação, criador de vídeo de desenho animado, vídeo estilo anime, criador de vídeo mangá, estilização de vídeo, gerador de vídeo animado, filtro de vídeo em quadrinhos, transformação de anime, transferência de estilo de desenho animado"}, "ui": {"title": "IA de Vídeo para Vídeo", "tooltip": "Transforme seus vídeos com a inteligência artificial de Vídeo para Vídeo e Transferência de Estilo de Vídeo", "generatedVideos": "Vídeos Estilizados Gerados", "emptyState": "Seus vídeos estilizados em anime e desenho animado aparecerão aqui", "steps": {"uploadVideo": "1. <PERSON><PERSON><PERSON>", "styleSelection": "Selecionar Estilo", "generateVideo": "3. <PERSON><PERSON><PERSON>"}, "upload": {"dragText": "Toque para enviar ou arraste seu vídeo aqui", "formatInfo": "Suporta MP4, MO<PERSON>, AVI • Máx. 5 segundos • Máx. 50MB", "extractFrameManually": "Extrair o Primeiro Frame Manualmente", "bestResultsTip": "Para melhores resultados, use um vídeo de uma única cena — idealmente gravado em uma tomada contínua.", "safariFormatInfo": "Suporta MP4, MOV, AVI • Máximo de 10 segundos • Até 50MB", "safariNotice": "Aviso para Usuários do Safari", "safariLimitWarning": "Duração do vídeo limitada a 10 segundos ou menos. Para vídeos mais longos ou controle de duração personalizado, por favor, use o navegador Chrome.", "bestResultsTip1": "Para obter os melhores resultados, utilize um vídeo gravado em uma única sequência.", "bestResultsTip2": "Se incluir um ator humano, o primeiro quadro deve mostrar uma visão frontal. Idealmente, limite o vídeo a um ator principal e inclua pelo menos a parte superior do corpo."}, "duration": {"title": "Duração", "description": "Selecione a duração desejada para a geração do vídeo. Durações mais longas consomem mais Z<PERSON>.", "originalDuration": "Original: {{duration}}s", "tooLong": "<PERSON><PERSON>", "willBeTrimmed": "Será cortado de {{original}}s para {{target}}s", "originalLength": "Duração Original", "safariNote": "Safari detectado: usando a duração original do vídeo para melhor compatibilidade", "chromeAdvice": "Para controle de duração personalizado, por favor, use o navegador Chrome.", "safariUseOriginal": "Usuários do Safari: o vídeo usará sua duração original para compatibilidade ideal."}, "videoMode": {"title": "Modo de Geração", "human": "<PERSON><PERSON> Ví<PERSON>o <PERSON>", "humanDescription": "Otimizado para pessoas e vídeos de retrato", "general": "<PERSON><PERSON>", "generalDescription": "Funciona com qualquer assunto e tipo de cena"}, "videoPrompt": {"title": "Prompt (Opcional)", "placeholder": "Ex., garota anime dançando", "description": "Adicione detalhes adicionais para direcionar o processo de geração de vídeo"}, "framePreview": {"original": "Original", "styled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "applyingStyle": "Aplicando Estilo...", "awaitingStyle": "<PERSON><PERSON><PERSON><PERSON>", "selectStyleBelow": "Selecione um estilo abaixo", "beforeAfterComparison": "Comparação da transferência de estilo antes e depois", "applyingStyleToFrame": "Aplicando o estilo selecionado ao frame...", "frameReferenceText": "Este frame será usado como referência para a Transferência de Estilo de Vídeo", "styleTooltip": "Este frame estilizado gui<PERSON>á toda a transformação do vídeo."}, "styleModes": {"templates": "Templates", "prompt": "Prompt", "reference": "Referência"}, "styleTemplates": {"anime": "Anime", "ghibliAnime": "<PERSON><PERSON>", "koreanManhwa": "<PERSON><PERSON>", "cartoon": "<PERSON><PERSON><PERSON>", "manga": "Mangá", "inkWash": "Pintura a Tinta", "watercolor": "Aquarela", "lineArt": "Arte Linear", "lowPoly": "Low Poly", "clay": "Claymation", "pixelArt": "Pixel Art", "origami": "Papel Origami", "lego": "Lego", "vaporwave": "Vaporwave", "rickAndMorty": "<PERSON> and <PERSON><PERSON><PERSON>", "southPark": "South Park", "simpsons": "Simpsons", "naruto": "<PERSON><PERSON><PERSON>", "onePiece": "One Piece", "myLittlePony": "My Little Pony", "comic": "Q<PERSON>rin<PERSON>", "miku": "<PERSON><PERSON>", "barbie": "Barbie", "goku": "<PERSON><PERSON> (Dragon Ball)", "trump": "<PERSON>", "princess": "Princesa / Príncipe", "kimono": "Kimono / Yukata", "superhero": "Super-herói", "magicalGirl": "<PERSON><PERSON><PERSON>", "hogwarts": "Hogwarts", "cowboy": "Cowboy", "sailorUniform": "Uniforme de Marinheiro", "pixar": "Pixar", "apocalypse": "Apocalip<PERSON>", "magicalWorld": "Mundo <PERSON>", "dreamland": "Terra dos Sonhos", "cyberpunk": "Cyberpunk", "kpopIdol": "Ídolo do K-pop", "cloud": "Nuvem", "mars": "<PERSON><PERSON>", "outerSpace": "Espaço Sideral", "sailorMoon": "<PERSON>"}, "prompt": {"placeholder": "Descreva a transformação de estilo desejada...", "example": "Exemplo: \"Substitua o homem por Naruto\", \"Transforme isso em um estilo anime clássico dos anos 90\", \"Vista a garota com um vestido floral\""}, "reference": {"uploadText": "Envie seu frame pré-estilizado como referência", "formatInfo": "Suporta JPG, PNG, JPEG, WEBP • Máx. 10MB", "compositionWarning": "Certifique-se de que a imagem de referência corresponda à composição exata do primeiro frame do vídeo original."}, "buttons": {"applying": "Aplicando...", "useNewReference": "Usar Nova Referência", "applyNewStyle": "Aplicar Novo Estilo", "useReference": "Usar Referência", "applyStyle": "<PERSON>p<PERSON><PERSON>", "generateVideo": "<PERSON><PERSON><PERSON>", "generatingVideo": "Gerando Vídeo...", "generateMore": "<PERSON><PERSON><PERSON>", "createAnother": "<PERSON><PERSON><PERSON>"}, "separators": {"readyToGenerate": "Pronto para gerar vídeo"}}, "whatIs": {"title": "O que é IA de Vídeo para Vídeo?", "description": "A IA de Vídeo para Vídeo transforma vídeos comuns em animações no estilo anime, desenho animado, mangá e manhwa, usando tecnologia avançada de Transferência de Estilo de Vídeo. Nosso processo de duas etapas primeiro aplica o estilo escolhido a um frame de referência e, em seguida, usa este frame estilizado para transformar todo o seu vídeo, mantendo o movimento e a duração originais. Escolha entre mais de 20 estilos, incluindo anime do Studio Ghibli, manhwa coreano, mangá japonês e estéticas populares de desenho animado, com nossa Transferência de Estilo de Vídeo."}, "examples": {"title": "Exemplos de IA de Vídeo para Vídeo", "description": "Veja como nossa IA de Vídeo para Vídeo transforma vídeos em estilos impressionantes de anime, desenho animado, mangá e manhwa, mantendo a consistência perfeita do movimento com a avançada Transferência de Estilo de Vídeo.", "description1": "Troca de Personagem | prompt: <PERSON><PERSON> a garo<PERSON> para <PERSON>", "description2": "Troca de Personagem | prompt: <PERSON><PERSON> a garota para Sailor <PERSON>", "description3": "Transferência de Estilo | Converter vídeo de dança real para estilo anime", "originalVideo": "Vídeo Original", "animeVideo": "Vídeo em Estilo Anime", "watercolorVideo": "Estilo de Animação em Aquarela", "style": "<PERSON><PERSON><PERSON>", "prompt": "Prompt de Estilo <PERSON>", "description5": "Transferência de Estilo | Converter vídeo de dança real para estilo de quadrinhos", "description6": "Transformação de Cena | Prompt: Alterar a imagem para que o ator esteja andando em um ambiente cyberpunk—uma cena extremamente sci-fi", "animeStyle": "<PERSON><PERSON><PERSON>", "comicStyle": "<PERSON><PERSON><PERSON>", "promptUsed": "Prompt de Estilo <PERSON>", "animeTransformation": "Transformação em Anime", "description7": "Transformação de um cachorro da vida real para o estilo anime, mostrando um animal de estimação realista convertido em personagem animado", "description8": "Mãos tricotando transformadas para o estilo anime, demonstrando a preservação detalhada dos movimentos em atividades de artesanato"}, "howTo": {"title": "Como Usar a IA de Vídeo para Vídeo"}, "steps": {"step1": {"title": "<PERSON>vie Seu Vídeo", "content": "Envie qualquer arquivo de vídeo (MP4, MOV, AVI) de até 5 segundos e 50MB para processamento de IA de Vídeo para Vídeo. Vídeos com mais de 5 segundos são cortados automaticamente para uma Transferência de Estilo de Vídeo ideal."}, "step2": {"title": "Referência de Transferência de Estilo de Vídeo", "content": "Extraímos o primeiro frame e aplicamos o estilo anime, desenho animado ou mangá escolhido para criar um guia de referência para uma transformação consistente de Transferência de Estilo de Vídeo."}, "step3": {"title": "Escolha o Estilo Artístico", "content": "Selecione entre mais de 20 estilos predefinidos, como anime do Studio Ghibli, man<PERSON> coreano, mang<PERSON> japon<PERSON>, ou crie estilos personalizados com prompts de texto e imagens de referência para Transferência de Estilo de Vídeo."}, "step4": {"title": "Gerar Vídeo para Vídeo com IA", "content": "Nossa IA de Vídeo para Vídeo transforma seu vídeo completo usando o frame de referência estilizado, preservando todo o movimento, expressões e duração originais por meio da avançada Transferência de Estilo de Vídeo."}}, "benefits": {"title": "Por que Usar a IA de Vídeo para Vídeo", "description": "Nossa IA de Vídeo para Vídeo oferece a Transferência de Estilo de Vídeo mais avançada com preservação do movimento, extensas opções de estilo e preços transparentes."}, "features": {"feature1": {"title": "Preservação Perfeita do Movimento", "content": "A IA de Vídeo para Vídeo mantém todos os detalhes do movimento original, expressões faciais e duração, enquanto aplica estilos anime, desenho animado ou mangá com consistência de Transferência de Estilo de Vídeo perfeita."}, "feature2": {"title": "Mais de 20 Opções de Transferência de Estilo de Vídeo", "content": "Escolha entre anime do Studio Ghibli, man<PERSON> core<PERSON>, man<PERSON><PERSON> j<PERSON>, desenho animado da <PERSON>, estilo <PERSON> e muito mais. Crie Transferência de Estilo de Vídeo personalizada com prompts de texto ou imagens de referência usando a IA de Vídeo para Vídeo."}, "feature3": {"title": "Saída de Qualidade Profissional", "content": "Gere vídeos de anime e desenho animado em alta definição com aplicação consistente de Transferência de Estilo de Vídeo, transições suaves e sem cintilação ou artefatos com nossa IA de Vídeo para Vídeo."}, "feature4": {"title": "Sistema de Custo Inteligente", "content": "Preços transparentes com cobranças separadas para Transferência de Estilo de Vídeo e geração de vídeo. Experimente diferentes estilos sem custos adicionais de vídeo usando a IA de Vídeo para Vídeo."}, "feature5": {"title": "Processo Fácil de Duas Etapas", "content": "Fluxo de trabalho simples de IA de Vídeo para Vídeo: envie o vídeo, aplique a Transferência de Estilo de Vídeo ao frame de referência, gere o vídeo completo. Nenhuma experiência técnica é necessária com rastreamento de progresso em tempo real."}, "feature6": {"title": "Otimização Automática", "content": "Processamento inteligente de IA de Vídeo para Vídeo com corte automático, suporte a formatos (MP4, MOV, AVI) e cálculo de custo com base na duração para Transferência de Estilo de Vídeo ideal."}}, "faq": {"title": "FAQ da IA de Vídeo para Vídeo", "description": "Perguntas comuns sobre nossa ferramenta, processo, custos e práticas recomendadas de IA de Vídeo para Vídeo e Transferência de Estilo de Vídeo.", "q1": "Como funciona a IA de Vídeo para Vídeo?", "a1": "Nossa IA de Vídeo para Vídeo usa um processo de Transferência de Estilo de Vídeo de duas etapas: 1) Extrai um frame de referência e aplica o estilo anime/desenho animado escolhido, 2) Transforma todo o vídeo usando este frame estilizado, mantendo o movimento e a duração originais. Isso garante uma aplicação consistente da Transferência de Estilo de Vídeo em todos os frames.", "q2": "Quais são os requisitos de formato de vídeo para a IA de Vídeo para Vídeo?", "a2": "Suportamos os formatos MP4, MOV e AVI com tamanho máximo de arquivo de 50MB para processamento de IA de Vídeo para Vídeo. Os vídeos são limitados a 5 segundos e cortados automaticamente se forem mais longos para um processamento de Transferência de Estilo de Vídeo ideal e eficiência de custos.", "q3": "Quanto tempo leva a Transferência de Estilo de Vídeo?", "a3": "O processamento total de IA de Vídeo para Vídeo leva de 5 a 10 minutos: Transferência de Estilo de Vídeo (1-3 minutos) e geração de vídeo (3-7 minutos). Você pode monitorar o progresso da Transferência de Estilo de Vídeo em tempo real.", "q4": "Quais são os custos da IA de Vídeo para Vídeo?", "a4": "Cobranças separadas para Transferência de Estilo de Vídeo e geração de vídeo com base na duração. Os custos da IA de Vídeo para Vídeo são mostrados em tempo real antes do processamento, e os créditos são deduzidos somente após a conclusão bem-sucedida da Transferência de Estilo de Vídeo.", "q5": "Posso criar estilos personalizados de Transferência de Estilo de Vídeo?", "a5": "Sim! Escolha entre mais de 20 templates predefinidos (Studio Ghibli, man<PERSON> coreano, mangá japonês, etc.), escreva prompts de texto personalizados ou envie imagens de referência para transformações exclusivas de Transferência de Estilo de Vídeo com nossa IA de Vídeo para Vídeo.", "q6": "O que torna bons vídeos de entrada para a IA de Vídeo para Vídeo?", "a6": "Melhores resultados de Transferência de Estilo de Vídeo com assuntos claros, boa iluminação, movimento estável e recursos bem definidos. Evite movimentos rápidos, filmagens escuras ou borradas para um processamento ideal de IA de Vídeo para Vídeo. Vídeos com menos de 5 segundos com pessoas ou objetos claros funcionam melhor para Transferência de Estilo de Vídeo."}}