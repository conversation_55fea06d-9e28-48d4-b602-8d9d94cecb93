{"auth": {"email": "E-mail", "email_placeholder": "<EMAIL>", "signin_email": "Entrar com E-mail", "signin_others": "Ou continue com", "google": "Google", "error": {"title": "Algo deu errado.", "description": "Sua solicitação de login falhou. Tente novamente."}, "success": {"title": "Verifique seu e-mail", "description": "Enviamos um link para login. Lembre-se de verificar a pasta de spam também."}, "invitation_code": "Código de convite", "invitation_code_placeholder": "Código de convite (Opcional)"}, "image": {"generation": {"failed": "Falha ao gerar imagem"}, "upscale": {"fetchFailed": "Falha ao buscar imagens", "deleteSuccess": "Exclusão realizada com sucesso", "deleteFailed": "Falha ao excluir", "downloadFailed": "Falha ao baixar", "purchaseMoreZaps": "Por favor, compre mais zaps", "upscaleFailed": "Falha ao ampliar imagem", "upscaleSuccess": "Imagem ampliada com sucesso", "noZaps": "Nenhum zap disponível"}}, "text": {"generation": {"failed": "Falha ao gerar texto"}}, "character": {"creation": {"failed": "<PERSON>alha ao criar personagem", "characterLimitExceeded": "Você atingiu o limite! Atualize seu plano para criar mais personagens."}}, "common": {"fetchFailed": "Falha ao buscar imagens", "deleteSuccess": "Exclusão realizada com sucesso", "deleteFailed": "Falha ao excluir", "downloadFailed": "Falha ao baixar", "noZaps": "Nenhum zap disponível", "rateLimitExceeded": "Você atingiu o limite! Atualize seu plano para gerar mais vezes.", "extractFailed": "Falha ao extrair quadro", "processingFailed": "Falha no processamento", "invalidImage": "Por favor, selecione um arquivo de imagem válido"}, "backgroundRemoval": {"purchaseZaps": "Por favor, compre mais zaps para usar o Removedor de Fundo", "failed": "Falha na remoção do fundo", "success": "Fundo removido com sucesso", "mediaReceived": "Mídia recebida com sucesso"}, "imageToVideo": {"purchaseMoreZaps": "Por favor, compre mais zaps", "generateSuccess": "Animação gerada com sucesso", "generateFailed": "Falha ao gerar animação", "resourceExhausted": "Recurso es<PERSON>ado", "noZaps": "Nenhum zap disponível", "fetchVideosFailed": "Falha ao buscar vídeos", "deleteSuccess": "Exclusão realizada com sucesso", "deleteFailed": "Falha ao excluir", "downloadFailed": "Falha ao baixar"}, "lineArtColorize": {"fetchFailed": "Falha ao buscar imagens", "deleteSuccess": "Exclusão realizada com sucesso", "deleteFailed": "Falha ao excluir", "downloadFailed": "Falha ao baixar", "purchaseMoreZaps": "Por favor, compre mais zaps", "colorizeSuccess": "Imagem colorida com sucesso", "colorizeFailed": "Falha ao colorir imagem", "noZaps": "Nenhum zap disponível", "createFailed": "<PERSON>alha ao criar imagem", "mediaReceived": "Mídia recebida com sucesso"}, "error": {"fetchImages": "Falha ao buscar imagens", "delete": "Falha ao excluir", "download": "Falha ao baixar", "createImage": "<PERSON>alha ao criar imagem", "noZaps": "Nenhum zap disponível", "insufficientZaps": "Por favor, compre mais zaps", "colorizeImage": "Falha ao colorir imagem", "fetchVideos": "Falha ao obter os vídeos", "upscaleFailed": "A ampliação do vídeo falhou", "generateComicFailed": "Falha ao gerar quadrinho, por favor, tente novamente.", "failedPost": "<PERSON>alha ao postar", "noImageError": "Vamos começar criando algumas imagens para prosseguir", "uploadImageFailed": "Falha ao fazer upload da imagem", "videoInterpolationFailed": "Erro na interpolação do vídeo", "failedToGeneratePrompt": "Falha ao gerar prompt", "invalidVideoUrl": "URL do vídeo é inválida", "generationModelNotFound": "Modelo de geração não foi encontrado", "generationTaskNotFound": "A tarefa de geração não foi encontrada", "invalidParams": "Parâmet<PERSON>", "generationResultFailed": "Falha ao obter o resultado da geração", "videoGenerationFailed": "Ops! Falha na geração de vídeo. Tente usar um modelo diferente ou ajustar seu prompt—alguns modelos podem bloquear conteúdo sensível.", "imageGenerationFailed": "Ops! Falha na geração de imagem. Tente usar um modelo diferente ou ajustar seu prompt—alguns modelos podem bloquear conteúdo sensível.", "sensitiveContent": "Falha na geração: A entrada ou saída foi marcada como sensível. Por favor, tente novamente com entradas diferentes.", "imageExportFailed": "A exportação da imagem falhou. Por favor, tente novamente.", "autoPostFailed": "Falha no envio automático. Por favor, tente enviar manualmente.", "uploadSuccess": "Upload realizado com sucesso!", "uploadFailed": "<PERSON><PERSON> fal<PERSON>", "invalidFileType": "Tipo de arquivo inválido. Por favor, faça upload apenas de imagens ou vídeos.", "unsupportedFileType": "Tipo de arquivo não é suportado. Por favor, envie apenas imagens ou vídeos.", "failedToProcessFile": "Falha ao processar o arquivo. Por favor, tente novamente.", "failedToPost": "Falha ao publicar. Por favor, tente novamente.", "someImagesGenerationFailed": "A geração de algumas imagens falhou. Por favor, tente novamente."}, "success": {"delete": "Exclusão realizada com sucesso", "colorizeImage": "Imagem colorida com sucesso", "upscaleVideo": "Ampliação do vídeo bem-sucedida", "downloaded_and_share": "Imagem baixada com sucesso! Compartilhe agora com seus amigos!", "download": "Download realizado com sucesso", "copy": "Cópia realizada com sucesso", "videoInterpolationSuccess": "Interpolação de vídeo realizada com sucesso", "publish": "Publicação realizada com sucesso", "share": "Conteúdo compartilhado com sucesso!", "shareLinkCopied": "Link copiado para a área de transferência! Compartilhe com seus amigos agora!", "uploadSuccess": "Upload realizado com sucesso!"}, "video": {"upscale": {"fetchFailed": "Falha ao buscar vídeos", "deleteSuccess": "Exclusão realizada com sucesso", "deleteFailed": "Falha ao excluir", "downloadFailed": "Falha ao baixar", "purchaseMoreZaps": "Por favor, compre mais zaps", "upscaleFailed": "Falha ao ampliar vídeo", "upscaleSuccess": "Vídeo ampliado com sucesso!", "noZaps": "Nenhum zap disponível", "invalidVideoUrl": "URL de vídeo inválida", "uploadFailed": "Falha no upload"}, "interpolation": {"success": "Interpolação de vídeo realizada com sucesso", "failed": "Falha na interpolação de vídeo"}, "styleTransfer": {"success": "Vídeo gerado com sucesso!", "successWithTime": "Vídeo gerado em {{duration}}!", "failed": "Falha na geração de vídeo. Tente novamente.", "frameExtracted": "Primeiro quadro extraído com sucesso!", "styleApplied": "Estilo aplicado com sucesso!", "referenceApplied": "Imagem de referência aplicada com sucesso!", "useReferenceFailed": "Falha ao usar a imagem de referência", "trimmed": "<PERSON><PERSON><PERSON><PERSON> cortado para {{duration}}s", "processedFirstSeconds": "O vídeo será processado usando apenas os primeiros {{duration}}s", "timeout": "Tempo de geração do vídeo esgotado após 10 minutos", "processingFailed": "Falha no processamento do vídeo, usando arquivo original", "videoDeleted": "Vídeo excluído com sucesso!", "styleTransferFailed": "Falha na transferência de estilo. Tente novamente.", "invalidStyleResponse": "Resposta inválida da API de transferência de estilo", "uploadExtractedFrameFailed": "Falha ao fazer upload do quadro extraído", "uploadReferenceFailed": "Falha ao fazer upload da imagem de referência", "uploadVideoFailed": "Falha ao fazer upload do vídeo", "videoGenerationFailed": "Falha na geração de vídeo. Tente novamente.", "videoGenerationTimeout": "Tempo de geração de vídeo esgotado após 10 minutos", "noPredictionId": "Nenhum ID de previsão recebido da API de geração de vídeo", "unexpectedOutputFormat": "Formato de saída inesperado na geração de vídeo", "noVideoUrlFound": "Nenhuma URL de vídeo encontrada na saída de geração", "missingVideoOrFrame": "Faltando vídeo ou quadro estilizado para geração", "downloadFailed": "Falha ao baixar. Tente novamente.", "deleteFailed": "Falha ao excluir. Tente novamente.", "durationWarning": "Apenas os primeiros 5 segundos serão processados.", "uploadVideoFirst": "Por favor, faça upload de um vídeo primeiro para extrair o primeiro quadro", "extractingFrame": "Extraindo primeiro quadro do vídeo...", "durationInfo": "Vídeo original: {{original}}s, usaremos os primeiros {{selected}}s para geração", "videoTrimmed": "<PERSON><PERSON><PERSON><PERSON> cortado para {{duration}}s para geração", "trimFailedUsingOriginal": "Falha ao cortar vídeo, usando vídeo original", "videoGenerationStarted": "Iniciando a geração do seu vídeo estilizado...", "videoGeneratedWithTime": "Vídeo gerado com sucesso em {{duration}}!", "referenceImageCropped": "Imagem de referência cortada automaticamente para coincidir com as dimensões do quadro", "autoCropFailed": "Falha no corte automático, usando imagem original. Verifique se sua referência coincide com a composição do quadro.", "frameExtractionEmpty": "A extração do quadro não retornou resultados", "frameExtractionAllRetries": "Falha ao extrair o quadro do vídeo após todas as tentativas", "retryingFrameExtraction": "Tentando extrair o quadro novamente (tentativa {{attempt}})...", "safariTimeoutAdvice": "Detectado tempo limite no Safari. Tente usar um vídeo mais curto ou um navegador diferente, como o Chrome, para melhorar o processamento do vídeo.", "safariVideoTooLong": "O vídeo tem mais de 10 segundos. Por favor, corte o vídeo para 10 segundos ou utilize o navegador Chrome para corte automático.", "safariDurationCheckFailed": "Não foi possível verificar a duração do vídeo. Por favor, tente novamente."}}, "warnings": {"durationWarning": "O vídeo tem mais de 5 segundos e será cortado", "uploadVideoFirst": "Por favor, faça upload de um vídeo primeiro", "extractingFrame": "Extraindo quadro do vídeo..."}, "talking-head": {"invalidAudio": "Formato de áudio inválido. Por favor, use MP3 ou WAV", "modelsFetchFailed": "Falha ao buscar modelos", "login": "Por favor, faça login para usar este recurso", "imageRequired": "Por favor, selecione uma imagem", "audioRequired": "Por favor, selecione um arquivo de áudio", "noCredit": "Créditos insuficientes", "success": "Vídeo de cabeça falante gerado com sucesso", "failure": "Falha ao gerar vídeo", "audioDurationExceeded": "Duração do áudio excede o limite de 2 minutos. Apenas os primeiros 2 minutos serão usados.", "imageTooLarge": "Arquivo de imagem muito grande. Tamanho máximo: {{maxSize}} MB.", "audioTooLarge": "Arquivo de áudio muito grande. Tamanho máximo: {{maxSize}} MB.", "filesSizeTooLarge": "Os arquivos estão muito grandes. Por favor, utilize arquivos de imagem e áudio menores.", "compressingImage": "Comprimindo imagem para um upload ideal...", "imageCompressionFailed": "Falha ao comprimir a imagem. Por favor, utilize um arquivo de imagem menor.", "requestTooLarge": "A requisição é muito grande. Por favor, utilize arquivos menores."}, "info": {"compressing": "Compactando o arquivo...", "uploading": "Fazendo upload do arquivo..."}}