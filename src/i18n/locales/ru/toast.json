{"auth": {"email": "Электронная почта", "email_placeholder": "<EMAIL>", "signin_email": "Войти с помощью электронной почты", "signin_others": "Или продолжить с", "google": "Google", "error": {"title": "Что-то пошло не так.", "description": "Не удалось войти. Попробуйте ещё раз."}, "success": {"title": "Проверьте свою электронную почту", "description": "Мы отправили вам ссылку для входа. Не забудьте проверить папку \"Спам\"."}, "invitation_code": "Код приглашения", "invitation_code_placeholder": "Код приглашения (необязательно)"}, "image": {"generation": {"failed": "Не удалось создать изображение"}, "upscale": {"fetchFailed": "Не удалось получить изображения", "deleteSuccess": "Удаление выполнено успешно", "deleteFailed": "Не удалось удалить", "downloadFailed": "Не удалось загрузить", "purchaseMoreZaps": "Пожалуйста, приобретите больше zaps", "upscaleFailed": "Не удалось улучшить изображение", "upscaleSuccess": "Изображение успешно улучшено", "noZaps": "Нет доступных zaps"}}, "text": {"generation": {"failed": "Не удалось создать текст"}}, "character": {"creation": {"failed": "Не удалось создать персонажа", "characterLimitExceeded": "Вы достигли лимита! Обновите свой план, чтобы создать больше персонажей."}}, "common": {"fetchFailed": "Не удалось получить изображения", "deleteSuccess": "Удаление выполнено успешно", "deleteFailed": "Не удалось удалить", "downloadFailed": "Не удалось загрузить", "noZaps": "Нет доступных zaps", "rateLimitExceeded": "Вы достигли лимита! Обновите план для большего количества генераций.", "extractFailed": "Не удалось извлечь кадр", "processingFailed": "Обработка не удалась", "invalidImage": "Пожалуйста, выберите действительный файл изображения"}, "backgroundRemoval": {"purchaseZaps": "Пожалуйста, приобретите больше zaps для использования средства удаления фона", "failed": "Удаление фона не удалось", "success": "Фон успешно удалён", "mediaReceived": "Медиа успешно получено"}, "imageToVideo": {"purchaseMoreZaps": "Пожалуйста, приобретите больше zaps", "generateSuccess": "Анимация создана успешно", "generateFailed": "Не удалось создать анимацию", "resourceExhausted": "Ресурс исчерпан", "noZaps": "Нет доступных zaps", "fetchVideosFailed": "Не удалось получить видео", "deleteSuccess": "Удаление выполнено успешно", "deleteFailed": "Не удалось удалить", "downloadFailed": "Не удалось загрузить"}, "lineArtColorize": {"fetchFailed": "Не удалось получить изображения", "deleteSuccess": "Удаление выполнено успешно", "deleteFailed": "Не удалось удалить", "downloadFailed": "Не удалось загрузить", "purchaseMoreZaps": "Пожалуйста, приобретите больше zaps", "colorizeSuccess": "Цветизация изображения прошла успешно", "colorizeFailed": "Не удалось расцветить изображение", "noZaps": "Нет доступных zaps", "createFailed": "Не удалось создать изображение", "mediaReceived": "Медиа успешно получено"}, "error": {"fetchImages": "Не удалось получить изображения", "delete": "Не удалось удалить", "download": "Не удалось загрузить", "createImage": "Не удалось создать изображение", "noZaps": "Нет доступных zaps", "insufficientZaps": "Пожалуйста, приобретите больше zaps", "colorizeImage": "Не удалось расцветить изображение", "fetchVideos": "Не удалось загрузить видео", "upscaleFailed": "Не удалось увеличить разрешение видео", "generateComicFailed": "Не удалось создать комикс, попробуйте ещё раз.", "failedPost": "Не удалось опубликовать", "noImageError": "Давайте начнём с создания нескольких изображений, чтобы двигаться дальше", "uploadImageFailed": "Не удалось загрузить изображение", "videoInterpolationFailed": "Не удалось выполнить интерполяцию видео", "failedToGeneratePrompt": "Не удалось создать запрос", "invalidVideoUrl": "Недействительный URL видео", "generationModelNotFound": "Модель для генерации не найдена", "generationTaskNotFound": "Задача на генерацию не найдена", "invalidParams": "Некорректные параметры", "generationResultFailed": "Не удалось получить результат генерации", "videoGenerationFailed": "Ой! Создать видео не удалось. Попробуйте выбрать другую модель или скорректировать ваш запрос — некоторые модели могут блокировать чувствительный контент.", "imageGenerationFailed": "Ой! Создать изображение не удалось. Попробуйте выбрать другую модель или скорректировать ваш запрос — некоторые модели могут блокировать чувствительный контент.", "sensitiveContent": "Не удалось сгенерировать: введённые данные или результат были помечены как конфиденциальные. Пожалуйста, попробуйте снова с другими данными.", "imageExportFailed": "Экспорт изображения не удался. Попробуйте еще раз.", "autoPostFailed": "Автоматическая публикация не удалась. Пожалуйста, попробуйте опубликовать вручную.", "uploadSuccess": "Загрузка успешно завершена!", "uploadFailed": "Не удалось загрузить", "invalidFileType": "Недопустимый тип файла. Пожалуйста, загружайте только изображения или видео.", "unsupportedFileType": "Тип файла не поддерживается. Пожалуйста, загружайте только изображения или видео.", "failedToProcessFile": "Не удалось обработать файл. Попробуйте еще раз.", "failedToPost": "Не удалось опубликовать. Попробуйте еще раз.", "someImagesGenerationFailed": "Не удалось сгенерировать некоторые изображения. Пожалуйста, попробуйте ещё раз."}, "success": {"delete": "Удаление выполнено успешно", "colorizeImage": "Цветизация изображения прошла успешно", "upscaleVideo": "Разрешение видео успешно увеличено", "downloaded_and_share": "Изображение успешно загружено! Поделитесь им с друзьями прямо сейчас!", "download": "Загрузка успешна", "copy": "Копирование выполнено успешно", "videoInterpolationSuccess": "Интерполяция видео успешно выполнена", "publish": "Публикация прошла успешно", "share": "Контент успешно опубликован!", "shareLinkCopied": "Ссылка на контент скопирована в буфер обмена! Поделитесь с друзьями сейчас!", "uploadSuccess": "Загрузка завершена успешно!"}, "video": {"upscale": {"fetchFailed": "Не удалось получить видео", "deleteSuccess": "Удаление выполнено успешно", "deleteFailed": "Не удалось удалить", "downloadFailed": "Не удалось загрузить", "purchaseMoreZaps": "Пожалуйста, приобретите больше zaps", "upscaleFailed": "Не удалось улучшить видео", "upscaleSuccess": "Видео успешно улучшено!", "noZaps": "Нет доступных zaps", "invalidVideoUrl": "Недействительный URL видео", "uploadFailed": "Не удалось загрузить"}, "interpolation": {"success": "Интерполяция видео прошла успешно", "failed": "Не удалось выполнить интерполяцию видео"}, "styleTransfer": {"success": "Видео успешно создано!", "successWithTime": "Видео создано за {{duration}}!", "failed": "Не удалось создать видео. Попробуйте ещё раз.", "frameExtracted": "Первый кадр успешно извлечён!", "styleApplied": "Стиль успешно применён!", "referenceApplied": "Референсное изображение успешно применено!", "useReferenceFailed": "Не удалось использовать референсное изображение", "trimmed": "Видео обрезано до {{duration}} секунд", "processedFirstSeconds": "Будут обработаны только первые {{duration}} секунд видео", "timeout": "Время на генерацию видео истекло через 10 минут", "processingFailed": "Не удалось обработать видео, будет использован оригинальный файл", "videoDeleted": "Видео успешно удалено!", "styleTransferFailed": "Не удалось передать стиль. Попробуйте ещё раз.", "invalidStyleResponse": "Недействительный ответ от API передачи стиля", "uploadExtractedFrameFailed": "Не удалось загрузить извлечённый кадр", "uploadReferenceFailed": "Не удалось загрузить референсное изображение", "uploadVideoFailed": "Не удалось загрузить видео", "videoGenerationFailed": "Не удалось создать видео. Попробуйте ещё раз.", "videoGenerationTimeout": "Время на генерацию видео истекло через 10 минут", "noPredictionId": "От API генерации видео не получено идентификатора предсказания", "unexpectedOutputFormat": "Неожиданный формат вывода от генерации видео", "noVideoUrlFound": "В выводе генерации не найден URL видео", "missingVideoOrFrame": "Отсутствует видео или стилизованный кадр для генерации", "downloadFailed": "Не удалось загрузить. Попробуйте ещё раз.", "deleteFailed": "Не удалось удалить. Попробуйте ещё раз.", "durationWarning": "Будут обработаны только первые 5 секунд.", "uploadVideoFirst": "Сначала загрузите видео для извлечения первого кадра", "extractingFrame": "Извлечение первого кадра из видео...", "durationInfo": "Исходное видео: {{original}} секунд, будут использованы первые {{selected}} секунд для генерации", "videoTrimmed": "Видео обрезано до {{duration}} секунд для генерации", "trimFailedUsingOriginal": "Не удалось обрезать видео, будет использовано оригинальное видео", "videoGenerationStarted": "Началось создание вашего стилизованного видео...", "videoGeneratedWithTime": "Видео успешно создано за {{duration}}!", "referenceImageCropped": "Референсное изображение было автоматически обрезано, чтобы соответствовать размерам кадра", "autoCropFailed": "Автообрезка не удалась, будет использовано оригинальное изображение. Убедитесь, что ваш референс соответствует композиции кадра.", "frameExtractionEmpty": "Извлеченные кадры отсутствуют", "frameExtractionAllRetries": "Не удалось извлечь кадр из видео после всех попыток", "retryingFrameExtraction": "Повторная попытка извлечения кадра (попытка {{attempt}})...", "safariTimeoutAdvice": "Обнаружена задержка в Safari. Попробуйте использовать более короткое видео или другой браузер, например, Chrome, для более эффективной обработки видео.", "safariVideoTooLong": "Видео длится более 10 секунд. Пожалуйста, укоротите видео до 10 секунд или используйте браузер Chrome для автоматической обрезки.", "safariDurationCheckFailed": "Не удалось проверить продолжительность видео. Пожалуйста, попробуйте еще раз."}}, "warnings": {"durationWarning": "Видео длится более 5 секунд и будет обрезано.", "uploadVideoFirst": "Пожалуйста, сначала загрузите видео", "extractingFrame": "Извлечение кадра из видео..."}, "talking-head": {"invalidAudio": "Недействительный аудиоформат. Используйте MP3 или WAV", "modelsFetchFailed": "Не удалось получить модели", "login": "Пожалуйста, войдите, чтобы использовать эту функцию", "imageRequired": "Пожалуйста, выберите изображение", "audioRequired": "Пожалуйста, выберите аудиофайл", "noCredit": "Недостаточно кредитов", "success": "Видео с анимацией головы успешно создано", "failure": "Не удалось создать видео", "audioDurationExceeded": "Длительность аудио превышает лимит в 2 минуты. Будут использованы только первые 2 минуты.", "imageTooLarge": "Размер файла изображения слишком большой. Максимальный размер: {{maxSize}}MB", "audioTooLarge": "Размер аудиофайла слишком большой. Максимальный размер: {{maxSize}}MB", "filesSizeTooLarge": "Размер файлов слишком большой. Пожалуйста, используйте файлы меньшего размера для изображений и аудио.", "compressingImage": "Сжатие изображения для оптимальной загрузки...", "imageCompressionFailed": "Не удалось сжать изображение. Пожалуйста, используйте файл изображения меньшего размера.", "requestTooLarge": "Запрос слишком большой. Пожалуйста, используйте файлы меньшего размера."}, "info": {"compressing": "Архивация файла...", "uploading": "Загрузка файла..."}}