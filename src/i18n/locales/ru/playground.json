{"meta": {"title": "AI Playground - <PERSON><PERSON><PERSON>", "description": "Универсальная AI-платформа для творчества: создавайте аниме, мангу, манхву, комиксы и легко меняйте стили. Превращайте ваши фотографии, эскизы и 3D-рендеры в профессиональные аниме или комиксы с помощью передовых инструментов на базе ИИ.", "keywords": "AI playground, генерация аниме, трансформация в стиль манги, создание манхвы, инструменты AI для комиксов, генератор аниме-персонажей, создание панелей манги, AI для раскраски манхвы, создание вебтунов, пакетный конвертер комиксов, AI для создания аниме-ава<PERSON>ар<PERSON>, генератор стикеров, анимация комикс-панелей, перенос стиля иллюстраций, готовые активы для анимации, инструментарий для комиксов на базе AI, креативная AI-студия, платформа для цифрового аниме-арта"}, "hero": {"title": "AI Playground", "description": "Универсальная AI-платформа для художников, авторов манги, комиксистов и аниматоров. Мгновенно создавайте, конвертируйте и преобразуйте изображения, фотографии, иллюстрации и 3D-рендеры в аниме, мангу, манхву, комиксы, вебтуны и многое другое! Используйте продвинутые AI-модели для лёгкой смены стилей и профессиональной работы."}, "howItWorks": {"title": "Как работает AI Playground", "step1": {"title": "Загрузите фотографии, рендеры или иллюстрации", "content": "Загрузите исходные материалы (фотографии в форматах JPG, PNG, WEBP, 3D-рендеры, иллюстрации персонажей, панели манги/комиксов или наброски от руки). Наша AI-платформа анализирует композицию, структуру и визуальный стиль для достижения оптимальных результатов преобразования."}, "step2": {"title": "Выберите и настройте стиль", "content": "Выбирайте из множества стилей: аниме, манга, манхва, мультфильмы, вебтуны и цифровое искусство (например, графика тушью, cel-shading, акварель, пиксельная графика или чиби). Настройте цветовые палитры, толщину линий и другие параметры по своему вкусу."}, "step3": {"title": "Мгновенное AI-преобразование стиля", "content": "Доверьте мощным AI-моделям автоматическое применение аутентичного стиля аниме, манги или комиксов. Просматривайте и преобразуйте свои изображения с помощью современных нейросетей, обученных на различных художественных стилях и визуальном контенте."}, "step4": {"title": "Скачивайте, делитесь и управляйте своими работами", "content": "Скачивайте свои работы в высоком разрешении, готовые для печати, публикации цифровых комиксов или обмена в социальных сетях. Управляйте активами, создавайте эпизоды вебтунов, наборы стикеров или листы персонажей с помощью удобных инструментов."}}, "examples": {"title": "Примеры AI Playground", "inputAlt": "Изображение до AI-преобразования", "outputAlt": "Изображение, преобразованное в аниме/комикс с помощью AI", "inputLabel": "Исходное изображение", "outputLabel": "AI-преобразование", "style": {"anime": "Стиль аниме", "manga": "Стиль манги", "manhwa": "Стиль манхвы", "webtoon": "Стиль вебтуна/комикса"}}, "benefits": {"title": "Почему стоит выбрать AI Playground?", "feature1": {"title": "🎨 Продвинутый AI-перенос стиля", "content": "Лучшие в индустрии нейросети для стилей аниме, манги, манхвы, вебтунов и мультфильмов. Добейтесь высококачественного преобразования стиля, сохраняя композицию и эмоции вашего искусства, добавляя аутентичные линейные рисунки, cel-shading и колористику."}, "feature2": {"title": "🗂️ Эффективная обработка изображений", "content": "Загружайте, конвертируйте и управляйте изображениями, листами персонажей, панелями комиксов или иллюстрациями без труда. Разработано для оптимизации рабочего процесса и удобства использования."}, "feature3": {"title": "💎 Высокое разрешение и профессиональное качество", "content": "Скачивайте чёткие PNG, JPG или WEBP изображения высокого качества и разрешения, идеально подходящие для профессионального производства комиксов, печати, цифровой публикации или анимационных активов."}, "feature4": {"title": "🖌️ Универсальная AI-библиотека и настройка", "content": "Исследуйте обширную коллекцию AI-стилей: ани<PERSON><PERSON>, манга, манхва, мультфильм, чиби, фигурки, пиксельная графика и многое другое. Полностью настраивайте результаты в соответствии со своим творческим видением."}, "feature5": {"title": "🚀 Постоянные обновления и актуальные тренды", "content": "Регулярно обновляется новыми AI-моделями, отражающими текущие тренды в аниме, манге, манхве и цифровых комиксах. Ваши работы всегда будут соответствовать популярным художественным стилям."}, "feature6": {"title": "🤝 Управление активами и сообщество", "content": "Безопасно храните, организуйте и делитесь своими аниме и комикс-артами. Присоединяйтесь к совместным творческим проектам или демонстрируйте свои работы в заинтересованном сообществе создателей аниме, манги и комиксов."}}, "faqSection": {"title": "FAQ по AI Playground", "description": "В<PERSON><PERSON>, что нужно знать о создании аниме, манг<PERSON>, манхвы, комик<PERSON><PERSON>, стикеров и анимированных активов из ваших фотографий или иллюстраций с помощью нашей современной AI-платформы."}, "faq": {"question1": "Какие форматы изображений поддерживает AI Playground?", "answer1": "Поддерживает форматы ввода JPG, PNG и WEBP, с выходами PNG и JPG высокого разрешения — идеально подходит для комиксов, вебтунов и иллюстраций, готовых к печати.", "question2": "Может ли AI обрабатывать различные стили анимации, манги и комиксов?", "answer2": "Да! AI-модели этой платформы обучены множеству художественных стилей: японское аниме, классическая манга, полноцветная манхва, вебтуны, стикеры чиби, комиксы в cel-shading, пиксельная графика и многое другое.", "question3": "Насколько точна трансформация стиля AI?", "answer3": "Благодаря продвинутым нейросетям, обученным на различных художественных стилях, наш AI обеспечивает высокую точность преобразования стиля для аниме, манги и манхвы, создавая аутентичные и высококачественные результаты.", "question4": "Подходит ли платформа для профессиональных художников или студий?", "answer4": "Да — результаты профессионального уровня, эффективная обработка, настраиваемые рабочие процессы и загрузки в высоком разрешении делают её идеальной для профессиональных авторов манги, художников комиксов, анимационных студий и независимых иллюстраторов.", "question5": "Какие дополнительные творческие функции доступны?", "answer5": "Помимо преобразования в аниме, создавайте стикеры, листы персонажей, аватары, фоны, спрайт-листы, панели комиксов, фигурки и многое другое. Управляйте своими работами и делитесь ими с помощью интегрированных инструментов для работы с активами и функций сообщества."}, "title": "AI Playground: Превратите свои изображения в аниме, мангу, манхву или комикс", "infoTooltip": "Поддерживает перетаскивание для загрузки фотографий, 3D-рендеров, панелей комиксов, эскизов и многого другого.", "styleSelection": {"label": "Выберите стиль"}, "button": {"convert": "Преобразовать в {{style}}", "zaps": "-{{cost}}/{{credit}}"}, "results": {"title": "Ваши AI-преобразования в аниме, мангу, манхву и комикс", "empty": "Здесь будут отображаться преобразованные работы. Создавайте потрясающие аниме, мангу, манхву и комиксы мгновенно!"}, "deleteModal": {"title": "Удалить работу", "message": "Вы уверены, что хотите удалить это изображение? Это действие необратимо.", "cancel": "Отменить", "delete": "Удалить"}, "toast": {"fetchFailed": "Не удалось загрузить изображения. Проверьте подключение к сети и повторите попытку.", "deleteSuccess": "Работа успешно удалена.", "deleteFailed": "Не удалось удалить. Попробуйте ещё раз.", "downloadFailed": "Не удалось скачать. Обновите страницу и попробуйте ещё раз.", "purchaseZaps": "Пожалуйста, приобретите больше zaps для продолжения.", "conversionDone": "AI-преобразование завершено!", "conversionFailed": "Не удалось преобразовать. Повторите попытку.", "noZaps": "Недостаточно zaps", "generateFailed": "Не удалось сгенерировать анимацию.", "mediaReceived": "Изображение загружено и готово к преобразованию."}, "errors": {"generateImageFailed": "Не удалось сгенерировать изображение. Попробуйте другой ввод.", "resourceExhausted": "Исчерпано использование ресурсов. Пожалуйста, обновите свой тарифный план."}, "styles": {"anime": "Аниме", "ghibliAnime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "koreanManhwa": "Корейская манхва", "cartoon": "Мультфильм", "manga": "Манга", "inkWash": "Рисунок тушью", "chibi": "Стикеры чиби", "spriteSheet": "Спрайт-лист", "simpsons": "Симпсоны", "rickAndMorty": "Рик и Морти", "southPark": "Южный парк", "naruto": "Наруто", "onePiece": "One Piece", "myLittlePony": "My Little Pony", "actionFigure": "Фигурка", "figureInBox": "Фигурка в коробке", "sticker": "Стикер", "watercolor": "Акварель", "lineArt": "Линейный рисунок", "origamiPaperArt": "Искусство оригами", "lego": "Lego", "lowPoly": "Low Poly", "clay": "Пластилиновая анимация", "pixelArt": "Пиксельная графика", "vaporwave": "Vaporwave", "cyberpunk": "К<PERSON>б<PERSON>р<PERSON><PERSON>нк", "dollBox": "Фигурка в коробке 2", "barbieDoll": "Кукла Барби", "characterSheet": "Лист персонажа", "plushie": "Плюшевая игрушка", "badge": "Значок", "standee": "Стенди", "bodyPillow": "Подушка для тела"}, "styleDescriptions": {"anime": "Японское аниме с яркими цветами, динамичным освещением и выразительными чертами.", "ghibliAnime": "Детализированный, причудливый стиль, вдохновленный фирменной анимацией Studio Ghibli.", "koreanManhwa": "Полноцветный стиль манхвы, вдохновленный вебтунами, с плавными градиентами и драматичными панелями.", "cartoon": "Современный мультяшный стиль с жирными контурами и игривыми выражениями.", "manga": "Чёрно-белая японская манга со сложной штриховкой и уникальной разбивкой по панелям.", "inkWash": "Традиционная азиатская живопись тушью, мягкие градиенты и тонкие мазки кистью.", "chibi": "Милые, стилизованные персонажи чиби — идеально подходят для стикеров, значков и смайликов.", "simpsons": "Классический вид Симпсонов: жёлтые оттенки кожи, жирные контуры и мультяшные черты.", "rickAndMorty": "Высоко стилизованный, эксцентричный вид из знаменитого мультсериала.", "southPark": "Плоские, простые формы с яркими цветами — отсылка к стилю Южного парка.", "naruto": "Аниме, вдохновленное дизайном персонажей Наруто, цветовой палитрой и драматическими эффектами.", "onePiece": "Классический стиль манги/аниме One Piece — приключенческий, энергичный, уникальные черты лица.", "myLittlePony": "Причудливые, пастельные и волшебные визуальные эффекты, смоделированные по образцу сериала My Little Pony.", "actionFigure": "Глянцевая 3D-фигурка, идеально подходящая для коллекционирования и макетов для показа.", "figureInBox": "Продемонстрируйте своё искусство как фигурку в коробке — готово для коллекционеров.", "sticker": "Смелый, яркий стиль для привлекающих внимание стикеров и наклеек в стиле аниме/комиксов.", "watercolor": "Мягкие, нежные акварели для создания эффекта нарисованной книги с историями.", "lineArt": "Чистый монохромный линейный рисунок — оптимален для комиксов или дизайна татуировок.", "origamiPaperArt": "Геометрические складки, текстура бумаги — современный взгляд на оригами.", "lego": "Переосмыслите персонажей в виде игривых мини-фигурок Lego.", "lowPoly": "Блочный, многоугольный стиль, напоминающий классическую 3D-анимацию.", "clay": "Вид пластилиновой анимации — ручная работа и тактильные, ностальгические вибрации анимации.", "pixelArt": "Ретро-пиксельный дизайн — ностальгические визуальные эффекты в стиле игр.", "vaporwave": "Пастельные, неоновые и ретрофутуристические стили — вдохновленные vaporwave.", "cyberpunk": "Мрачное, светящееся, технологичное киберпанк искусство.", "dollBox": "Презентация фигурки в коробке, идеально подходит для макетов и предметов коллекционирования.", "barbieDoll": "Стильные цифровые куклы в стиле Барби, глянцевые и модные.", "characterSheet": "Листы персонажей для аниматоров/художников, работающих в игровой индустрии.", "plushie": "Мя<PERSON><PERSON><PERSON>, плюшевый стиль для милого и приятного на ощупь искусства.", "badge": "Круглый стиль значка — отлично подходит для аватаров, значков и социальных сетей.", "standee": "Автономное вырезанное искусство, как товары для аниме-конвенций.", "bodyPillow": "Пользовательское искусство, идеально отформатированное для дакимакур/подушек для тела."}}