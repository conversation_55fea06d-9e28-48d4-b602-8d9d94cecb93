{"auth": {"email": "电子邮件", "email_placeholder": "<EMAIL>", "signin_email": "通过电子邮件登录", "signin_others": "或继续使用", "google": "谷歌", "error": {"title": "发生问题", "description": "您的登录请求失败，请重试。"}, "success": {"title": "检查您的电子邮件", "description": "我们已发送登录链接到您的邮箱，请检查您的垃圾邮件文件夹。"}, "invitation_code": "邀请码", "invitation_code_placeholder": "邀请码（可选）"}, "image": {"generation": {"failed": "生成图像失败"}, "upscale": {"fetchFailed": "获取图像失败", "deleteSuccess": "删除成功", "deleteFailed": "删除失败", "downloadFailed": "下载失败", "purchaseMoreZaps": "请购买更多Zaps", "upscaleFailed": "图像放大失败", "upscaleSuccess": "图像放大成功", "noZaps": "没有可用的Zaps"}}, "text": {"generation": {"failed": "生成文本失败"}}, "character": {"creation": {"failed": "创建角色失败", "characterLimitExceeded": "您已达到角色数量限制！请升级您的计划以创建更多角色。"}}, "common": {"fetchFailed": "获取图像失败", "deleteSuccess": "删除成功", "deleteFailed": "删除失败", "downloadFailed": "下载失败", "noZaps": "没有可用的Zaps", "rateLimitExceeded": "您已达到限制！请升级您的计划以一次生成更多。", "extractFailed": "提取帧失败", "processingFailed": "处理失败", "invalidImage": "请选择一个有效的图像文件"}, "backgroundRemoval": {"purchaseZaps": "请购买更多Zaps以使用背景去除功能", "failed": "背景去除失败", "success": "背景去除成功", "mediaReceived": "媒体接收成功"}, "imageToVideo": {"purchaseMoreZaps": "请购买更多Zaps", "generateSuccess": "动画生成成功", "generateFailed": "动画生成失败", "resourceExhausted": "资源已耗尽", "noZaps": "没有可用的Zaps", "fetchVideosFailed": "获取视频失败", "deleteSuccess": "删除成功", "deleteFailed": "删除失败", "downloadFailed": "下载失败"}, "lineArtColorize": {"fetchFailed": "获取图像失败", "deleteSuccess": "删除成功", "deleteFailed": "删除失败", "downloadFailed": "下载失败", "purchaseMoreZaps": "请购买更多Zaps", "colorizeSuccess": "图像上色成功", "colorizeFailed": "图像上色失败", "noZaps": "没有可用的Zaps", "createFailed": "创建图像失败", "mediaReceived": "媒体接收成功"}, "error": {"fetchImages": "获取图像失败", "delete": "删除失败", "download": "下载失败", "createImage": "创建图像失败", "noZaps": "没有可用的Zaps", "insufficientZaps": "请购买更多Zaps", "colorizeImage": "图像上色失败", "fetchVideos": "获取视频失败", "upscaleFailed": "视频放大失败", "generateComicFailed": "生成漫画失败，请重试。", "failedPost": "发布失败", "noImageError": "请开始创建一些图像以继续", "uploadImageFailed": "上传图像失败", "videoInterpolationFailed": "视频插帧失败", "failedToGeneratePrompt": "生成提示失败", "invalidVideoUrl": "视频URL无效", "generationModelNotFound": "未找到生成模型", "generationTaskNotFound": "未找到生成任务", "invalidParams": "参数无效", "generationResultFailed": "无法获取生成结果", "videoGenerationFailed": "抱歉！视频生成失败。您可以尝试使用不同的模型或调整您的提示，因为有些模型可能对敏感内容进行屏蔽。", "imageGenerationFailed": "抱歉！图像生成失败。您可以尝试使用不同的模型或调整您的提示，因为有些模型可能对敏感内容进行屏蔽。", "sensitiveContent": "生成失败：输入或输出含有敏感内容。请尝试使用其他输入。", "imageExportFailed": "图像导出失败。请重试。", "autoPostFailed": "自动发布失败。请尝试手动发布。", "uploadSuccess": "文件上传成功！", "uploadFailed": "上传失败，请重试。", "invalidFileType": "文件类型不支持。请仅上传图片或视频格式的文件。", "unsupportedFileType": "文件类型不支持。请只上传图片或视频。", "failedToProcessFile": "文件处理失败，请重试。", "failedToPost": "发布失败，请重试。", "someImagesGenerationFailed": "部分图像生成失败，请重试一次。"}, "success": {"delete": "删除成功", "colorizeImage": "图像上色成功", "upscaleVideo": "视频放大成功", "downloaded_and_share": "图像下载成功！与朋友分享吧！", "download": "下载成功", "copy": "复制成功", "videoInterpolationSuccess": "视频插帧成功", "publish": "发布成功", "share": "内容已成功分享！", "shareLinkCopied": "链接已复制到剪贴板！快分享给您的朋友吧！", "uploadSuccess": "文件上传成功！"}, "video": {"upscale": {"fetchFailed": "获取视频失败", "deleteSuccess": "删除成功", "deleteFailed": "删除失败", "downloadFailed": "下载失败", "purchaseMoreZaps": "请购买更多Zaps", "upscaleFailed": "视频放大失败", "upscaleSuccess": "视频放大成功", "noZaps": "没有可用的Zaps", "invalidVideoUrl": "视频URL无效", "uploadFailed": "上传失败"}, "interpolation": {"success": "视频插值成功", "failed": "视频插值失败"}, "styleTransfer": {"success": "视频生成成功！", "successWithTime": "视频生成时间：{{duration}}", "failed": "视频生成失败，请重试。", "frameExtracted": "成功提取第一帧！", "styleApplied": "样式应用成功！", "referenceApplied": "参考图像应用成功！", "useReferenceFailed": "使用参考图像失败", "trimmed": "视频已剪辑至{{duration}}秒", "processedFirstSeconds": "视频将仅处理前{{duration}}秒", "timeout": "视频生成在10分钟后超时", "processingFailed": "视频处理失败，将使用原文件", "videoDeleted": "视频删除成功！", "styleTransferFailed": "样式转换失败，请重试。", "invalidStyleResponse": "样式转换API响应无效", "uploadExtractedFrameFailed": "上传提取帧失败", "uploadReferenceFailed": "上传参考图像失败", "uploadVideoFailed": "上传视频失败", "videoGenerationFailed": "视频生成失败，请重试。", "videoGenerationTimeout": "视频生成在10分钟后超时", "noPredictionId": "未收到视频生成API的预测ID", "unexpectedOutputFormat": "视频生成的输出格式意外", "noVideoUrlFound": "生成输出中未找到视频URL", "missingVideoOrFrame": "生成缺少视频或样式帧", "downloadFailed": "下载失败，请重试。", "deleteFailed": "删除失败，请重试。", "durationWarning": "仅处理视频的前5秒。", "uploadVideoFirst": "请先上传一个视频以提取第一帧", "extractingFrame": "从视频提取第一帧...", "durationInfo": "原视频：{{original}}秒，将使用前{{selected}}秒进行生成", "videoTrimmed": "视频已剪辑至{{duration}}秒进行生成", "trimFailedUsingOriginal": "视频剪辑失败，使用原视频", "videoGenerationStarted": "正在生成您的样式化视频...", "videoGeneratedWithTime": "视频生成成功，耗时{{duration}}！", "referenceImageCropped": "参考图像自动裁剪以匹配帧尺寸", "autoCropFailed": "自动裁剪失败，使用原始图像。请确保您的参考图像匹配帧构图。", "frameExtractionEmpty": "未能提取任何帧", "frameExtractionAllRetries": "多次重试后仍无法从视频中提取帧", "retryingFrameExtraction": "正在重试提取帧（第 {{attempt}} 次尝试）...", "safariTimeoutAdvice": "检测到Safari浏览器超时。请尝试使用较短的视频或更换浏览器（例如，Chrome）以获得更佳的视频处理效果。", "safariVideoTooLong": "视频长度超过10秒。请将视频裁剪为10秒以内，或使用Chrome浏览器自动裁剪。", "safariDurationCheckFailed": "视频时长检查失败，请重试。"}}, "warnings": {"durationWarning": "视频长度超过5秒，将会被剪辑", "uploadVideoFirst": "请先上传一个视频", "extractingFrame": "正在从视频提取帧..."}, "talking-head": {"invalidAudio": "无效的音频格式。请使用MP3或WAV格式", "modelsFetchFailed": "获取模型失败", "login": "请登录以使用此功能", "imageRequired": "请选择一张图像", "audioRequired": "请选择一个音频文件", "noCredit": "积分不足", "success": "动态头像视频生成成功", "failure": "生成视频失败", "audioDurationExceeded": "音频时长超过2分钟限制，使用前2分钟。", "imageTooLarge": "图像文件过大。最大允许尺寸为：{{maxSize}}MB。", "audioTooLarge": "音频文件过大。最大允许尺寸为：{{maxSize}}MB。", "filesSizeTooLarge": "文件过大。请使用较小的图像和音频文件。", "compressingImage": "正在压缩图像以优化上传...", "imageCompressionFailed": "图像压缩失败。请使用较小的图像文件。", "requestTooLarge": "请求过大。请使用较小的文件。"}, "info": {"compressing": "正在压缩文件...", "uploading": "正在上传文件..."}}