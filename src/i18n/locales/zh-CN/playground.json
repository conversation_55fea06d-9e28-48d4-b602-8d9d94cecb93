{"meta": {"title": "AI 乐园 - <PERSON><PERSON><PERSON>", "description": "一站式 AI 创意乐园，专为动漫、漫画艺术生成和无缝风格转换而设计。借助先进的 AI 工具包，轻松将您的照片、草图和 3D 渲染图生成、转换成专业品质的动漫或漫画作品。", "keywords": "AI乐园, 动漫艺术生成, 漫画风格转换, 漫画创作, 漫画风格AI工具, 动漫角色生成器, 漫画分格创建器, 漫画着色AI, Webtoon制作器, 批量漫画转换器, AI动漫头像创建器, 贴纸艺术生成器, 漫画分格动画, 插画风格迁移, 动画素材, AI驱动漫画工具, 创意AI工作室, 数字动漫艺术平台"}, "hero": {"title": "AI 乐园", "description": "为艺术家、漫画创作者、漫画家和动画师打造的一体化 AI 平台。立即将您的图像、照片、插图和 3D 渲染图生成、转换成动漫、漫画、条漫等！利用先进的 AI 模型，实现无缝风格转换和专业艺术创作。"}, "howItWorks": {"title": "如何使用 AI 乐园", "step1": {"title": "上传照片、渲染图或艺术作品", "content": "上传您的源文件（JPG、PNG、WEBP 格式的照片、3D 渲染图、角色插图、漫画/条漫分格或手绘草图）。我们的 AI 乐园会分析构图、结构和视觉风格，以获得最佳转换效果。"}, "step2": {"title": "选择并自定义艺术风格", "content": "从数十种动漫、漫画、条漫、卡通和数字艺术风格中选择，例如水墨画、赛璐珞着色、水彩画、像素艺术或 Q 版。自定义调色板和线条粗细等，以匹配您的创作愿景。"}, "step3": {"title": "即时 AI 驱动的风格转换", "content": "让强大的 AI 模型自动应用正宗的动漫、漫画或条漫风格。使用在各种艺术风格和视觉内容上训练的先进神经网络，预览和转换您的图像。"}, "step4": {"title": "下载、分享和管理创作", "content": "下载您的高分辨率作品，可用于打印、数字漫画出版或社交媒体分享。使用直观的管理工具组织素材，创建条漫剧集、贴纸包或角色设定图。"}}, "examples": {"title": "AI 乐园示例", "inputAlt": "AI 转换前的图像", "outputAlt": "动漫/漫画 AI 转换后的图像", "inputLabel": "原始图像", "outputLabel": "AI 转换后的作品", "style": {"anime": "动漫风格", "manga": "漫画风格", "manhwa": "韩漫风格", "webtoon": "网络漫画风格"}}, "benefits": {"title": "为什么选择 AI 乐园？", "feature1": {"title": "🎨 高级 AI 风格迁移", "content": "业界领先的动漫、漫画、条漫和卡通风格神经网络。实现高质量的风格转换，在添加正宗的线条、赛璐珞着色和色彩的同时，保留您作品的构图和情感。"}, "feature2": {"title": "🗂️ 高效的图像处理", "content": "轻松上传、转换和管理图像、角色设定图、漫画分格或艺术作品。专为简化工作流程和提供便利而设计。"}, "feature3": {"title": "💎 高分辨率、专业品质的输出", "content": "下载清晰、高质量的 PNG、JPG 或 WEBP 格式高分辨率图像——非常适合专业漫画制作、印刷、数字出版或动画素材。"}, "feature4": {"title": "🖌️ 多样的 AI 艺术风格和自定义选项", "content": "探索大量的 AI 驱动风格：动漫、漫画、条漫、卡通、Q 版、玩偶、像素艺术等。完全自定义您的输出，以匹配您的创意愿景。"}, "feature5": {"title": "🚀 紧跟最新潮流", "content": "不断更新新的 AI 模型，反映动漫、漫画和数字漫画的当前趋势。让您的作品始终站在流行的艺术风格前沿。"}, "feature6": {"title": "🤝 素材管理与社区协作", "content": "安全地存储、组织和分享您的动漫和漫画艺术。加入协作创意项目，或向充满激情的动漫、漫画创作者社区展示您的作品。"}}, "faqSection": {"title": "AI 乐园 常见问题", "description": "你想知道的关于使用我们最先进的 AI 创意乐园，从照片或艺术作品中生成动漫、漫画、条漫、贴纸和动画素材的一切。"}, "faq": {"question1": "AI 乐园支持哪些图像格式？", "answer1": "支持 JPG、PNG 和 WEBP 格式的输入，以及高分辨率 PNG 和 JPG 格式的输出——非常适合漫画、条漫和可打印的艺术作品。", "question2": "AI 能否处理不同的动漫、漫画风格？", "answer2": "是的！这个乐园的 AI 模型经过训练，可以处理各种艺术风格：日式动漫、经典漫画、全彩条漫、Q 版贴纸、赛璐珞着色漫画、像素艺术等等。", "question3": "AI 的风格转换有多准确？", "answer3": "由于在各种艺术风格上训练的先进神经网络，我们的 AI 为动漫、漫画和条漫提供高度准确的风格转换——产生正宗、高质量的结果。", "question4": "该平台适合专业艺术家或工作室吗？", "answer4": "是的——专业级的输出、高效的处理、可自定义的工作流程和高分辨率下载，使其成为专业漫画创作者、漫画家、动画工作室和独立插画家的理想选择。", "question5": "有哪些额外的创意功能可用？", "answer5": "除了动漫转换，还可以生成贴纸、角色设定图、头像、背景、精灵表、漫画分格、玩偶等等。使用集成的素材工具和社区功能管理和分享你的作品。"}, "title": "AI 乐园：转换为动漫、漫画或条漫", "infoTooltip": "支持拖放上传照片、3D 渲染图、漫画分格、草图等。", "styleSelection": {"label": "选择艺术风格"}, "button": {"convert": "转换为 {{style}}", "zaps": "-{{cost}}/{{credit}}"}, "results": {"title": "您的 AI 动漫、漫画和条漫转换作品", "empty": "转换后的作品将显示在此处。立即生成令人惊叹的动漫、漫画和条漫艺术！"}, "deleteModal": {"title": "删除作品", "message": "您确定要删除此图片吗？此操作无法撤销。", "cancel": "取消", "delete": "删除"}, "toast": {"fetchFailed": "加载图像失败。请检查您的网络并重试。", "deleteSuccess": "作品删除成功。", "deleteFailed": "删除失败。请重试。", "downloadFailed": "下载失败。请刷新并重试。", "purchaseZaps": "请购买更多 Zaps 以继续。", "conversionDone": "AI 艺术转换完成！", "conversionFailed": "转换失败。请重试。", "noZaps": "Zaps 不足", "generateFailed": "生成动画失败。", "mediaReceived": "图像已上传，可以进行转换。"}, "errors": {"generateImageFailed": "图像生成失败。请尝试其他输入。", "resourceExhausted": "资源使用完毕。请升级您的计划。"}, "styles": {"anime": "动漫", "ghibliAnime": "吉卜力动漫", "koreanManhwa": "韩国漫画", "cartoon": "卡通", "manga": "漫画", "inkWash": "水墨画", "chibi": "Q 版贴纸", "spriteSheet": "精灵图", "simpsons": "辛普森一家", "rickAndMorty": "瑞克和莫蒂", "southPark": "南方公园", "naruto": "火影忍者", "onePiece": "海贼王", "myLittlePony": "小马宝莉", "actionFigure": "手办", "figureInBox": "盒装手办", "sticker": "贴纸", "watercolor": "水彩画", "lineArt": "线条艺术", "origamiPaperArt": "折纸艺术", "lego": "乐高", "lowPoly": "低多边形", "clay": "黏土动画", "pixelArt": "像素艺术", "vaporwave": "蒸汽波", "cyberpunk": "赛博朋克", "dollBox": "玩偶盒 2", "barbieDoll": "芭比娃娃", "characterSheet": "角色设定图", "plushie": "毛绒玩具", "badge": "徽章", "standee": "立牌", "bodyPillow": "抱枕"}, "styleDescriptions": {"anime": "日式动漫，色彩鲜艳，光线动态，表现力丰富。", "ghibliAnime": "细腻、异想天开的风格，灵感来自吉卜力工作室的标志性动画。", "koreanManhwa": "全彩，受网络漫画启发的风格，具有平滑的渐变和戏剧性的分格。", "cartoon": "现代卡通风格，具有大胆的轮廓和俏皮的表情。", "manga": "黑白日式漫画，线条复杂，分格独特。", "inkWash": "传统亚洲水墨画，柔和的渐变和细腻的笔触。", "chibi": "可爱、风格化的 Q 版角色——非常适合贴纸、徽章和表情符号。", "simpsons": "经典的辛普森一家外观：黄色皮肤色调、大胆的轮廓和卡通化的特征。", "rickAndMorty": "高度风格化，古怪的外观，来自著名的动画系列。", "southPark": "扁平、简单的形状，色彩鲜艳——向南方公园风格致敬。", "naruto": "受火影忍者的角色设计、调色板和戏剧效果启发的动漫。", "onePiece": "经典的海贼王漫画/动漫风格——冒险、充满活力、独特的面部特征。", "myLittlePony": "异想天开、柔和和神奇的视觉效果，以小马宝莉系列为模型。", "actionFigure": "光面 3D 手办艺术，非常适合收藏品和展示模型。", "figureInBox": "像盒装手办一样展示你的艺术——为收藏家准备好了。", "sticker": "大胆、鲜艳的风格，用于引人注目的动漫/漫画贴纸和贴花。", "watercolor": "柔和、轻柔的水彩画，带来绘画般的故事书效果。", "lineArt": "干净的单色线条艺术——最适合漫画或纹身设计。", "origamiPaperArt": "几何折叠，纸张纹理——现代折纸风格。", "lego": "将角色重新想象成俏皮的乐高迷你人仔。", "lowPoly": "块状、多边形风格，让人联想到经典的 3D 动画。", "clay": "黏土动画外观——手工制作和触感，怀旧的动画氛围。", "pixelArt": "复古像素设计——怀旧、受游戏启发的视觉效果。", "vaporwave": "柔和、霓虹灯和复古未来主义风格——受蒸汽波启发。", "cyberpunk": "黑暗、发光、技术驱动的赛博朋克艺术。", "dollBox": "盒装玩偶展示，非常适合模型和收藏品。", "barbieDoll": "时尚的芭比娃娃风格数字玩偶，光鲜亮丽，时尚。", "characterSheet": "用于动画/游戏艺术家的角色转盘图。", "plushie": "柔软、毛绒玩具风格，适合可爱和可拥抱的艺术。", "badge": "圆形徽章风格——非常适合头像、别针和社交媒体。", "standee": "独立式剪裁艺术，如动漫展商品。", "bodyPillow": "定制艺术，完美格式化为 dakimakura/抱枕。"}}