{"copyright": "版权所有© {{currentYear}} Caffelabs。保留所有权利。", "dashboard": {"main_nav_documentation": "文档", "main_nav_support": "支持", "sidebar_nav_clusters": "集群", "sidebar_nav_billing": "账单", "sidebar_nav_settings": "设置", "title_text": "创建和管理集群。"}, "marketing": {"introducing": "了解 Saasfly", "get_started": "立即开始", "price_features": "经济高效的生产环境。", "main_nav_features": "功能", "main_nav_pricing": "价格", "main_nav_create_comics": "游乐场", "main_nav_products": "产品", "main_nav_blog": "博客", "main_nav_partners": "合作伙伴", "main_nav_documentation": "文档", "login": "登录", "signup": "注册"}, "price": {"title": "划算透明的价格！", "pricing": "价格", "slogan": "全速起步！", "monthly_bill": "月账单", "annual_bill": "年账单", "annual_info": "每年收费", "monthly_info": "每月收费", "mo": "/月", "contact": "联系我们的客服团队。", "contact_2": "您可以体验订阅，期间不会收费。", "faq": "常见问题", "faq_detail": "查看我们的常见问题解答，快速找到答案。如需更多帮助，请随时联系我们获得个性化支持。", "signup": "注册", "upgrade": "升级", "manage_subscription": "管理订阅", "go_to_dashboard": "前往仪表板"}, "nav": {"features": "功能", "ai_comic_generator": "AI 漫画生成器", "pricing": "价格", "faq": "常见问题", "blog": "博客", "partners": "合作伙伴", "menu": "菜单", "ai_anime_generator": "AI动漫生成工具", "anigen_competition": "AniGen 竞赛", "video_to_video": "视频转视频 AI", "layer_splitter": "分层器", "ai-talking-head": "AI演讲者", "professional_artists": "专业艺术家", "oc_maker": "OC 制作器", "playground": "AI 玩乐场"}, "footer": {"pricing": "价格", "ai_comic_generator": "AI 漫画生成器", "logo_alt": "Komiko AI 标志", "twitter_aria": "Twitter", "twitter_alt": "Twitter", "tiktok_aria": "TikTok", "tiktok_alt": "TikTok", "ai_tools": "AI 工具", "description": "<PERSON><PERSON><PERSON> 是一个一站式 AI 平台，用于创作漫画、插画和动漫作品。", "ai_models": "AI 模型", "all_tools": "所有工具", "comic_tools": "漫画工具", "illustration_tools": "插画工具", "animation_tools": "动画工具", "learn_more": "了解更多", "languages": "语言", "carbon_removal": "碳移除", "blog": "博客", "partners": "合作伙伴"}, "layer_options": {"send_to_back": "移至底层", "send_backward": "向后移", "send_forward": "向前移", "send_to_front": "移至顶层"}, "ai_tools": {"comic_generation": {"title": "生成漫画、动漫和插画", "create_character": {"title": "创建角色", "content": "为您的故事设计原创角色。"}, "create_on_canvas": {"title": "漫画画布", "content": "从空白画布上开始创作漫画。"}, "ai_comic_generator": {"title": "AI 漫画生成器", "content": "输入一个创意，瞬间生成漫画。"}, "ai_comic_factory": {"title": "AI漫画工厂"}, "ai_manga_maker": {"title": "AI漫画创作器"}, "ai_manhwa_generator": {"title": "AI韩漫生成器"}, "ai_manga_generator": {"title": "AI漫画生成器"}, "ai_comic_strip_generator": {"title": "AI连环漫画生成器"}, "ai_comic_book_generator": {"title": "AI漫画书生成器"}, "ai_comic_maker": {"title": "AI漫画创作器"}, "ai_storyboard_generator": {"title": "AI分镜头脚本生成器"}, "free_ai_comic_strip_maker": {"title": "免费AI连环漫画创作器"}, "nsfw_ai_comic_generator": {"title": "NSFW AI成人漫画生成器"}, "ai_marvel_comic_generator": {"title": "AI漫威漫画生成器"}, "batman_comic_ai_generator": {"title": "蝙蝠侠漫画AI生成器"}, "dc_comic_ai_generator": {"title": "DC漫画AI生成器"}, "ai_porn_comic_generator": {"title": "AI色情漫画生成器"}, "ai_erotic_comic_generator": {"title": "AI情色漫画生成器"}, "boomer_comic_ai_generator": {"title": "老派漫画AI生成器"}, "anime_manga_ai_generator": {"title": "动漫AI漫画生成器"}, "ai_webtoon_generator": {"title": "AI网络漫画生成器"}}, "illustration": {"title": "辅助艺术、插图和分镜", "line_art_colorization": {"title": "线稿上色", "content": "为线稿和素描上色。"}, "background_removal": {"title": "背景移除", "content": "使用 AI 从图像中去除背景。"}, "image_upscaling": {"title": "图像放大", "content": "使用 AI 放大您的图像。"}, "image_relighting": {"title": "图像重光", "content": "使用 AI 重新调整图像的光线。"}, "photo_to_anime": {"title": "照片转动漫", "content": "将您的照片转换为动漫或其他风格。"}, "ai_playground": {"title": "AI 游乐场", "content": "将照片转换为动漫、漫画、卡通以及其他超过30种艺术风格。"}, "frame_pack_ai_video_generator": {"title": "FramePack AI 视频创作工具"}, "vidu_ai_video_generator": {"title": "Vidu Q1 视频生成工具"}, "magi_1_video_generator": {"title": "Magi-1 视频制作器"}, "ai-anime-generator": {"title": "AI 动漫生成器"}, "ai_anime_generator": {"title": "动漫艺术生成器", "content": "生成带有角色的动漫风格图像。"}, "sketch_simplification": {"content": "将粗糙杂乱的素描转换为干净的线稿。", "title": "草图优化"}, "veo3_video_generator": {"title": "Veo3 AI 视频制作工具"}, "ai_waifu_generator": {"title": "AI 妹子生成器"}, "ai_naruto_generator": {"title": "AI 火影忍者生成器"}, "random_genshin_character_generator": {"title": "随机原神角色生成器"}, "dragon_ball_ai_generator": {"title": "龙珠 AI 生成器"}, "pokemon_ai_generator": {"title": "宝可梦 AI 生成器"}, "ai_genshin_impact_generator": {"title": "AI 原神生成器"}, "one_piece_ai_generator": {"title": "航海王 AI 生成器"}, "demon_slayer_ai_generator": {"title": "鬼灭之刃 AI 生成器"}, "attack_on_titan_ai_generator": {"title": "进击的巨人 AI 生成器"}, "jujutsu_kaisen_ai_generator": {"title": "咒术回战 AI 生成器"}, "my_hero_academia_ai_generator": {"title": "我的英雄学院 AI 生成器"}, "spy_x_family_ai_generator": {"title": "间谍过家家 AI 生成器"}, "league_of_legends_ai_generator": {"title": "英雄联盟 AI 生成器"}, "ai_disney_pixar_generator": {"title": "AI 迪士尼皮克斯生成器"}, "marvel_ai_generator": {"title": "漫威 AI 生成器"}, "sonic_ai_generator": {"title": "索尼克 AI 生成器"}, "ai_spiderman_generator": {"title": "AI 蜘蛛侠生成器"}, "anime_ai_generator": {"title": "动漫 AI 生成器"}, "anime_art_ai_generator": {"title": "动漫艺术 AI 生成器"}, "studio_ghibli_style_ai_image_generator": {"title": "吉卜力风 AI 图像生成器"}, "female_character_generator": {"title": "女性角色生成器"}, "superhero_ai_generator": {"title": "超级英雄 AI 生成器"}, "villain_maker": {"title": "反派制作器"}, "villain_generator": {"title": "反派生成器"}, "fantasy_character_ai_generator": {"title": "奇幻角色 AI 生成器"}, "tim_burton_character_maker": {"title": "蒂姆·伯顿角色制作器"}, "dbz_ai_generator": {"title": "龙珠Z AI 生成器"}, "ai_elf_generator": {"title": "AI 精灵生成器"}, "ai_princess_generator": {"title": "AI 公主生成器"}, "ai_vampire_generator": {"title": "AI 吸血鬼生成器"}, "ai_pirate_generator": {"title": "AI 海盗生成器"}, "ai_couple_generator": {"title": "AI 情侣生成器"}, "chibi_creator": {"title": "Q版角色制作器"}, "chibi_ai_generator": {"title": "Q版角色 AI 生成器"}, "ai_character_generator_from_text": {"title": "AI 文字生成角色"}, "video_game_character_generator": {"title": "电子游戏角色生成器"}, "ai_fantasy_art_generator": {"title": "AI 奇幻艺术生成器"}, "ai_world_generator": {"title": "AI 世界生成器"}, "ai_landscape_generator": {"title": "AI 风景生成器"}, "ai_map_generator": {"title": "AI 地图生成器"}, "ai_village_generator": {"title": "AI 村庄生成器"}, "ai_mansion_builder": {"title": "AI 豪宅建造器"}, "ai_castle_generator": {"title": "AI 城堡生成器"}, "fantasy_island_generator": {"title": "奇幻岛生成器"}, "city_map_maker": {"title": "城市地图制作器"}, "ai_skybox_generator": {"title": "AI 天空盒生成器"}, "pop_art_creator": {"title": "波普艺术创作器"}, "pop_art_generator": {"title": "波普艺术生成器"}, "ai_watercolor_generator": {"title": "AI 水彩生成器"}, "abstract_ai_art_generator": {"title": "抽象 AI 艺术生成器"}, "ai_renaissance_painting_generator": {"title": "AI 文艺复兴绘画生成器"}, "ai_oil_painter": {"title": "AI 油画师"}, "ai_dark_art_generator": {"title": "AI 黑暗艺术生成器"}, "ai_cartoon_generator_from_text_free": {"title": "AI 免费文字生成卡通"}, "ai_pencil_drawing_generator": {"title": "AI 铅笔画生成器"}, "ai_generated_coloring_pages": {"title": "AI 生成的彩色页"}, "ai_generated_collager": {"title": "AI 生成的拼贴艺术"}, "ai_mural_generator": {"title": "AI 壁画生成器"}, "glitch_art_generator": {"title": "故障艺术生成器"}, "ai_vaporwave_generator": {"title": "AI Vaporwave 生成器"}, "ai_funko_pops_creator": {"title": "AI Funko Pops 制作器"}, "ai_weapon_generator": {"title": "AI 武器生成器"}, "sword_generator": {"title": "剑生成器"}, "ai_monster_generator": {"title": "AI 怪物生成器"}, "bratz_ai_generator": {"title": "Bratz AI 生成器"}, "ai_creature_generator": {"title": "AI 生物生成器"}, "ai_muppet_generator": {"title": "AI 木偶生成器"}, "ai_clothing_generator": {"title": "AI 服装生成器"}, "ai_jersey_generator": {"title": "AI 球衣生成器"}, "ai_t_shirt_designer": {"title": "AI T 恤设计师"}, "midjourney_video_generator": {"title": "Midjourney AI 视频创建工具"}, "anime_character_generator": {"title": "动漫角色生成器"}, "vampire_oc_maker": {"title": "吸血鬼 OC 制作器"}, "anime_oc_maker": {"title": "动漫 OC 制作器"}, "perchance_ai_character_generator": {"title": "Perchance AI 角色生成器"}, "genshin_oc_maker": {"title": "原神 OC 制作器"}, "genshin_oc_generator": {"title": "原神 OC 生成器"}, "naruto_oc_maker": {"title": "火影忍者 OC 制作器"}, "demon_slayer_oc_maker": {"title": "鬼灭之刃 OC 制作器"}, "pokemon_oc_maker": {"title": "宝可梦 OC 制作器"}, "jjk_oc_maker": {"title": "咒术回战 OC 制作器"}, "jujutsu_kaisen_oc_maker": {"title": "咒术回战 OC 制作器"}, "one_piece_oc_maker": {"title": "航海王 OC 制作器"}, "one_piece_characters_generator": {"title": "航海王角色生成器"}, "dragon_ball_oc_maker": {"title": "龙珠 OC 制作器"}, "attack_on_titan_oc_maker": {"title": "进击的巨人 OC 制作器"}, "aot_oc_maker": {"title": "AOT OC 制作器"}, "my_hero_academia_oc_maker": {"title": "我的英雄学院 OC 制作器"}, "spy_x_family_oc_maker": {"title": "间谍过家家 OC 制作器"}, "sonic_oc_maker": {"title": "索尼克 OC 制作器"}, "league_of_legends_oc_maker": {"title": "英雄联盟 OC 制作器"}, "nsfw_oc_maker": {"title": "NSFW OC 制作器"}, "nsfw_character_creator": {"title": "NSFW 角色创作器"}, "disney_oc_maker": {"title": "迪士尼 OC 制作器"}, "marvel_oc_maker": {"title": "漫威 OC 制作器"}, "ai_marvel_character_generator": {"title": "AI 漫威角色生成器"}, "ai_action_figure_generator": {"title": "AI 动作玩偶生成器"}, "ai_character_sheet_generator": {"title": "AI 角色卡生成器"}, "ai_doll_generator": {"title": "AI 人偶生成器"}, "anime_ai_filter": {"title": "动漫 AI 滤镜"}, "studio_ghibli_ai_generator": {"title": "吉卜力工作室 AI 生成器"}, "studio_ghibli_filter": {"title": "吉卜力风格滤镜"}, "anisora_video_generator": {"title": "AniSora AI 视频生成器"}, "ai_anime_porn_generator": {"title": "AI 动漫成人内容生成器"}, "furry_ai_generator": {"title": "兽迷 AI 生成器"}, "ai_art_generator_nsfw": {"title": "AI 艺术生成器 (含 NSFW)"}, "ai_image_generator_to_create_a_webtoon_story": {"title": "网络漫画故事 AI 图像生成器"}, "anime_ai_generator_nsfw": {"title": "动漫 AI 生成器 (含 NSFW)"}, "nsfw_anime_ai_generator": {"title": "NSFW 动漫 AI 生成器"}, "nsfw_ai_art_generator": {"title": "NSFW AI 艺术生成器"}, "mlp_ai_generator": {"title": "小马宝莉 AI 生成器"}, "my_little_pony_ai_generator": {"title": "小马宝莉 AI 生成器"}, "anime_pfp_maker": {"title": "动漫头像生成器"}, "ai_game_asset_generator": {"title": "AI 游戏素材生成器"}, "mlp_oc_maker": {"title": "小马宝莉原创角色生成器"}, "my_little_pony_oc_maker": {"title": "小马宝莉原创角色生成器"}, "visual_novel_character_generator": {"title": "视觉小说角色创建器"}, "random_video_game_character_generator": {"title": "随机游戏角色创建器"}, "moonvalley_ai_video_generator": {"title": "Moonvalley AI 视频制作工具"}, "marey_ai_video_generator": {"title": "Marey AI 视频制作工具"}, "lego_ai_filter": {"title": "乐高AI滤镜"}, "photo_to_line_art": {"title": "照片转线条画"}, "photo_to_simpsons": {"title": "照片转辛普森风格"}, "naruto_ai_filter": {"title": "火影忍者AI滤镜"}, "watercolor_ai_filter": {"title": "水彩AI滤镜"}, "cyberpunk_filter": {"title": "赛博朋克滤镜"}, "emote_maker": {"title": "表情包制作器"}, "ai_sprite_sheet_generator": {"title": "AI精灵图生成器"}, "ai_emoji_generator": {"title": "AI表情符号生成器"}, "ai_emoticon_generator": {"title": "AI符号表情生成器"}, "anime_sticker_maker": {"title": "动漫贴纸制作器"}, "ai_sticker_generator": {"title": "AI贴纸生成器"}, "ai_sprite_generator": {"title": "AI精灵生成器"}, "ai_plush_generator": {"title": "AI毛绒玩具生成器"}, "ai_badge_generator": {"title": "AI徽章生成器"}, "ai_clay_filter": {"title": "AI黏土滤镜"}, "photo_to_minecraft": {"title": "照片转我的世界风格"}, "ai_style_transfer": {"title": "AI风格转换"}, "photo_to_pixel_art": {"title": "照片转像素艺术"}, "ai_cosplay_generator": {"title": "AI角色扮演生成器"}}, "animation": {"title": "创建动画视频", "image_to_animation": {"title": "图像转动画", "content": "从图像生成流畅的动画视频。"}, "inbetweening": {"title": "生成中间帧", "content": "在关键帧之间生成过渡帧。"}, "video_upscaling": {"title": "视频放大", "content": "提升视频的分辨率。"}, "video_interpolation": {"title": "帧插值", "content": "通过插帧使视频更流畅。"}, "layer_splitter": {"content": "将角色图像分割为独立的组件。", "title": "图层分离器"}, "ai_talking_head": {"title": "AI 动态人像", "content": "将静态图像生成动态的说话角色。"}, "video_to_video": {"title": "视频转视频", "content": "使用AI将您的视频转换为不同的风格。"}}, "more": {"title": "还有更多", "content": "探索我们完整的AI工具包，用于绘制漫画、插图和制作动画。"}}, "gridIndex": "网格 {{index}}", "popularity": "{{count}} 人气", "roleplay": "角色扮演", "likes": "{{count}} 赞", "following": "关注中", "follow": "关注", "post_card": {"following": "关注中", "follow": "关注", "the_end": "—— 结束 ——", "discover": "发现", "more_stories": "更多故事", "or_start": "或者开始", "creating_your_own": "创建你自己的", "comments": "评论", "the_end_dash": "- 结束 -", "prompts": "提示", "prompt_copied": "提示已复制到剪贴板", "copy_prompt": "复制提示", "generate_more": "生成更多", "no_prompts": "暂无可用提示😢", "join_discussion": "加入讨论！", "sharing_link_copied": "链接已复制！快与朋友分享！", "image_downloaded": "图像已成功下载！", "download_failed": "图像下载失败，请稍后重试。", "download_image": "下载图像", "copy_link": "复制链接", "more": "更多", "share_text": "刚发现了这个超棒的漫画 \"{{title}}\"！你一定要看看！", "delete": "删除", "confirm_delete": "确认删除", "confirm_delete_message": "确定要删除这条帖子吗？", "cancel": "取消", "delete_success": "帖子删除成功", "delete_failed": "帖子删除失败"}, "uploadFile": {"tapToUpload": "点击上传或将图像拖到此处", "errors": {"invalidExtension": "请选择一个扩展名为：{{accept}} 的文件", "selectImage": "请选择一张图片", "selectVideo": "请选择一个视频", "selectAudio": "请选择一个音频文件", "fileSize": "文件大小不得超过 {{limit}}MB。", "maxDuration": "最大时长为：{{duration}} 分钟", "safariVideoTooLong": "为在 Safari 上获得最佳体验，请上传不超过 10 秒的视频，或使用 Chrome 浏览更长的视频。", "audioSize": "音频文件大小需小于{{limit}}MB"}, "dragAudio": "拖放音频到此处或点击上传", "supportedAudio": "支持格式：MP3, WAV", "warnings": {"imageWillCompress": "图像大小超过{{limit}}MB，将自动压缩", "audioTooLarge": "音频文件大小超过{{limit}}MB，可能影响处理质量"}}, "home": {"create_story": {"title": "新漫画", "subtitle": "生成图像并编写故事", "generate": {"title": "生成", "description": "在几秒钟内通过简单提示生成漫画"}, "from_scratch": {"title": "从头开始", "description": "从空白画布开始，释放您的创意"}}, "create_character": {"title": "新角色", "subtitle": "为您的故事创建角色"}, "create_animation": {"title": "新角色扮演"}}, "result_card": {"generating": "正在生成中", "video_not_supported": "您的浏览器不支持播放视频。", "more_ai_tools": {"title": "更多AI工具", "description": "利用更多相关的AI工具拓展您的创意视野。", "view_more": "查看更多AI工具"}, "generated_by": "生成者", "tools": {"remove_bg": "去背景", "upscale": "高清放大", "anime_style": "动漫风格", "animate": "动画化", "video_upscale": "视频高清放大", "video_interpolate": "视频插值", "character_sheet": "角色表", "oc-maker": "OC 制作", "line_art_colorization": "线稿填色", "title": "工具", "character": "角色", "restyle": "样式重塑", "add_text": "新增文字"}}, "logout": "退出登录", "create_character": "创建角色", "generate_image": "生成图片", "generate": "生成", "create_comic": "制作漫画", "generate_animation": "制作动画", "actions": {"download": "下载", "delete": "删除", "publish": "发布", "cancel": "取消", "confirm": "确认", "post": "发布", "share": "分享", "apply": "应用", "applying": "正在应用...", "generate": "生成", "generating": "正在生成...", "upload": "上传", "uploading": "正在上传...", "extract": "提取", "extracting": "正在提取..."}, "tools": {"image-animation-generator": "图像动画生成器", "video-upscaling": "视频高清放大", "video_upscaling": "视频高清放大", "photo-to-anime": "照片转动漫", "background-removal": "背景移除", "image-upscaling": "图像高清放大", "video-interpolation": "视频插值", "video_interpolation": "视频插值", "ai-character-sheet-generator": "角色表生成器", "oc-maker": "OC 制作", "oc_maker": "OC 制作", "line_art_colorization": "线稿填色", "character": "角色", "restyle": "样式重塑", "layer_splitter": "分层器", "video_to_video": "AI视频转换", "ai-talking-head": "AI演讲者", "video-to-video": "AI视频转换"}, "toasts": {"fetchFailed": "获取失败", "deleteSuccess": "删除成功", "deleteFailed": "删除失败", "downloadFailed": "下载失败", "processingSuccess": "处理成功", "processingFailed": "处理失败", "uploadFailed": "上传失败", "noVideoUrl": "未提供视频 URL", "noZaps": "请购买更多 zaps", "mediaReceived": "已从其他工具接收媒体", "extractFailed": "帧提取失败，请重试。", "invalidResponse": "API返回了无效的响应", "timeout": "操作超时", "missingData": "缺少必要的数据", "unexpectedFormat": "数据格式异常"}, "from_other_tools": "已从 {{tools}} 接收 {{input_type}}", "input_types": {"image": "图像", "character": "角色", "video": "视频"}, "alt": {"generated_image": "生成的图像"}, "success": {"copy": "已复制到剪贴板"}, "unknown": "未知", "exampleResult": "示例结果", "error": {"code": "错误代码：{{code}}", "requestId": "请求 ID：{{id}}", "downloadFailed": "下载失败，请重试。"}, "video_tabs": {"image_to_animation": "图片转动画", "video_to_video": "视频转视频", "talking_head": "动态头像", "image_to_video_sub": "图片转视频", "character_to_video_sub": "角色转视频"}, "audioRecorder": {"duration": "时长", "recording": "正在录音", "preparing": "准备中...", "instructions": "点击下方按钮开始录音", "stop": "停止录音", "start": "开始录音", "record_new": "录制新音频"}, "sampleImage": "示例图片", "sampleVideo": "示例视频", "loading": "正在加载..."}