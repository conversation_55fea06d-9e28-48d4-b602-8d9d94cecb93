{"auth": {"email": "이메일", "email_placeholder": "<EMAIL>", "signin_email": "이메일로 로그인", "signin_others": "또는 계속하기", "google": "구글", "error": {"title": "문제가 발생했습니다.", "description": "로그인 요청이 실패했습니다. 다시 시도해 주세요."}, "success": {"title": "이메일을 확인하세요", "description": "로그인 링크를 보냈습니다. 스팸 폴더도 확인해 주세요."}, "invitation_code": "초대 코드", "invitation_code_placeholder": "초대 코드 (선택 사항)"}, "image": {"generation": {"failed": "이미지 생성 실패"}, "upscale": {"fetchFailed": "이미지 가져오기 실패", "deleteSuccess": "삭제 완료", "deleteFailed": "삭제 실패", "downloadFailed": "다운로드 실패", "purchaseMoreZaps": "추가 zaps를 구매해 주세요", "upscaleFailed": "이미지 고급화 실패", "upscaleSuccess": "이미지 고급화 성공", "noZaps": "사용 가능한 zaps가 없습니다"}}, "text": {"generation": {"failed": "텍스트 생성 실패"}}, "character": {"creation": {"failed": "캐릭터 생성 실패", "characterLimitExceeded": "한도에 도달했습니다! 더 많은 캐릭터를 생성하려면 플랜을 업그레이드하세요."}}, "common": {"fetchFailed": "이미지 가져오기 실패", "deleteSuccess": "삭제 완료", "deleteFailed": "삭제 실패", "downloadFailed": "다운로드 실패", "noZaps": "사용 가능한 zaps가 없습니다", "rateLimitExceeded": "한도에 도달했습니다! 한 번에 더 많이 생성하려면 플랜을 업그레이드하세요.", "extractFailed": "프레임 추출 실패", "processingFailed": "처리 실패", "invalidImage": "유효한 이미지 파일을 선택해 주세요"}, "backgroundRemoval": {"purchaseZaps": "배경 제거기를 사용하려면 추가 zaps를 구매해 주세요", "failed": "배경 제거 실패", "success": "배경 제거 성공", "mediaReceived": "미디어를 성공적으로 받았습니다"}, "imageToVideo": {"purchaseMoreZaps": "추가 zaps를 구매해 주세요", "generateSuccess": "애니메이션 생성 성공", "generateFailed": "애니메이션 생성 실패", "resourceExhausted": "자원이 고갈되었습니다", "noZaps": "사용 가능한 zaps가 없습니다", "fetchVideosFailed": "비디오 가져오기 실패", "deleteSuccess": "삭제 완료", "deleteFailed": "삭제 실패", "downloadFailed": "다운로드 실패"}, "lineArtColorize": {"fetchFailed": "이미지 가져오기 실패", "deleteSuccess": "삭제 완료", "deleteFailed": "삭제 실패", "downloadFailed": "다운로드 실패", "purchaseMoreZaps": "추가 zaps를 구매해 주세요", "colorizeSuccess": "이미지 색상화 성공", "colorizeFailed": "이미지 색상화 실패", "noZaps": "사용 가능한 zaps가 없습니다", "createFailed": "이미지 생성 실패", "mediaReceived": "미디어를 성공적으로 받았습니다"}, "error": {"fetchImages": "이미지 가져오기 실패", "delete": "삭제 실패", "download": "다운로드 실패", "createImage": "이미지 생성 실패", "noZaps": "사용 가능한 zaps가 없습니다", "insufficientZaps": "추가 zaps를 구매해 주세요", "colorizeImage": "이미지 색상화 실패", "fetchVideos": "동영상 불러오기에 실패했습니다.", "upscaleFailed": "동영상 업스케일링에 실패했습니다.", "generateComicFailed": "코믹 생성 실패, 다시 시도해 주세요.", "failedPost": "게시 실패", "noImageError": "몇 가지 이미지를 생성하여 진행해 보세요", "uploadImageFailed": "이미지 업로드 실패", "videoInterpolationFailed": "비디오 보간에 실패했습니다.", "failedToGeneratePrompt": "프롬프트 생성 실패", "invalidVideoUrl": "잘못된 동영상 URL입니다", "generationModelNotFound": "생성 모델을 찾을 수 없습니다.", "generationTaskNotFound": "생성 작업을 찾을 수 없습니다.", "invalidParams": "잘못된 매개변수입니다.", "generationResultFailed": "생성 결과를 가져올 수 없습니다.", "videoGenerationFailed": "죄송합니다! 비디오 생성에 실패했습니다. 다른 모델을 사용해 보시거나 프롬프트를 조정해 보세요. 일부 모델은 민감한 콘텐츠를 제한할 수 있습니다.", "imageGenerationFailed": "죄송합니다! 이미지 생성에 실패했습니다. 다른 모델을 사용해 보시거나 프롬프트를 조정해 보세요. 일부 모델은 민감한 콘텐츠를 제한할 수 있습니다.", "sensitiveContent": "생성 실패: 입력 내용이나 출력 내용에 민감한 정보가 포함되어 있습니다. 다른 입력으로 다시 시도해 주세요.", "imageExportFailed": "이미지를 내보내는 데 실패했습니다. 다시 시도해 주세요.", "autoPostFailed": "자동 게시에 실패했습니다. 직접 게시해 주세요.", "uploadSuccess": "업로드가 성공적으로 완료되었습니다!", "uploadFailed": "업로드에 실패했습니다.", "invalidFileType": "파일 형식이 잘못되었습니다. 이미지 또는 비디오 파일만 업로드하세요.", "unsupportedFileType": "지원되지 않는 파일 형식입니다.\n이미지나 비디오만 업로드해 주세요.", "failedToProcessFile": "파일을 처리할 수 없습니다. 다시 시도해 주세요.", "failedToPost": "게시할 수 없습니다. 다시 시도해 주세요.", "someImagesGenerationFailed": "일부 이미지가 생성되지 않았습니다. 다시 시도해 주세요."}, "success": {"delete": "삭제 완료", "colorizeImage": "이미지 색상화 성공", "upscaleVideo": "동영상 업스케일에 성공했습니다.", "downloaded_and_share": "이미지를 성공적으로 다운로드했습니다! 친구들에게 지금 공유하세요!", "download": "다운로드 성공", "copy": "복사 성공", "videoInterpolationSuccess": "비디오 보간이 성공적으로 완료되었습니다.", "publish": "게시 성공", "share": "콘텐츠가 성공적으로 공유되었습니다!", "shareLinkCopied": "콘텐츠 링크가 클립보드에 복사되었습니다! 이제 친구들과 공유해보세요!", "uploadSuccess": "업로드가 성공적으로 완료되었습니다!"}, "video": {"upscale": {"fetchFailed": "비디오 가져오기 실패", "deleteSuccess": "삭제 완료", "deleteFailed": "삭제 실패", "downloadFailed": "다운로드 실패", "purchaseMoreZaps": "추가 zaps를 구매해 주세요", "upscaleFailed": "비디오 고급화 실패", "upscaleSuccess": "비디오 고급화 성공!", "noZaps": "사용 가능한 zaps가 없습니다", "invalidVideoUrl": "잘못된 비디오 URL", "uploadFailed": "업로드 실패"}, "interpolation": {"success": "비디오 보간 성공", "failed": "비디오 보간 실패"}, "styleTransfer": {"success": "비디오 생성 성공!", "successWithTime": "비디오가 {{duration}}에 생성되었습니다!", "failed": "비디오 생성 실패. 다시 시도해 주세요.", "frameExtracted": "첫 프레임 성공적으로 추출됨!", "styleApplied": "스타일 적용 성공!", "referenceApplied": "참조 이미지 성공적으로 적용됨!", "useReferenceFailed": "참조 이미지를 사용하는 데 실패했습니다", "trimmed": "비디오가 {{duration}}초로 잘렸습니다", "processedFirstSeconds": "비디오가 첫 {{duration}}초만 사용됩니다", "timeout": "비디오 생성이 10분 후 시간 초과되었습니다", "processingFailed": "비디오 처리 실패, 원본 파일 사용", "videoDeleted": "비디오가 성공적으로 삭제되었습니다!", "styleTransferFailed": "스타일 전송 실패. 다시 시도해 주세요.", "invalidStyleResponse": "스타일 전송 API에서 잘못된 응답 받음", "uploadExtractedFrameFailed": "추출한 프레임 업로드 실패", "uploadReferenceFailed": "참조 이미지 업로드 실패", "uploadVideoFailed": "비디오 업로드 실패", "videoGenerationFailed": "비디오 생성 실패. 다시 시도해 주세요.", "videoGenerationTimeout": "비디오 생성이 10분 후 시간 초과되었습니다", "noPredictionId": "비디오 생성 API에서 예측 ID를 받지 못함", "unexpectedOutputFormat": "비디오 생성의 예상치 못한 출력 형식", "noVideoUrlFound": "생성 출력에 비디오 URL이 없음", "missingVideoOrFrame": "생성을 위한 비디오 또는 스타일 프레임이 없음", "downloadFailed": "다운로드 실패. 다시 시도해 주세요.", "deleteFailed": "삭제 실패. 다시 시도해 주세요.", "durationWarning": "처음 5초만 처리됩니다.", "uploadVideoFirst": "먼저 비디오를 업로드하여 첫 프레임을 추출해 주세요", "extractingFrame": "비디오에서 첫 프레임을 추출 중...", "durationInfo": "원본 비디오: {{original}}초, 생성에 첫 {{selected}}초 사용", "videoTrimmed": "생성을 위해 비디오가 {{duration}}초로 잘렸습니다", "trimFailedUsingOriginal": "비디오 자르기 실패, 원본 비디오 사용", "videoGenerationStarted": "스타일이 적용된 비디오 생성 시작...", "videoGeneratedWithTime": "{{duration}}에 비디오 생성 성공!", "referenceImageCropped": "참조 이미지가 자동으로 프레임 크기에 맞게 잘렸습니다", "autoCropFailed": "자동 자르기 실패, 원본 이미지를 사용합니다. 참조가 프레임 구성과 일치하는지 확인하세요.", "frameExtractionEmpty": "프레임 추출 결과가 없습니다.", "frameExtractionAllRetries": "모든 시도 후에도 동영상의 프레임 추출에 실패했습니다.", "retryingFrameExtraction": "프레임 추출을 다시 시도 중입니다 (시도 {{attempt}})...", "safariTimeoutAdvice": "Safari 타임아웃이 발생했습니다. 더 부드러운 비디오 처리를 위해 짧은 동영상을 사용하거나 Chrome과 같은 다른 브라우저를 사용해보세요.", "safariVideoTooLong": "동영상이 10초를 초과합니다. 동영상을 10초로 잘라주시거나 Chrome 브라우저를 사용하여 자동으로 잘라보세요.", "safariDurationCheckFailed": "비디오 지속 시간을 확인할 수 없습니다. 다시 시도해 주세요."}}, "warnings": {"durationWarning": "비디오가 5초보다 길어서 잘릴 것입니다.", "uploadVideoFirst": "먼저 비디오를 업로드해 주세요", "extractingFrame": "비디오에서 프레임 추출 중..."}, "talking-head": {"invalidAudio": "잘못된 오디오 포맷입니다. MP3 또는 WAV를 사용해 주세요", "modelsFetchFailed": "모델 가져오기 실패", "login": "이 기능을 사용하려면 로그인해 주세요", "imageRequired": "이미지를 선택해 주세요", "audioRequired": "오디오 파일을 선택해 주세요", "noCredit": "크레딧이 부족합니다", "success": "토킹 헤드 비디오가 성공적으로 생성되었습니다", "failure": "비디오 생성 실패", "audioDurationExceeded": "오디오 길이가 2분 제한을 초과합니다. 처음 2분만 사용됩니다.", "imageTooLarge": "이미지 파일이 너무 큽니다. 최대 크기: {{maxSize}}MB", "audioTooLarge": "오디오 파일이 너무 큽니다. 최대 크기: {{maxSize}}MB", "filesSizeTooLarge": "파일 크기가 너무 큽니다. 더 작은 이미지 및 오디오 파일을 사용하세요.", "compressingImage": "최적의 업로드를 위해 이미지 압축 중입니다...", "imageCompressionFailed": "이미지 압축에 실패했습니다. 더 작은 이미지 파일을 선택해주세요.", "requestTooLarge": "요청 크기가 너무 큽니다. 더 작은 파일을 사용해주세요."}, "info": {"compressing": "파일을 압축하고 있습니다...", "uploading": "파일을 업로드하고 있습니다..."}}