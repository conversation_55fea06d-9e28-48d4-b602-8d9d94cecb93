{"title": "비디오-투-비디오 AI", "description": "비디오를 비디오-투-비디오 AI 기술로 변환하세요. 원본 움직임과 타이밍을 유지하면서 애니메이션, 만화, 코믹, 웹툰 스타일 등 다양한 스타일로 바꿔드립니다.", "meta": {"title": "비디오-투-비디오 AI | 최고의 비디오 스타일 변환", "description": "비디오를 비디오-투-비디오 AI 기술로 변환하세요. 원본 움직임과 타이밍을 유지하면서 애니메이션, 만화, 코믹, 웹툰 스타일 등 다양한 스타일로 바꿔드립니다.", "fullDescription": "평범한 비디오를 애니메이션, 만화, 코믹 스타일, 웹툰 스타일의 작품으로 바꿔주는 혁신적인 비디오-투-비디오 AI 도구입니다. 고급 비디오 스타일 변환 기술을 통해 스튜디오 지브리 애니메이션, 한국 웹툰, 일본 만화, 수채화 애니메이션, 코믹 스타일 등 다양한 예술적 스타일을 적용하면서도 움직임의 일관성을 유지합니다. 콘텐츠 제작자, 애니메이터, 전문적인 비디오 스타일 변환을 원하는 아티스트에게 적합합니다.", "keywords": "비디오-투-비디오 AI, 비디오 스타일 변환, 애니메이션 비디오 생성기, 만화 비디오 필터, 코믹 스타일 변환, 웹툰 애니메이션, 비디오 애니메이션 변환기, 애니메이션 도구, 만화 비디오 메이커, 애니메이션 스타일 비디오, 코믹 비디오 제작자, 비디오 스타일링, 애니메이션 비디오 생성기, 코믹 비디오 필터, 애니메이션 변환, 만화 스타일 변환"}, "ui": {"title": "비디오-투-비디오 AI", "tooltip": "비디오-투-비디오 AI 및 비디오 스타일 변환 기술로 비디오를 변환하세요.", "generatedVideos": "변환된 스타일 비디오", "emptyState": "애니메이션 및 만화 스타일의 비디오가 여기에 표시됩니다.", "steps": {"uploadVideo": "1. 비디오 업로드", "styleSelection": "스타일 선택", "generateVideo": "3. 비디오 생성"}, "upload": {"dragText": "탭하여 업로드하거나 비디오를 여기로 드래그하세요.", "formatInfo": "MP4, MOV, AVI 지원 • 최대 5초 • 최대 50MB", "extractFrameManually": "수동으로 첫 번째 프레임 추출", "bestResultsTip": "최상의 결과를 얻으려면 단일 장면의 비디오를 사용하세요. 가능한 한 컷 편집 없이 촬영하는 것이 좋습니다.", "safariFormatInfo": "MP4, MOV, AVI 지원 • 최대 10초 • 최대 50MB", "safariNotice": "Safari 사용자 공지", "safariLimitWarning": "동영상 길이는 10초 이하로 제한됩니다. 더 긴 동영상이나 사용자 지정 시간을 제어하려면 Chrome 브라우저를 사용하세요.", "bestResultsTip1": "최상의 결과를 위해 하나의 연속적인 샷으로 촬영된 비디오를 사용하세요.", "bestResultsTip2": "사람 배우가 있는 경우, 첫 번째 프레임에서는 정면 모습을 보여야 합니다. 가능하면, 비디오는 한 명의 주요 배우로 제한하고 적어도 상반신 전체를 포함해야 합니다."}, "duration": {"title": "길이", "description": "비디오 생성 길이를 선택하세요. 길이가 길수록 더 많은 Zap이 소모됩니다.", "originalDuration": "원본: {{duration}}초", "tooLong": "너무 깁니다.", "willBeTrimmed": "{{original}}초에서 {{target}}초로 잘립니다.", "originalLength": "원본 길이", "safariNote": "Safari 감지됨: 최상의 호환성을 위해 원본 동영상 길이 사용", "chromeAdvice": "사용자 지정 시간 제어를 위해서는 Chrome 브라우저를 사용하세요", "safariUseOriginal": "Safari 사용자: 비디오는 최적의 호환성을 위해 원본 시간을 사용합니다"}, "videoMode": {"title": "생성 모드", "human": "인물 비디오 모드", "humanDescription": "인물 피사체 및 인물 비디오에 최적화", "general": "일반 모드", "generalDescription": "모든 피사체 및 장면 유형에서 작동"}, "videoPrompt": {"title": "프롬프트 (선택 사항)", "placeholder": "예: 춤추는 애니메이션 소녀", "description": "비디오 생성 과정을 안내하기 위해 추가 정보를 입력하세요."}, "framePreview": {"original": "원본", "styled": "스타일 적용", "applyingStyle": "스타일 적용 중...", "awaitingStyle": "스타일 대기 중", "selectStyleBelow": "아래에서 스타일을 선택하세요.", "beforeAfterComparison": "스타일 변환 전후 비교", "applyingStyleToFrame": "선택한 스타일을 프레임에 적용 중...", "frameReferenceText": "이 프레임은 비디오 스타일 변환의 기준으로 사용됩니다.", "styleTooltip": "이 스타일의 프레임은 전체 비디오 변환을 안내합니다."}, "styleModes": {"templates": "템플릿", "prompt": "프롬프트", "reference": "참조", "komiko": "<PERSON><PERSON><PERSON>"}, "styleTemplates": {"anime": "애니메이션", "ghibliAnime": "지브리 애니메이션", "koreanManhwa": "한국 웹툰", "cartoon": "만화", "manga": "만화책", "inkWash": "수묵화", "watercolor": "수채화", "lineArt": "라인 아트", "lowPoly": "로우 폴리", "clay": "클레이 애니메이션", "pixelArt": "픽셀 아트", "origami": "종이접기", "lego": "레고", "vaporwave": "베이퍼웨이브", "rickAndMorty": "릭 앤 모티", "southPark": "사우스 파크", "simpsons": "심슨 가족", "naruto": "나루토", "onePiece": "원피스", "myLittlePony": "마이 리틀 포니", "comic": "만화", "miku": "미쿠", "barbie": "바비", "goku": "손오공 (드래곤볼)", "trump": "도널드 트럼프", "princess": "공주 / 왕자", "kimono": "기모노 / 유카타", "superhero": "슈퍼히어로", "magicalGirl": "마법소녀", "hogwarts": "호그와트", "cowboy": "카우보이", "sailorUniform": "세일러복", "pixar": "픽사", "apocalypse": "종말", "magicalWorld": "마법의 세계", "dreamland": "꿈의 나라", "cyberpunk": "사이버펑크", "kpopIdol": "케이팝 아이돌", "cloud": "구름", "mars": "화성", "outerSpace": "우주", "sailorMoon": "세일러문"}, "prompt": {"placeholder": "원하는 스타일 변환을 설명하세요...", "example": "예: \"남자를 나루토로 바꾸세요\", \"이것을 고전적인 90년대 애니메이션 스타일로 바꾸세요\", \"소녀에게 꽃무늬 드레스를 입히세요.\""}, "reference": {"uploadText": "스타일이 적용된 프레임을 참조 이미지로 업로드하세요.", "formatInfo": "JPG, PNG, JPEG, WEBP 지원 • 최대 10MB", "compositionWarning": "참조 이미지가 원본 비디오의 첫 번째 프레임과 정확히 일치하는지 확인하세요."}, "buttons": {"applying": "적용 중...", "useNewReference": "새 참조 사용", "applyNewStyle": "새 스타일 적용", "useReference": "참조 사용", "applyStyle": "스타일 적용", "generateVideo": "비디오 생성", "generatingVideo": "비디오 생성 중...", "generateMore": "비디오 더 생성", "createAnother": "다른 비디오 만들기"}, "separators": {"readyToGenerate": "비디오를 생성할 준비가 되었습니다."}}, "whatIs": {"title": "비디오-투-비디오 AI란 무엇인가요?", "description": "비디오-투-비디오 AI는 고급 비디오 스타일 변환 기술을 사용하여 일반 비디오를 애니메이션, 만화, 코믹, 웹툰 스타일로 변환합니다. 2단계 프로세스를 통해 먼저 선택한 스타일을 참조 프레임에 적용한 다음, 이 스타일이 적용된 프레임을 사용하여 원본 움직임과 타이밍을 유지하면서 전체 비디오를 변환합니다. 스튜디오 지브리 애니메이션, 한국 웹툰, 일본 만화 및 인기 만화 스타일을 포함한 20개 이상의 스타일 중에서 선택하여 비디오 스타일을 변환해보세요."}, "examples": {"title": "비디오-투-비디오 AI 예시", "description": "비디오-투-비디오 AI가 고급 비디오 스타일 변환을 통해 완벽한 움직임 일관성을 유지하면서 비디오를 멋진 애니메이션, 만화, 코믹, 웹툰 스타일로 어떻게 변환하는지 확인해보세요.", "description1": "캐릭터 교체 | 프롬프트: 소녀를 도널드 트럼프로 바꾸기", "description2": "캐릭터 교체 | 프롬프트: 소녀를 세일러문으로 바꾸기", "description3": "스타일 전환 | 실제 춤 영상을 애니메이션 스타일로", "originalVideo": "원본 영상", "animeVideo": "애니메이션 스타일 비디오", "watercolorVideo": "수채화 애니메이션 스타일", "style": "적용된 스타일", "prompt": "사용된 스타일 프롬프트", "description5": "스타일 전환 | 실제 춤 영상을 만화 스타일로", "description6": "장면 변환 | 프롬프트: 배우가 사이버펑크 환경, 즉 발전된 SF 장면 속에서 걷고 있는 이미지로 변경", "animeStyle": "애니메이션 스타일", "comicStyle": "만화 스타일", "promptUsed": "사용된 프롬프트", "animeTransformation": "애니메이션 변환", "description7": "현실의 강아지를 애니메이션 스타일로 변환하여 실제 애완동물이 애니메이션 캐릭터로 변신하는 모습을 보여줍니다.", "description8": "뜨개질하는 손을 애니메이션 스타일로 변환하여 공예 활동에서의 세밀한 동작이 유지되는 것을 보여줍니다."}, "howTo": {"title": "비디오-투-비디오 AI 사용 방법"}, "steps": {"step1": {"title": "비디오 업로드", "content": "비디오-투-비디오 AI 처리를 위해 최대 5초 및 50MB의 모든 비디오 파일 (MP4, MOV, AVI)을 업로드하세요. 최적의 비디오 스타일 변환을 위해 5초보다 긴 비디오는 자동으로 잘립니다."}, "step2": {"title": "비디오 스타일 변환 참조", "content": "첫 번째 프레임을 추출하고 선택한 애니메이션, 만화 또는 코믹 스타일을 적용하여 일관된 비디오 스타일 변환을 위한 참조 가이드를 만듭니다."}, "step3": {"title": "예술 스타일 선택", "content": "스튜디오 지브리 애니메이션, 한국 웹툰, 일본 만화와 같은 20개 이상의 사전 설정 스타일 중에서 선택하거나 텍스트 프롬프트와 참조 이미지를 사용하여 사용자 정의 스타일을 만들어 비디오 스타일을 변환해보세요."}, "step4": {"title": "비디오-투-비디오 AI 생성", "content": "비디오-투-비디오 AI는 고급 비디오 스타일 변환을 통해 원본 움직임, 표정, 타이밍을 모두 보존하면서 스타일이 적용된 참조 프레임을 사용하여 전체 비디오를 변환합니다."}}, "benefits": {"title": "비디오-투-비디오 AI를 사용하는 이유", "description": "비디오-투-비디오 AI는 움직임 보존, 다양한 스타일 옵션, 투명한 가격 책정으로 가장 발전된 비디오 스타일 변환을 제공합니다."}, "features": {"feature1": {"title": "완벽한 움직임 보존", "content": "비디오-투-비디오 AI는 프레임 단위의 완벽한 비디오 스타일 변환 일관성을 통해 애니메이션, 만화 또는 코믹 스타일을 적용하면서 원본 움직임, 표정, 타이밍의 모든 디테일을 유지합니다."}, "feature2": {"title": "20개 이상의 비디오 스타일 변환 옵션", "content": "스튜디오 지브리 애니메이션, 한국 웹툰, 일본 만화, 디즈니 만화, 나루토 스타일 등을 선택하세요. 비디오-투-비디오 AI를 사용하여 텍스트 프롬프트 또는 참조 이미지를 사용하여 사용자 정의 비디오 스타일 변환을 만들 수도 있습니다."}, "feature3": {"title": "전문적인 품질의 결과물", "content": "일관된 비디오 스타일 변환 적용, 부드러운 전환, 깜박임이나 인공물 없이 고화질 애니메이션 및 만화 비디오를 비디오-투-비디오 AI로 생성하세요."}, "feature4": {"title": "합리적인 비용 시스템", "content": "비디오 스타일 변환과 비디오 생성에 대해 별도로 요금이 부과되는 투명한 가격 책정. 비디오-투-비디오 AI를 사용하여 추가 비용 부담 없이 다양한 스타일을 실험해보세요."}, "feature5": {"title": "간단한 2단계 프로세스", "content": "간단한 비디오-투-비디오 AI 워크플로우: 비디오 업로드, 참조 프레임에 비디오 스타일 변환 적용, 전체 비디오 생성. 실시간 진행 상황 추적으로 기술적인 전문 지식이 필요하지 않습니다."}, "feature6": {"title": "자동 최적화", "content": "최적의 비디오 스타일 변환을 위해 자동 트리밍, 형식 지원 (MP4, MOV, AVI), 길이 기반 비용 계산을 통해 스마트 비디오-투-비디오 AI 처리를 지원합니다."}}, "faq": {"title": "비디오-투-비디오 AI FAQ", "description": "비디오-투-비디오 AI 및 비디오 스타일 변환 도구, 프로세스, 비용, 사용 팁에 대한 일반적인 질문과 답변입니다.", "q1": "비디오-투-비디오 AI는 어떻게 작동하나요?", "a1": "비디오-투-비디오 AI는 2단계 비디오 스타일 변환 프로세스를 사용합니다. 1) 참조 프레임을 추출하고 선택한 애니메이션/만화 스타일을 적용합니다. 2) 원본 움직임과 타이밍을 유지하면서 스타일이 지정된 이 프레임을 사용하여 전체 비디오를 변환합니다. 이를 통해 모든 프레임에서 일관된 비디오 스타일 변환 적용이 보장됩니다.", "q2": "비디오-투-비디오 AI의 비디오 형식 요구 사항은 무엇인가요?", "a2": "비디오-투-비디오 AI 처리를 위해 50MB 최대 파일 크기로 MP4, MOV, AVI 형식을 지원합니다. 최적의 비디오 스타일 변환 처리 및 비용 효율성을 위해 비디오 길이는 5초로 제한되며, 길면 자동으로 잘립니다.", "q3": "비디오 스타일 변환은 얼마나 걸리나요?", "a3": "총 비디오-투-비디오 AI 처리 시간은 5~10분 정도 소요됩니다. 비디오 스타일 변환 (1~3분) 및 비디오 생성 (3~7분). 비디오 스타일 변환 진행 상황을 실시간으로 확인할 수 있습니다.", "q4": "비디오-투-비디오 AI 비용은 얼마인가요?", "a4": "길이에 따라 비디오 스타일 변환 및 비디오 생성에 대해 별도로 요금이 부과됩니다. 비디오-투-비디오 AI 비용은 처리 전에 실시간으로 표시되며, 비디오 스타일 변환이 성공적으로 완료된 후에만 크레딧이 차감됩니다.", "q5": "사용자 정의 비디오 스타일 변환 스타일을 만들 수 있나요?", "a5": "네! 20개 이상의 사전 설정 템플릿 (스튜디오 지브리, 한국 웹툰, 일본 만화 등) 중에서 선택하거나 사용자 정의 텍스트 프롬프트를 작성하거나 참조 이미지를 업로드하여 비디오-투-비디오 AI로 고유한 비디오 스타일 변환을 만들 수 있습니다.", "q6": "비디오-투-비디오 AI에 적합한 입력 비디오는 무엇인가요?", "a6": "명확한 피사체, 좋은 조명, 안정적인 움직임, 잘 정의된 특징을 가진 비디오가 최상의 비디오 스타일 변환 결과를 제공합니다. 빠른 움직임, 어둡거나 흐릿한 영상은 피하는 것이 좋습니다. 사람 또는 명확한 객체가 있는 5초 미만의 비디오가 비디오 스타일 변환에 가장 적합합니다."}}