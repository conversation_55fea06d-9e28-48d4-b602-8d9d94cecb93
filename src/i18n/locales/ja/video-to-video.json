{"title": "ビデオ変換AI", "description": "ビデオ変換AI技術で、動画を新たな表現へと進化させましょう。高度なビデオスタイル変換により、オリジナルの動きやタイミングを維持しながら、どんな動画もアニメ、カートゥーン、漫画、マンガ風に変換できます。", "meta": {"title": "ビデオ変換AI | 最高のビデオスタイル変換", "description": "ビデオ変換AI技術で、動画を新たな表現へと進化させましょう。高度なビデオスタイル変換により、オリジナルの動きやタイミングを維持しながら、どんな動画もアニメ、カートゥーン、漫画、マンガ風に変換できます。", "fullDescription": "ビデオ変換AIの革新的なツールで、ありふれた動画をアニメの傑作、カートゥーンアニメーション、漫画のようなビジュアル、マンガの美へと昇華させます。高度なビデオスタイル変換技術は、スタジオジブリ風アニメ、韓国マンガ、日本漫画、水彩アニメ、カートゥーンスタイルなど、様々な芸術的スタイルを適用しながら、自然な動きの一貫性を保ちます。プロレベルのビデオスタイル変換を求めるコンテンツクリエイター、アニメーター、アーティストに最適です。", "keywords": "ビデオ変換AI, ビデオスタイル変換, アニメ動画ジェネレーター, カートゥーン動画フィルター, 漫画スタイル変換, マンガアニメーション, 動画アニメ変換, アニメーションツール, カートゥーン動画メーカー, アニメスタイル動画, 漫画動画クリエイター, ビデオスタイル化, アニメーション動画ジェネレーター, コミック動画フィルター, アニメ変換, カートゥーンスタイル変換"}, "ui": {"title": "ビデオ変換AI", "tooltip": "ビデオ変換AIとビデオスタイル変換技術で、動画を新たな表現へと進化させましょう。", "generatedVideos": "スタイル変換後の動画", "emptyState": "アニメやカートゥーンスタイルに変換された動画がここに表示されます。", "steps": {"uploadVideo": "1. 動画をアップロード", "styleSelection": "スタイルを選択", "generateVideo": "3. 動画を生成"}, "upload": {"dragText": "タップしてアップロード、またはここに動画をドラッグ", "formatInfo": "MP4, MOV, AVI対応 • 最大5秒 • 最大50MB", "extractFrameManually": "手動で最初のフレームを抽出", "bestResultsTip": "最高の仕上がりを得るには、ワンシーンを撮影した動画をご利用ください。ワンカット撮影が理想的です。", "safariFormatInfo": "MP4、MOV、AVIに対応しています • 最長10秒 • 最大50MB", "safariNotice": "Safariユーザーの皆様へ", "safariLimitWarning": "ビデオの長さは10秒以内に制限されています。より長時間のビデオやカスタム期間の設定には、Chromeブラウザをご利用ください。", "bestResultsTip1": "最良の結果を得るには、連続したシーンで撮影されたビデオを使用してください。", "bestResultsTip2": "人物が出演している場合、最初のフレームは正面を向いた姿勢を示すべきです。理想的には、ビデオには1人の主要な人物が登場し、少なくとも上半身全体が含まれていることが望ましいです。"}, "duration": {"title": "長さ", "description": "動画生成の長さを選択してください。時間が長いほど、より多くのZapを消費します。", "originalDuration": "オリジナル: {{duration}}秒", "tooLong": "長すぎます", "willBeTrimmed": "{{original}}秒から{{target}}秒にトリミングされます", "originalLength": "元の長さ", "safariNote": "Safariを使用しています：最適な互換性を確保するために元のビデオの長さを使用します", "chromeAdvice": "カスタム期間の設定には、Chromeブラウザをご利用ください", "safariUseOriginal": "Safariをご利用の方へ: ビデオは最適な互換性のために元の期間で再生されます"}, "videoMode": {"title": "生成モード", "human": "人物動画モード", "humanDescription": "人物やポートレート動画に最適化されています", "general": "一般モード", "generalDescription": "あらゆる被写体やシーンタイプに対応"}, "videoPrompt": {"title": "プロンプト (オプション)", "placeholder": "例: アニメの女の子が踊っている", "description": "動画生成をより詳細に指示できます。"}, "framePreview": {"original": "オリジナル", "styled": "スタイル適用後", "applyingStyle": "スタイルを適用中...", "awaitingStyle": "スタイル適用待ち", "selectStyleBelow": "下のスタイルを選択してください", "beforeAfterComparison": "スタイル変換のビフォーアフター", "applyingStyleToFrame": "選択したスタイルをフレームに適用中...", "frameReferenceText": "このフレームがビデオスタイル変換のリファレンスとして使用されます。", "styleTooltip": "このスタイルが適用されたフレームが、動画全体の変換をガイドします。"}, "styleModes": {"templates": "テンプレート", "prompt": "プロンプト", "reference": "リファレンス"}, "styleTemplates": {"anime": "アニメ", "ghibliAnime": "ジブリ風", "koreanManhwa": "韓国マンガ", "cartoon": "カートゥーン", "manga": "漫画", "inkWash": "水墨画", "watercolor": "水彩", "lineArt": "線画", "lowPoly": "ローポリ", "clay": "クレイアニメーション", "pixelArt": "ピクセルアート", "origami": "折り紙", "lego": "レゴ", "vaporwave": "ヴェイパーウェイヴ", "rickAndMorty": "リック・アンド・モーティ", "southPark": "サウスパーク", "simpsons": "シンプソンズ", "naruto": "ナルト", "onePiece": "ワンピース", "myLittlePony": "マイリトルポニー", "comic": "マンガ", "miku": "ミク", "barbie": "バービー人形", "goku": "悟空（ドラゴンボール）", "trump": "ドナルド・トランプ", "princess": "王子／姫君", "kimono": "着物／浴衣", "superhero": "スーパーヒーロー", "magicalGirl": "魔法少女", "hogwarts": "ホグワーツ", "cowboy": "カウボーイ", "sailorUniform": "セーラー服", "pixar": "ピクサー", "apocalypse": "黙示録", "magicalWorld": "魔法の世界", "dreamland": "夢の国", "cyberpunk": "サイバーパンク", "kpopIdol": "K-POPアイドル", "cloud": "くも", "mars": "かせい", "outerSpace": "うちゅう", "sailorMoon": "セーラームーン"}, "prompt": {"placeholder": "表現したいスタイルを記述してください...", "example": "例: 「男性をナルト風に」、「90年代の懐かしいアニメ風に」、「女の子に花柄のドレスを着せて」"}, "reference": {"uploadText": "スタイル適用済みのフレームをリファレンスとしてアップロード", "formatInfo": "JPG, PNG, JPEG, WEBP対応 • 最大10MB", "compositionWarning": "リファレンス画像が、元の動画の最初のフレームと正確に一致する構図であることを確認してください。"}, "buttons": {"applying": "適用中...", "useNewReference": "新しいリファレンスを使用", "applyNewStyle": "新しいスタイルを適用", "useReference": "リファレンスを使用", "applyStyle": "スタイルを適用", "generateVideo": "動画を生成", "generatingVideo": "動画を生成中...", "generateMore": "さらに動画を生成", "createAnother": "別の動画を作成"}, "separators": {"readyToGenerate": "動画生成の準備完了"}}, "whatIs": {"title": "ビデオ変換AIとは？", "description": "ビデオ変換AIは、高度なビデオスタイル変換技術を用いて、ありふれた動画をアニメ、カートゥーン、漫画、マンガ風のアニメーションへと変換します。独自の2段階プロセスでは、まず選択したスタイルをリファレンスフレームに適用し、次にそのスタイルが適用されたフレームを基に、オリジナルの動きやタイミングを維持しながら動画全体を変換します。スタジオジブリ風アニメ、韓国マンガ、日本漫画、人気カートゥーンなど、20種類以上のスタイルから選択できます。"}, "examples": {"title": "ビデオ変換AIの例", "description": "ビデオ変換AIが、高度なビデオスタイル変換によって、動きの一貫性を保ちながら、動画をアニメ、カートゥーン、漫画、マンガ風の素晴らしい映像へとどのように変貌させるかをご覧ください。", "description1": "キャラクター置換 | プロンプト: 女の子をドナルド・トランプに変える", "description2": "キャラクター置換 | プロンプト: 女の子をセーラームーンに変える", "description3": "スタイル転送 | 実写のダンスビデオをアニメ風に", "originalVideo": "オリジナルビデオ", "animeVideo": "アニメスタイル動画", "watercolorVideo": "水彩アニメーションスタイル", "style": "適用されたスタイル", "prompt": "使用されたスタイルプロンプト", "description5": "スタイル転送 | 実写のダンスビデオをコミック風に", "description6": "シーン変換 | プロンプト: 俳優が歩いているシーンをサイバーパンク風の環境に—非常にSF的なシーン", "animeStyle": "アニメ風", "comicStyle": "コミック風", "promptUsed": "使用したプロンプト", "animeTransformation": "アニメ変身", "description7": "現実の犬をアニメ風に変身させ、ペットがアニメキャラクターになる様子を示す。", "description8": "手編み作品をアニメスタイルに変え、その作業の細かい動きを捉える様子を示す。"}, "howTo": {"title": "ビデオ変換AIの使用方法"}, "steps": {"step1": {"title": "動画をアップロード", "content": "ビデオ変換AIで処理するために、5秒以内、50MBまでの動画ファイル（MP4、MOV、AVI）をアップロードしてください。5秒を超える動画は、最適なビデオスタイル変換のために自動的にトリミングされます。"}, "step2": {"title": "ビデオスタイル変換リファレンス", "content": "最初のフレームを抽出し、選択したアニメ、カートゥーン、または漫画スタイルを適用して、一貫性のあるビデオスタイル変換を実現するためのガイドを作成します。"}, "step3": {"title": "芸術的なスタイルを選択", "content": "スタジオジブリ風アニメ、韓国マンガ、日本漫画など、20種類以上のプリセットスタイルから選択するか、テキストプロンプトやリファレンス画像を使用してカスタムスタイルを作成し、ビデオスタイル変換を行います。"}, "step4": {"title": "ビデオ変換AIで動画を生成", "content": "ビデオ変換AIは、スタイルが適用されたリファレンスフレームを使用して動画全体を変換し、高度なビデオスタイル変換を通じて、元の動き、表情、タイミングをすべて維持します。"}}, "benefits": {"title": "ビデオ変換AIを使用する理由", "description": "ビデオ変換AIは、自然な動きの維持、豊富なスタイルオプション、そして透明性の高い価格設定を備えた、最先端のビデオスタイル変換を提供します。"}, "features": {"feature1": {"title": "完璧な動きの維持", "content": "ビデオ変換AIは、フレーム単位で完璧なビデオスタイル変換の一貫性を保ちながら、アニメ、カートゥーン、または漫画スタイルを適用しつつ、オリジナルの動き、表情、タイミングの細部をすべて忠実に再現します。"}, "feature2": {"title": "20種類以上のビデオスタイル変換オプション", "content": "スタジオジブリ風アニメ、韓国マンガ、日本漫画、ディズニー風カートゥーン、ナルト風など、豊富なスタイルから選択できます。テキストプロンプトやリファレンス画像を使用して、独自のビデオスタイル変換を作成することも可能です。"}, "feature3": {"title": "プロ品質の出力", "content": "ビデオ変換AIは、一貫性のあるビデオスタイル変換、スムーズなトランジション、ちらつきやノイズのない高解像度のアニメおよびカートゥーン動画を生成します。"}, "feature4": {"title": "スマートコストシステム", "content": "ビデオスタイル変換と動画生成の料金を個別に設定した、透明性の高い価格設定。ビデオ変換AIなら、追加コストを気にせずに様々なスタイルを試すことができます。"}, "feature5": {"title": "簡単な2段階プロセス", "content": "ビデオ変換AIのシンプルなワークフロー：動画をアップロードし、リファレンスフレームにビデオスタイル変換を適用し、フル動画を生成するだけ。リアルタイムで進捗状況を確認でき、専門知識は不要です。"}, "feature6": {"title": "自動最適化", "content": "自動トリミング、多様なフォーマットサポート（MP4、MOV、AVI）、最適なビデオスタイル変換のための時間ベースのコスト計算など、ビデオ変換AIはスマートな処理を提供します。"}}, "faq": {"title": "ビデオ変換AIに関するよくある質問", "description": "ビデオ変換AIおよびビデオスタイル変換ツール、プロセス、コスト、ベストプラクティスに関するFAQです。", "q1": "ビデオ変換AIはどのように機能しますか？", "a1": "ビデオ変換AIは、2段階のビデオスタイル変換プロセスを採用しています。1) リファレンスフレームを抽出し、選択したアニメ/カートゥーンスタイルを適用します。2) 元の動きやタイミングを維持しながら、そのスタイルが適用されたフレームを基に動画全体を変換します。これにより、すべてのフレームにおいて一貫したビデオスタイル変換が保証されます。", "q2": "ビデオ変換AIの動画フォーマット要件は何ですか？", "a2": "ビデオ変換AIでは、MP4、MOV、AVI形式をサポートしており、最大ファイルサイズは50MBです。動画は5秒に制限されており、最適なビデオスタイル変換処理とコスト効率のために、それを超える場合は自動的にトリミングされます。", "q3": "ビデオスタイル変換にはどのくらいの時間がかかりますか？", "a3": "ビデオ変換AIの処理時間は合計で5〜10分程度です：ビデオスタイル変換（1〜3分）と動画生成（3〜7分）。ビデオスタイル変換の進行状況はリアルタイムで確認できます。", "q4": "ビデオ変換AIの費用はいくらですか？", "a4": "ビデオスタイル変換と動画生成の料金は、動画の長さに応じて個別に設定されています。ビデオ変換AIの費用は処理前にリアルタイムで表示され、クレジットはビデオスタイル変換が正常に完了した場合にのみ差し引かれます。", "q5": "カスタムのビデオスタイル変換を作成できますか？", "a5": "はい！20種類以上のプリセットテンプレート（スタジオジブリ、韓国マンガ、日本漫画など）から選択するか、カスタムテキストプロンプトを入力するか、またはビデオ変換AIにリファレンス画像をアップロードして、独自のビデオスタイル変換を作成できます。", "q6": "ビデオ変換AIに適した入力動画はどのようなものですか？", "a6": "明確な被写体、良好な照明、安定した動き、そして明確に定義された特徴を持つ動画が、最も優れたビデオスタイル変換の結果をもたらします。ビデオ変換AIを最適に利用するには、速い動き、暗い映像、またはぼやけた映像は避けてください。5秒以内で人物や明確なオブジェクトを含む動画は、ビデオスタイル変換に最適です。"}}