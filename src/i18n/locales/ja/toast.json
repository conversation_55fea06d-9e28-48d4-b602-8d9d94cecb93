{"auth": {"email": "メールアドレス", "email_placeholder": "<EMAIL>", "signin_email": "メールでログイン", "signin_others": "または以下で続行", "google": "Google", "error": {"title": "エラーが発生しました。", "description": "ログインリクエストに失敗しました。もう一度お試しください。"}, "success": {"title": "メールを確認してください", "description": "ログインリンクを送信しました。スパムフォルダも確認してください。"}, "invitation_code": "招待コード", "invitation_code_placeholder": "招待コード（任意）"}, "image": {"generation": {"failed": "画像の生成に失敗しました"}, "upscale": {"fetchFailed": "画像の取得に失敗しました", "deleteSuccess": "正常に削除しました", "deleteFailed": "削除に失敗しました", "downloadFailed": "ダウンロードに失敗しました", "purchaseMoreZaps": "追加のザップを購入してください", "upscaleFailed": "画像のアップスケールに失敗しました", "upscaleSuccess": "画像のアップスケールに成功しました", "noZaps": "利用可能なザップがありません"}}, "text": {"generation": {"failed": "テキストの生成に失敗しました"}}, "character": {"creation": {"failed": "キャラクターの作成に失敗しました", "characterLimitExceeded": "制限に達しました！プランをアップグレードして、より多くのキャラクターを作成してください。"}}, "common": {"fetchFailed": "画像の取得に失敗しました", "deleteSuccess": "正常に削除しました", "deleteFailed": "削除に失敗しました", "downloadFailed": "ダウンロードに失敗しました", "noZaps": "利用可能なザップがありません", "rateLimitExceeded": "制限に達しました！プランをアップグレードして、一度により多く生成してください。", "extractFailed": "フレームの抽出に失敗しました", "processingFailed": "処理に失敗しました", "invalidImage": "有効な画像ファイルを選択してください"}, "backgroundRemoval": {"purchaseZaps": "背景リムーバーを使用するために追加のザップを購入してください", "failed": "背景を削除することに失敗しました", "success": "背景を正常に削除しました", "mediaReceived": "メディアを正常に受信しました"}, "imageToVideo": {"purchaseMoreZaps": "追加のザップを購入してください", "generateSuccess": "アニメーションの生成に成功しました", "generateFailed": "アニメーションの生成に失敗しました", "resourceExhausted": "リソースが使い果たされました", "noZaps": "利用可能なザップがありません", "fetchVideosFailed": "ビデオの取得に失敗しました", "deleteSuccess": "正常に削除しました", "deleteFailed": "削除に失敗しました", "downloadFailed": "ダウンロードに失敗しました"}, "lineArtColorize": {"fetchFailed": "画像の取得に失敗しました", "deleteSuccess": "正常に削除しました", "deleteFailed": "削除に失敗しました", "downloadFailed": "ダウンロードに失敗しました", "purchaseMoreZaps": "追加のザップを購入してください", "colorizeSuccess": "画像のカラー化に成功しました", "colorizeFailed": "画像のカラー化に失敗しました", "noZaps": "利用可能なザップがありません", "createFailed": "画像の作成に失敗しました", "mediaReceived": "メディアを正常に受信しました"}, "error": {"fetchImages": "画像の取得に失敗しました", "delete": "削除に失敗しました", "download": "ダウンロードに失敗しました", "createImage": "画像作成に失敗しました", "noZaps": "利用可能なザップがありません", "insufficientZaps": "追加のザップを購入してください", "colorizeImage": "画像のカラー化に失敗しました", "fetchVideos": "動画の取得に失敗しました。", "upscaleFailed": "動画の高解像度化に失敗しました。", "generateComicFailed": "漫画の生成に失敗しました。もう一度お試しください。", "failedPost": "投稿に失敗しました", "noImageError": "進むためにいくつかの画像を作成してみましょう", "uploadImageFailed": "画像のアップロードに失敗しました", "videoInterpolationFailed": "ビデオの補間に失敗しました。", "failedToGeneratePrompt": "プロンプトの生成に失敗しました", "invalidVideoUrl": "無効なビデオURLです", "generationModelNotFound": "生成モデルが見つかりません。", "generationTaskNotFound": "生成タスクが見つかりません。", "invalidParams": "パラメータが無効です。", "generationResultFailed": "生成結果の取得に失敗しました。", "videoGenerationFailed": "おっと！ビデオの生成に失敗しました。別のモデルを試すか、プロンプトを調整してください。一部のモデルはセンシティブなコンテンツをブロックすることがあります。", "imageGenerationFailed": "おっと！画像の生成に失敗しました。別のモデルを試すか、プロンプトを調整してください。一部のモデルはセンシティブなコンテンツをブロックすることがあります。", "sensitiveContent": "生成に失敗しました: 入力または出力がセンシティブと判断されました。別の入力でお試しください。", "imageExportFailed": "画像のエクスポートに失敗しました。再度お試しください。", "autoPostFailed": "自動投稿に失敗しました。手動で投稿してください。", "uploadSuccess": "アップロードが完了しました！", "uploadFailed": "アップロードに失敗しました。", "invalidFileType": "無効なファイル形式です。画像または動画のみをアップロードしてください。", "unsupportedFileType": "対応していないファイル形式です。画像または動画のみアップロードしてください。", "failedToProcessFile": "ファイルの処理が失敗しました。もう一度お試しください。", "failedToPost": "投稿に失敗しました。再試行してください。", "someImagesGenerationFailed": "一部の画像の生成に失敗しました。もう一度お試しください。"}, "success": {"delete": "正常に削除しました", "colorizeImage": "画像のカラー化に成功しました", "upscaleVideo": "動画の高解像度化に成功しました。", "downloaded_and_share": "画像を正常にダウンロードしました！友達とシェアしましょう！", "download": "ダウンロードに成功しました", "copy": "コピーに成功しました", "videoInterpolationSuccess": "ビデオの補間が成功しました。", "publish": "公開に成功しました", "share": "コンテンツを共有しました！", "shareLinkCopied": "リンクをクリップボードにコピーしました！友達とシェアしましょう！", "uploadSuccess": "アップロードが完了しました！"}, "video": {"upscale": {"fetchFailed": "ビデオの取得に失敗しました", "deleteSuccess": "正常に削除しました", "deleteFailed": "削除に失敗しました", "downloadFailed": "ダウンロードに失敗しました", "purchaseMoreZaps": "追加のザップを購入してください", "upscaleFailed": "ビデオのアップスケールに失敗しました", "upscaleSuccess": "ビデオのアップスケールに成功しました！", "noZaps": "利用可能なザップがありません", "invalidVideoUrl": "無効なビデオURLです", "uploadFailed": "アップロードに失敗しました"}, "interpolation": {"success": "ビデオの補間に成功しました", "failed": "ビデオの補間に失敗しました"}, "styleTransfer": {"success": "ビデオの生成に成功しました！", "successWithTime": "{{duration}}でビデオを生成しました！", "failed": "ビデオ生成に失敗しました。もう一度お試しください。", "frameExtracted": "最初のフレームを正常に抽出しました！", "styleApplied": "スタイルが正常に適用されました！", "referenceApplied": "参照画像が正常に適用されました！", "useReferenceFailed": "参照画像の使用に失敗しました", "trimmed": "ビデオは{{duration}}秒にトリミングされました", "processedFirstSeconds": "ビデオは最初の{{duration}}秒のみを使用して処理されます", "timeout": "ビデオ生成は10分後にタイムアウトしました", "processingFailed": "ビデオ処理に失敗しました。元のファイルを使用します", "videoDeleted": "ビデオが正常に削除されました！", "styleTransferFailed": "スタイル転送に失敗しました。もう一度お試しください。", "invalidStyleResponse": "スタイル転送APIからの無効な応答", "uploadExtractedFrameFailed": "抽出されたフレームのアップロードに失敗しました", "uploadReferenceFailed": "参照画像のアップロードに失敗しました", "uploadVideoFailed": "ビデオのアップロードに失敗しました", "videoGenerationFailed": "ビデオ生成に失敗しました。もう一度お試しください。", "videoGenerationTimeout": "ビデオ生成は10分後にタイムアウトしました", "noPredictionId": "ビデオ生成APIから予測IDが受信されませんでした", "unexpectedOutputFormat": "予期しないビデオ生成の出力形式", "noVideoUrlFound": "生成出力にビデオURLが見つかりませんでした", "missingVideoOrFrame": "生成のためのビデオまたはスタイル付きフレームが不足しています", "downloadFailed": "ダウンロードに失敗しました。もう一度お試しください。", "deleteFailed": "削除に失敗しました。もう一度お試しください。", "durationWarning": "最初の5秒間のみ処理されます。", "uploadVideoFirst": "まずビデオをアップロードして最初のフレームを抽出してください", "extractingFrame": "ビデオから最初のフレームを抽出中...", "durationInfo": "元のビデオ: {{original}}秒、生成に最初の{{selected}}秒を使用します", "videoTrimmed": "生成のためにビデオが{{duration}}秒にトリミングされました", "trimFailedUsingOriginal": "ビデオのトリミングに失敗しました。元のビデオを使用します", "videoGenerationStarted": "スタイル付きビデオの生成を開始しました...", "videoGeneratedWithTime": "{{duration}}でビデオを正常に生成しました！", "referenceImageCropped": "参照画像がフレームの寸法に合わせて自動的にトリミングされました", "autoCropFailed": "自動トリミングに失敗しました。元の画像を使用します。参照がフレーム構成に一致するようにしてください。", "frameExtractionEmpty": "フレーム抽出結果がありません", "frameExtractionAllRetries": "すべての試行後、ビデオからのフレーム抽出に失敗しました", "retryingFrameExtraction": "フレーム抽出を再試行しています（{{attempt}}回目）...", "safariTimeoutAdvice": "Safariでタイムアウトが発生しました。ビデオ処理を改善するには、短いビデオを使用するか、Chromeなどの異なるブラウザを試してください。", "safariVideoTooLong": "ビデオの長さが10秒を超えています。10秒以内になるようにビデオをトリミングするか、またはChromeブラウザで自動トリミングを行ってください。", "safariDurationCheckFailed": "ビデオの長さを確認できませんでした。もう一度お試しください。"}}, "warnings": {"durationWarning": "ビデオが5秒を超えるため、トリミングされます。", "uploadVideoFirst": "まずビデオをアップロードしてください", "extractingFrame": "ビデオからフレームを抽出中..."}, "talking-head": {"invalidAudio": "無効なオーディオ形式です。MP3またはWAV形式を使用してください", "modelsFetchFailed": "モデルの取得に失敗しました", "login": "この機能を利用するにはログインしてください", "imageRequired": "画像を選択してください", "audioRequired": "オーディオファイルを選択してください", "noCredit": "クレジットが不足しています", "success": "トーキングヘッドビデオが正常に生成されました", "failure": "ビデオの生成に失敗しました", "audioDurationExceeded": "オーディオの長さが2分の制限を超えています。最初の2分のみが使用されます。", "imageTooLarge": "画像ファイルが大きすぎます。最大サイズ: {{maxSize}}MBです。", "audioTooLarge": "音声ファイルが大きすぎます。最大サイズ: {{maxSize}}MBです。", "filesSizeTooLarge": "ファイルサイズが大きすぎます。より小さな画像と音声ファイルを使用してください。", "compressingImage": "最適なアップロードのため、画像を圧縮しています...", "imageCompressionFailed": "画像の圧縮に失敗しました。より小さな画像ファイルを使用してください。", "requestTooLarge": "リクエストサイズが大きすぎます。より小さなファイルを使用してください。"}, "info": {"compressing": "ファイルを圧縮中です...", "uploading": "ファイルをアップロード中です..."}}