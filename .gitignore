src/secrets.json
public/images/lineart
.env
env
.next
.vscode/*
!.vscode/settings.json
.vercel/
.vercel
.vercel**
*.js
!scripts/variant-generation/*
!*.ts
!/src/Components/PoseEditor/mannequin.js
!/src/Components/PoseEditor/PoseEditor.js
!tailwind.config.js
!postcss.config.js
!nextui.config.js
!public/sw.js

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.vercel/*
.contentlayer

.env.local

bun.lockb
scripts/variant-generation/config.json
tsconfig.tsbuildinfo