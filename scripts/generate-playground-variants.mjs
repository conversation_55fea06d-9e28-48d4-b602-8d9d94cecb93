#!/usr/bin/env node
import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'
import { execSync } from 'child_process'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 读取variants.txt文件
function readVariantsFile() {
  const variantsPath = path.join(__dirname, 'variants.txt')
  const content = fs.readFileSync(variantsPath, 'utf8')
  
  const variants = []
  const lines = content.split('\n').filter(line => line.trim())
  
  for (const line of lines) {
    // 解析每一行，提取关键词
    let keyword = line.trim()
    
    // 移除特殊标记和URL
    keyword = keyword.replace(/（.*?）/g, '') // 移除中文括号内容
    keyword = keyword.replace(/\(.*?\)/g, '') // 移除英文括号内容
    keyword = keyword.replace(/https?:\/\/[^\s)]+/g, '') // 移除URL
    keyword = keyword.replace(/\|.*$/, '') // 移除管道符后的内容
    keyword = keyword.trim()
    
    if (keyword) {
      variants.push(keyword)
    }
  }
  
  return variants
}

// 将关键字转换为slug格式
function keywordToSlug(keyword) {
  return keyword
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/^-+|-+$/g, '')
}

// 生成单个variant页面
async function generateVariant(keyword) {
  const slug = keywordToSlug(keyword)
  console.log(`\n🚀 生成variant: ${keyword} (${slug})`)
  
  try {
    // 调用generate-variant-page.mjs脚本，添加--text-only参数跳过图片生成
    const command = `node scripts/variant-generation/generate-variant-page.mjs playground "${keyword}" --text-only --force`
    console.log(`执行命令: ${command}`)
    
    execSync(command, {
      stdio: 'inherit',
      cwd: path.join(__dirname, '..')
    })
    
    console.log(`✅ ${keyword} 图片生成完成`)
  } catch (error) {
    console.error(`❌ ${keyword} 生成失败:`, error.message)
  }
}

// 主函数
async function main() {
  console.log('🎯 开始生成playground variants（仅图片模式）...')
  
  // 读取variants列表
  const variants = readVariantsFile()
  console.log(`📋 找到 ${variants.length} 个variants:`)
  variants.forEach((variant, index) => {
    console.log(`  ${index + 1}. ${variant}`)
  })
  
  // 确认是否继续
  console.log('\n🖼️  仅图片模式：将只生成示例图片，跳过文案内容生成。')
  console.log('⚠️  这将生成大量图片，可能需要较长时间和消耗大量AI图片生成额度。')
  console.log('按 Ctrl+C 取消，或按 Enter 继续...')
  
  // 等待用户输入
  await new Promise(resolve => {
    process.stdin.once('data', () => resolve())
  })
  
  // 生成所有variants
  let successCount = 0
  let failCount = 0
  
  for (let i = 0; i < variants.length; i++) {
    const variant = variants[i]
    console.log(`\n📊 进度: ${i + 1}/${variants.length}`)
    
    try {
      await generateVariant(variant)
      successCount++
    } catch (error) {
      console.error(`❌ ${variant} 生成失败:`, error.message)
      failCount++
    }
    
    // 添加延迟避免API限制
    if (i < variants.length - 1) {
      console.log('⏳ 等待2秒...')
      await new Promise(resolve => setTimeout(resolve, 2000))
    }
  }
  
  // 
  console.log('\n🎉 所有variants图片生成完成!')
  console.log(`✅ 成功: ${successCount}`)
  console.log(`❌ 失败: ${failCount}`)
  console.log(`📊 总计: ${variants.length}`)
  console.log('\n💡 提示: 如需生成文案内容，可以稍后使用 --text-only 参数重新运行。')
}

// 运行主函数
main().catch(error => {
  console.error('❌ 脚本执行失败:', error)
  process.exit(1)
})
