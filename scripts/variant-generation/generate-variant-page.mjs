#!/usr/bin/env node
import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

// 获取图片尺寸的简单实现
async function getImageSizeFromBuffer(buffer) {
  try {
    // 尝试使用sharp获取图片尺寸（如果可用）
    try {
      const sharp = await import('sharp')
      const metadata = await sharp.default(buffer).metadata()
      return { width: metadata.width || 1024, height: metadata.height || 1024 }
    } catch (sharpError) {
      // sharp不可用，使用基本的图片头部分析
      return getImageSizeFromHeader(buffer)
    }
  } catch (error) {
    console.warn('⚠️ 无法获取图片尺寸，使用默认值 1024x1024')
    return { width: 1024, height: 1024 }
  }
}

// 从图片头部获取尺寸的简单实现
function getImageSizeFromHeader(buffer) {
  // JPEG
  if (buffer[0] === 0xFF && buffer[1] === 0xD8) {
    let offset = 2
    while (offset < buffer.length - 4) {
      if (buffer[offset] === 0xFF) {
        const marker = buffer[offset + 1]
        if (marker >= 0xC0 && marker <= 0xC3) {
          const height = (buffer[offset + 5] << 8) | buffer[offset + 6]
          const width = (buffer[offset + 7] << 8) | buffer[offset + 8]
          return { width, height }
        }
        offset += 2 + ((buffer[offset + 2] << 8) | buffer[offset + 3])
      } else {
        offset++
      }
    }
  }
  
  // PNG
  if (buffer.slice(0, 8).toString('hex') === '89504e470d0a1a0a') {
    const width = buffer.readUInt32BE(16)
    const height = buffer.readUInt32BE(20)
    return { width, height }
  }
  
  // WebP
  if (buffer.slice(0, 4).toString() === 'RIFF' && buffer.slice(8, 12).toString() === 'WEBP') {
    // 简化的WebP尺寸检测
    const width = buffer.readUInt16LE(26) + 1
    const height = buffer.readUInt16LE(28) + 1
    return { width, height }
  }
  
  // 默认尺寸
  return { width: 1024, height: 1024 }
}

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 将关键字转换为合适的slug格式（用于文件路径和URL）
function keywordToSlug(keyword) {
  return keyword
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-') // 将非字母数字字符替换为连字符
    .replace(/^-+|-+$/g, '') // 移除开头和结尾的连字符
}

// 配置文件路径
const CONFIG_FILE = path.join(__dirname, 'config.json')
const VARIANT_DATA_FILE = path.join(__dirname, '../src/data/variant-pages.json')
const VARIANTS_DIR = path.join(__dirname, '../../src/data/variants')

// 默认配置
const DEFAULT_CONFIG = {
  apiBaseUrl: 'http://localhost:3000',
  sessionToken:'eyJhbGciOiJkaXIiLCJlbmMiOiJBMjU2R0NNIn0..BTa9G1Z89vqckVtI.ZpTyTS-oALBQLd7NbGHqbAweZ-9hKqfEptAMCLrdvptsYFlqum-U1M2bHxaM8Ut9sCwm62NA3wwUpbEnw2bAuDb-sx2LYZJVFe0-auo0WUyAu38zZwCNnACJ7rb01-A9mJoLYwtlAYPH4nitQSqo3ggyK_tLVJhYfwYTlT2k8ErccxCtMAe9OQa5OddIZFJV_zvdg6D6FbvYoqH4Lhq3puKIIwmMYNMETuVzdw0iurBdVZmr3qWO1Am3JeHfjah5WzG0b9JQnA.zZWdTPsZ9_RCgcn2BcJvvw',
  defaultModel: 'AnimagineXL',
  imagesPerVariant: 4, // 修改为4张图片
  skipApiCall: false, // 是否跳过API调用，直接使用占位符图片
}

// 加载配置函数
function loadConfig() {
  try {
    const configData = fs.readFileSync(CONFIG_FILE, 'utf8')
    return {
      ...DEFAULT_CONFIG,
      ...JSON.parse(configData || '{}'),
    }
  } catch (error) {
    console.warn('⚠️  无法读取配置文件，使用默认配置')
    return DEFAULT_CONFIG
  }
}

// 设置配置函数
async function setupConfig() {
  console.log('🔧 配置设置')
  console.log('请在 config.json 文件中设置以下配置:')
  console.log('- apiBaseUrl: API服务器地址')
  console.log('- sessionToken: 会话令牌')
  console.log('- defaultModel: 默认AI模型')
  console.log('- imagesPerVariant: 每个变体的图片数量')

  const configExists = fs.existsSync(CONFIG_FILE)
  if (configExists) {
    console.log(`✅ 配置文件已存在: ${CONFIG_FILE}`)
    const config = loadConfig()
    console.log('当前配置:')
    console.log(JSON.stringify(config, null, 2))
  } else {
    console.log(`❌ 配置文件不存在: ${CONFIG_FILE}`)
    console.log('创建默认配置文件...')
    fs.writeFileSync(CONFIG_FILE, JSON.stringify(DEFAULT_CONFIG, null, 2))
    console.log('✅ 默认配置文件已创建')
  }
}

// 模型配置 - 根据OC Maker实际使用的模型
const MODELS = {
  AnimagineXL: { endpoint: '/api/generateImageAnimagineXL3_1', cost: 30 },
  Animagine: { endpoint: '/api/generateImageAnimagineXL3_1', cost: 30 },
  Gemini: { endpoint: '/api/generateCanvasImageGemini', cost: 30 },
  'Gemini Mini': { endpoint: '/api/generateCanvasImageGeminiMini', cost: 15 },
  'Flux Mini': { endpoint: '/api/generateImageFluxKontextProMini', cost: 20 },
  GPT: { endpoint: '/api/generateImageGPT4O', cost: 50 },
  'GPT Mini': { endpoint: '/api/generateImageGPT4OMini', cost: 25 },
  Neta: { endpoint: '/api/generateImageNeta', cost: 10 },
}

// 图片比例配置
const IMAGE_RATIOS = {
  // 竖屏比例 - 适合人物角色
  'portrait-tall': {
    width: 768,
    height: 1344,
    name: '9:16 portrait',
    suffix: 'tall',
  }, // 9:16
  portrait: {
    width: 768,
    height: 1024,
    name: '3:4 portrait',
    suffix: 'portrait',
  }, // 3:4
  square: { width: 1024, height: 1024, name: '1:1 square', suffix: 'square' }, // 1:1
  // 2:3
  'portrait-2-3': {
    width: 768,
    height: 1152,
    name: '2:3 portrait',
    suffix: '2-3',
  },
}

// 横屏比例配置 - 适合场景环境
const LANDSCAPE_RATIOS = {
  landscape: {
    width: 1344,
    height: 768,
    name: '16:9 landscape',
    suffix: 'landscape',
  }, // 16:9
  'landscape-wide': {
    width: 1024,
    height: 768,
    name: '4:3 landscape',
    suffix: 'wide',
  }, // 4:3
  square: { width: 1024, height: 1024, name: '1:1 square', suffix: 'square' }, // 1:1
}

// 工具类型配置 map
const TOOL_TYPE_CONFIG = {
  'ai-anime-generator': {
    name: 'AI Anime Generator',
    description: 'art generation',
    targetAudience: 'Artists, designers, content creators, and hobbyists interested in creating art',
    contentType: 'art',
    placeholderTemplate: 'A scene in {keyword} style',
    buttonTemplate: 'Create {keyword} Art Free',
    functionalDescription: 'AI {keyword} Generator: an AI tool that creates {keyword} style artwork from text descriptions. First input your text prompt describing the {keyword} scene or character you want, then select the {keyword} art style from various templates, then click Generate Art, and you will get a high-quality {keyword} style artwork.',
    stepTemplates: {
      step1: { title: 'Describe Your Vision', content: 'Explain how to write a good prompt for \'{keyword}\' style. Use an example. Match the detail level of the few-shot example.' },
      step2: { title: 'Customize Your Creation', content: 'Explain available customization options relevant to the art style. Follow example format.' },
      step3: { title: 'Generate Your Art', content: 'Explain the generation process. Match example style.' },
      step4: { title: 'Download and Share', content: 'Encourage users to download and share their \'{keyword}\' creations. Follow example tone.' }
    },
    seoStandard: 'Follow this quality standard for art generation: \'Generate stunning {keyword} scenes with our AI. Bring your creative visions to life in seconds. Try it free!\'',
    keywordFocus: '5-8 core keywords focusing on AI-powered art generation. Include \'AI\' in 2-3 key terms since users specifically search for AI tools. Primary keyword once, then add genuine search terms like \'AI art generator\', \'AI anime generator\', \'anime AI art\', plus relevant non-AI terms users actually type. Prioritize real search intent over keyword stuffing.',
    tips: {
      tip1: 'Tip 1 for \'{keyword}\' art - make it specific and actionable like the example tips.',
      tip2: 'Tip 2 for \'{keyword}\' art - follow the format and depth of the example.',
      tip3: 'Tip 3 for \'{keyword}\' art - match the practical value of the example tips.'
    },
    faq: {
      q1: 'What is {keyword} and how does it work?',
      q2: 'How to create the best {keyword} with AI?',
      q3: 'Is {keyword} {toolName} free to use?',
      q4: 'What makes {keyword} unique compared to other styles?',
      q5: 'Can I use {keyword} creations commercially?'
    },
    skipImages: false,
    ratioOverride: null
  },
  'oc-maker': {
    name: 'OC Maker',
    description: 'character creation',
    targetAudience: 'Artists, writers, game developers, and content creators interested in creating original characters (OCs) for their stories, games, comics, and animations',
    contentType: 'character',
    placeholderTemplate: 'A {keyword} character with blue hair and magical powers',
    buttonTemplate: 'Create {keyword} Characters Free',
    functionalDescription: 'AI {keyword} Character Creator: an AI tool that generates original {keyword} characters from text descriptions. First input your character description including appearance, personality, and traits for your {keyword} character, then select the {keyword} character style from various templates, then click Generate Character, and you will get a unique {keyword} character design.',
    stepTemplates: {
      step1: { title: 'Describe Your Character', content: 'Explain how to describe a {keyword} character\'s appearance, personality, and traits. Use specific examples. Match the detail level of the few-shot example.' },
      step2: { title: 'Customize Character Details', content: 'Explain available customization options for {keyword} characters like appearance, clothing, accessories. Follow example format.' },
      step3: { title: 'Generate Your Character', content: 'Explain the character generation process. Match example style.' },
      step4: { title: 'Finalize and Use', content: 'Encourage users to finalize their {keyword} character design and use it in their projects. Follow example tone.' }
    },
    seoStandard: 'Follow this quality standard for character creation: \'Create unique {keyword} characters with AI. Design original characters with custom appearances and personalities. Try it free!\'',
    keywordFocus: '5-8 core keywords focusing on AI-powered character creation. Include \'AI\' in 2-3 key terms since users specifically search for AI tools. Primary keyword once, then add genuine search terms like \'AI character creator\', \'OC maker\', \'character generator\', plus relevant non-AI terms users actually type. Prioritize real search intent over keyword stuffing.',
    tips: {
      tip1: 'Tip 1 for \'{keyword}\' character creation - make it specific and actionable like the example tips.',
      tip2: 'Tip 2 for \'{keyword}\' character design - follow the format and depth of the example.',
      tip3: 'Tip 3 for \'{keyword}\' character development - match the practical value of the example tips.'
    },
    faq: {
      q1: 'What is {keyword} OC Maker and how does it work?',
      q2: 'How to create the best {keyword} characters with AI?',
      q3: 'Is {keyword} OC Maker free to use?',
      q4: 'What makes {keyword} characters unique?',
      q5: 'Can I use {keyword} characters commercially?'
    },
    skipImages: false,
    ratioOverride: 'portrait-2-3'
  },
  'ai-comic-generator': {
    name: 'AI Comic Generator',
    description: 'comic creation',
    targetAudience: 'Comic creators, storytellers, writers, and content creators interested in creating comics, graphic novels, and visual narratives',
    contentType: 'comic',
    placeholderTemplate: 'A thrilling {keyword} adventure story with heroes and villains',
    buttonTemplate: 'Create {keyword} Comics Free',
    functionalDescription: 'AI {keyword} Comic Generator: an AI tool that creates {keyword} style comics from story descriptions. First input your story plot, characters, and dialogue for your {keyword} comic, then select the {keyword} comic style from various panel layouts and art styles, then click Generate Comic, and you will get a complete {keyword} style comic with panels and speech bubbles.',
    stepTemplates: {
      step1: { title: 'Write Your Story', content: 'Explain how to write a compelling {keyword} comic story with characters, plot, and dialogue. Use specific examples. Match the detail level of the few-shot example.' },
      step2: { title: 'Choose Comic Style', content: 'Explain available {keyword} comic style options like panel layouts, art styles, and visual effects. Follow example format.' },
      step3: { title: 'Generate Your Comic', content: 'Explain the comic generation process with panels, speech bubbles, and visual storytelling. Match example style.' },
      step4: { title: 'Download and Share', content: 'Encourage users to download their {keyword} comic and share it with others. Follow example tone.' }
    },
    seoStandard: 'Follow this quality standard for comic creation: \'Create amazing {keyword} comics with AI. Generate engaging visual stories and narratives instantly. Try it free!\'',
    keywordFocus: '5-8 core keywords focusing on AI-powered comic creation. Include \'AI\' in 2-3 key terms since users specifically search for AI tools. Primary keyword once, then add genuine search terms like \'AI comic generator\', \'comic creator\', \'comic maker\', plus relevant non-AI terms users actually type. Prioritize real search intent over keyword stuffing.',
    tips: {
      tip1: 'Tip 1 for \'{keyword}\' comic creation - make it specific and actionable like the example tips.',
      tip2: 'Tip 2 for \'{keyword}\' comic storytelling - follow the format and depth of the example.',
      tip3: 'Tip 3 for \'{keyword}\' comic publishing - match the practical value of the example tips.'
    },
    faq: {
      q1: 'What is {keyword} Comic Generator and how does it work?',
      q2: 'How to create the best {keyword} comics with AI?',
      q3: 'Is {keyword} Comic Generator free to use?',
      q4: 'What makes {keyword} comics unique?',
      q5: 'Can I use {keyword} comics commercially?'
    },
    skipImages: true,
    ratioOverride: null
  },
  'playground': {
    name: 'AI Style Transfer',
    description: 'style transformation',
    targetAudience: 'Artists, designers, content creators, and hobbyists interested in transforming photos into different artistic styles',
    contentType: 'style',
    placeholderTemplate: 'Transform your photo into {keyword} style',
    buttonTemplate: 'Transform to {keyword} Free',
    functionalDescription: 'AI {keyword} Generator: an AI tool that converts your photos to {keyword} style. First upload your photo or image, then select the {keyword} template, then click Convert to {keyword}, and you will get a photo transformed into {keyword} style.',
    stepTemplates: {
      step1: { title: 'Upload Your Photo', content: 'Explain how to upload and prepare photos for {keyword} style transformation. Use specific examples. Match the detail level of the few-shot example.' },
      step2: { title: 'Select {keyword} Style', content: 'Explain the {keyword} style characteristics and how to select it from available options. Follow example format.' },
      step3: { title: 'Generate Your Art', content: 'Explain the AI transformation process for {keyword} style. Match example style.' },
      step4: { title: 'Download and Share', content: 'Encourage users to download and share their {keyword} style creations. Follow example tone.' }
    },
    seoStandard: 'Follow this quality standard for style transformation: \'Transform photos into stunning {keyword} style with AI. Create unique artistic variations instantly. Try it free!\'',
    keywordFocus: '5-8 core keywords focusing on AI-powered style transformation. Include \'AI\' in 2-3 key terms since users specifically search for AI tools. Primary keyword once, then add genuine search terms like \'AI style transfer\', \'photo to {keyword}\', \'{keyword} filter\', plus relevant non-AI terms users actually type. Prioritize real search intent over keyword stuffing.',
    tips: {
      tip1: 'Tip 1 for \'{keyword}\' style transformation - make it specific and actionable like the example tips.',
      tip2: 'Tip 2 for \'{keyword}\' style creation - follow the format and depth of the example.',
      tip3: 'Tip 3 for \'{keyword}\' artistic effects - match the practical value of the example tips.'
    },
    faq: {
      q1: 'What is {keyword} style transformation and how does it work?',
      q2: 'How to create the best {keyword} style effects with AI?',
      q3: 'Is {keyword} style transformation free to use?',
      q4: 'What makes {keyword} style unique compared to other filters?',
      q5: 'Can I use {keyword} style creations commercially?'
    },
    skipImages: false,
    ratioOverride: null
  }
}

// 获取工具类型配置
function getToolTypeConfig(toolType) {
  return TOOL_TYPE_CONFIG[toolType] || TOOL_TYPE_CONFIG['ai-anime-generator']
}

// 支持的工具类型 - 从index.json文件动态获取并合并配置
function getSupportedToolTypes() {
  try {
    const indexPath = path.join(__dirname, '../../src/data/variants/index.json')
    const indexData = JSON.parse(fs.readFileSync(indexPath, 'utf8'))
    const toolTypes = {}

    indexData.tools.forEach(tool => {
      const config = TOOL_TYPE_CONFIG[tool.key] || TOOL_TYPE_CONFIG['ai-anime-generator'] // 默认回退
      
      toolTypes[tool.key] = {
        name: tool.name,
        description: config.description,
        category: config.contentType === 'character' ? 'Character Generation' : 
                 config.contentType === 'comic' ? 'Comic Generation' : 'Art Generation',
        baseTemplate: tool.key,
        config: config
      }
    })

    return toolTypes
  } catch (error) {
    console.warn('⚠️ 无法读取variants/index.json，使用默认配置')
    // 回退到默认配置
    const defaultTools = {}
    Object.keys(TOOL_TYPE_CONFIG).forEach(toolKey => {
      const config = TOOL_TYPE_CONFIG[toolKey]
      defaultTools[toolKey] = {
        name: config.name,
        description: config.description,
        category: config.contentType === 'character' ? 'Character Generation' : 
                 config.contentType === 'comic' ? 'Comic Generation' : 'Art Generation',
        baseTemplate: toolKey,
        config: config
      }
    })
    return defaultTools
  }
}

// 获取原始工具的基础内容
function getBaseToolContent(toolType) {
  try {
    // 尝试加载英文翻译文件作为基础内容
    const translationPath = path.join(
      __dirname,
      `../../src/i18n/locales/en/${toolType}.json`,
    )
    if (fs.existsSync(translationPath)) {
      const translation = JSON.parse(fs.readFileSync(translationPath, 'utf8'))

      // 根据工具类型使用不同的内容结构
      let baseContent

      if (toolType === 'oc-maker') {
        // OC Maker 特定的内容结构
        baseContent = {
          // 基础信息
          toolName:
            translation.title || translation.header?.title || 'OC Maker',
          intro: translation.whatIs?.description || '',

          // 主要内容区域
          header: {
            title: translation.header?.title || 'OC Maker',
            subtitle: translation.header?.subtitle || '',
          },

          // 各个section - OC Maker 结构
          sections: {
            whatIs: {
              title: translation.whatIs?.title || 'What is OC Maker',
              description: translation.whatIs?.description || '',
            },
            howToUse: {
              title:
                translation.howToUse?.title ||
                translation.howItWorks?.title ||
                'How to Use OC Maker',
              subtitle:
                translation.howToUse?.description ||
                translation.howItWorks?.description ||
                '',
              steps: translation.howItWorks?.steps
                ? Object.values(translation.howItWorks.steps)
                : [],
            },
            whyUse: {
              title: translation.features?.title || 'Why Use OC Maker',
              subtitle: '',
              features: translation.features?.features
                ? Object.values(translation.features.features)
                : [],
            },
          },

          // 样式和应用 - OC Maker 没有这些，使用空数组
          styles: [],
          applications: [],

          // 提示和FAQ
          tips: [],
          faq: translation.faq
            ? Object.keys(translation.faq)
                .filter(key => key.startsWith('question'))
                .map((questionKey, index) => {
                  const answerKey = `answer${questionKey.replace('question', '')}`
                  return {
                    question: translation.faq[questionKey],
                    answer: translation.faq[answerKey],
                  }
                })
            : [],

          // SEO信息
          seo: {
            title: translation.title || '',
            description: translation.meta?.description || '',
            keywords: translation.meta?.keywords || '',
          },
        }
      } else if (toolType === 'ai-comic-generator') {
        // AI Comic Generator 特定的内容结构
        baseContent = {
          // 基础信息
          toolName:
            translation.title ||
            translation.header?.title ||
            'AI Comic Generator',
          intro: translation.whatIs?.description || '',

          // 主要内容区域
          header: {
            title: translation.header?.title || 'AI Comic Generator',
            subtitle: translation.header?.subtitle || '',
          },

          // 各个section - AI Comic Generator 结构
          sections: {
            whatIs: {
              title: translation.whatIs?.title || 'What is AI Comic Generator',
              description: translation.whatIs?.description || '',
            },
            howToUse: {
              title:
                translation.howToUse?.title || 'How to Create Comics with AI',
              subtitle: translation.howToUse?.subtitle || '',
              steps: translation.howToUse?.steps
                ? Object.values(translation.howToUse.steps)
                : [],
            },
            whyUse: {
              title: translation.whyUse?.title || 'Why Use AI Comic Generator',
              subtitle: translation.whyUse?.subtitle || '',
              features: translation.whyUse?.features
                ? Object.values(translation.whyUse.features)
                : [],
            },
            styles: {
              title: translation.styles?.title || 'Comic Styles Available',
              items: translation.styles?.items || [],
            },
            applications: {
              title:
                translation.applications?.title || 'Comic Generator Use Cases',
              items: translation.applications?.items || [],
            },
          },

          // 样式和应用 - AI Comic Generator 有这些
          styles: translation.styles?.items || [],
          applications: translation.applications?.items || [],

          // 提示和FAQ
          tips: [],
          faq: translation.faq
            ? Object.keys(translation.faq)
                .filter(key => key.startsWith('question'))
                .map((questionKey, index) => {
                  const answerKey = `answer${questionKey.replace('question', '')}`
                  return {
                    question: translation.faq[questionKey],
                    answer: translation.faq[answerKey],
                  }
                })
            : [],

          // SEO信息
          seo: {
            title: translation.title || '',
            description: translation.meta?.description || '',
            keywords: translation.meta?.keywords || '',
          },
        }
      } else {
        // AI Anime Generator 和其他工具的原有结构
        baseContent = {
          // 基础信息
          toolName:
            translation.head?.title ||
            translation.title ||
            toolType.replace('-', ' '),
          intro: translation.intro?.description1 || '',

          // 主要内容区域
          header: {
            title:
              translation.content?.header?.title ||
              translation.header?.title ||
              translation.head?.title ||
              '',
            subtitle:
              translation.content?.header?.subtitle ||
              translation.header?.subtitle ||
              translation.head?.description ||
              '',
          },

          // 各个section
          sections: {
            whatIs: {
              title:
                translation.headings?.whatIs ||
                translation.sections?.whatIs?.title ||
                '',
              description:
                translation.content?.sections?.whatIs?.description ||
                translation.sections?.whatIs?.description ||
                '',
            },
            howToUse: {
              title:
                translation.headings?.howToUse ||
                translation.sections?.howToUse?.title ||
                '',
              subtitle:
                translation.content?.sections?.howToUse?.subtitle ||
                translation.sections?.howToUse?.subtitle ||
                '',
              steps:
                translation.content?.sections?.howToUse?.steps ||
                translation.sections?.howToUse?.steps ||
                translation.steps ||
                [],
            },
            whyUse: {
              title:
                translation.headings?.whyUse ||
                translation.sections?.whyUse?.title ||
                '',
              subtitle:
                translation.content?.sections?.whyUse?.subtitle ||
                translation.sections?.whyUse?.subtitle ||
                '',
              features:
                translation.content?.sections?.whyUse?.features ||
                translation.sections?.whyUse?.features ||
                translation.features ||
                [],
            },
            faq: {
              title:
                translation.headings?.faq ||
                translation.sections?.faq?.title ||
                translation.faqSection?.title ||
                `${keyword} FAQ`,
              description:
                translation.content?.sections?.faq?.description ||
                translation.sections?.faq?.description ||
                translation.faqSection?.description ||
                `Everything you need to know about ${keyword}`,
            },
          },

          // 样式和应用
          styles:
            translation.styles ||
            translation.content?.sections?.styles?.items ||
            [],
          applications:
            translation.applications ||
            translation.content?.sections?.applications?.items ||
            [],

          // 提示和FAQ
          tips: translation.tips?.items || translation.tips || [],
          faq: translation.faq?.items || translation.faq || [],

          // SEO信息
          seo: {
            title: translation.head?.title || translation.title || '',
            description:
              translation.head?.description ||
              translation.meta?.description ||
              '',
            keywords:
              translation.head?.keywords || translation.meta?.keywords || '',
          },
        }
      }

      console.log(`✅ 成功加载 ${toolType} 的基础内容`)
      return baseContent
    }
  } catch (error) {
    console.warn(`⚠️ 无法加载${toolType}的基础内容:`, error.message)
  }

  // 返回空对象作为fallback
  return {
    toolName: toolType.replace('-', ' '),
    intro: '',
    header: { title: '', subtitle: '' },
    sections: { whatIs: {}, howToUse: {}, whyUse: {} },
    styles: [],
    applications: [],
    tips: [],
    faq: [],
    seo: { title: '', description: '', keywords: '' },
  }
}

// 生成随机页面结构，避免内容牧场（基于keyword生成一致的随机种子）
function generateRandomPageStructure(keyword, toolInfo) {
  // 使用关键词生成一致的随机种子
  const seed = keyword.split('').reduce((a, b) => {
    a = (a << 5) - a + b.charCodeAt(0)
    return a & a
  }, 0)

  // 基于种子的伪随机函数
  let randomSeed = Math.abs(seed)
  const seededRandom = () => {
    randomSeed = (randomSeed * 9301 + 49297) % 233280
    return randomSeed / 233280
  }

  // 定义所有可用的版块
  const allSections = [
    'whatIs',
    'howToUse',
    'whyUse',
    'examples',
    'applications',
    'tips',
  ]

  // Examples必须在前两个位置（基于种子决定）
  const examplesPosition = seededRandom() < 0.5 ? 0 : 1 // 0 或 1

  // 移除examples，准备其他版块
  const otherSections = allSections.filter(section => section !== 'examples')

  // 使用种子随机打乱其他版块
  const shuffledSections = [...otherSections].sort(() => seededRandom() - 0.5)

  // 构建最终结构
  const structure = []

  // 插入Examples到指定位置
  if (toolInfo.baseTemplate !== 'ai-comic-generator') {
    if (examplesPosition === 0) {
      structure.push('examples')
      structure.push(...shuffledSections)
    } else {
      structure.push(shuffledSections[0])
      structure.push('examples')
      structure.push(...shuffledSections.slice(1))
    }
  } else {
    structure.push(...shuffledSections)
  }

  // 添加FAQ和CTA到最后
  structure.push('faq')
  structure.push('cta')

  console.log(`📋 为 "${keyword}" 生成固定页面结构: ${structure.join(' → ')}`)

  // 返回结构和对应的标题 - 使用固定格式
  // 根据关键词生成工具名称
  const toolName = `${keyword.charAt(0).toUpperCase() + keyword.slice(1)} AI Generator`

  return {
    structure,
    sectionTitles: {
      whatIs: `What is ${toolName}?`,
      howToUse: `How to Use The ${toolName}`,
      whyUse: `Why Use The ${toolName}`,
      examples: `${toolName} Examples`,
      applications: `${toolName} Applications and Use Cases`,
      tips: `Pro Tips for ${toolName} Creation`,
      faq: `${toolName} FAQ`,
      cta: `Start Creating ${keyword} Art Today`,
    },
  }
}

// 生成页面内容的AI prompt
function generateContentPrompt(toolType, keyword, toolInfo) {
  const baseContent = getBaseToolContent(toolType)
  const pageStructure = generateRandomPageStructure(keyword, toolInfo)

  // 加载原始工具的英文翻译文件作为few-shot example
  let fewShotExample = null
  try {
    const examplePath = path.join(
      __dirname,
      `../../src/i18n/locales/en/${toolType}.json`,
    )
    if (fs.existsSync(examplePath)) {
      fewShotExample = JSON.parse(fs.readFileSync(examplePath, 'utf8'))
      console.log(`✅ 加载few-shot示例: ${toolType}.json`)
    }
  } catch (error) {
    console.warn(`⚠️ 无法加载few-shot示例: ${error.message}`)
  }

  // 根据工具类型生成不同的few-shot prompt
  let fewShotPrompt = ''

  if (fewShotExample) {
    if (toolType === 'oc-maker') {
      // OC Maker 特定的few-shot结构
      fewShotPrompt = `

**IMPORTANT: Few-Shot Example (Use this as a template for quality and structure)**

Here's a high-quality example of how the original "${toolInfo.name}" content is structured. Use this as a template for style, depth, and quality when creating the "${keyword}" variation:

\`\`\`json
{
  "seo": {
    "title": "${fewShotExample.title || `${toolInfo.name}`}",
    "description": "${fewShotExample.meta?.description || ''}",
    "keywords": "${fewShotExample.meta?.keywords || ''}"
  },
  "placeholderText": "${fewShotExample.characterAppearance?.placeholder || 'Describe your character...'}",
  "header": {
    "title": "${fewShotExample.header?.title || toolInfo.name}",
    "subtitle": "${fewShotExample.header?.subtitle || ''}"
  },
  "sections": {
    "whatIs": {
      "title": "${fewShotExample.whatIs?.title || 'What is OC Maker'}",
      "description": "${fewShotExample.whatIs?.description || 'Example of detailed, engaging description style to follow...'}"
    },
    "howToUse": {
      "title": "${fewShotExample.howItWorks?.title ? fewShotExample.howItWorks.title.replace('OC', keyword) : fewShotExample.howToUse?.title || 'How to Use'}",
      "subtitle": "${fewShotExample.howToUse?.description || fewShotExample.howItWorks?.description || ''}",
      "steps": ${JSON.stringify(
        fewShotExample.howItWorks?.steps
          ? Object.values(fewShotExample.howItWorks.steps).slice(0, 4)
          : [
              {
                title: 'Example Step',
                content: 'Example content style to follow',
              },
            ],
        null,
        8,
      )}
    },
    "whyUse": {
      "title": "${fewShotExample.features?.title || 'Why Use This Tool'}",
      "subtitle": "",
      "features": ${JSON.stringify(
        fewShotExample.features?.features
          ? Object.values(fewShotExample.features.features).slice(0, 4)
          : [
              {
                title: 'Example Feature',
                content: 'Example feature description style',
              },
            ],
        null,
        8,
      )}
    }
  },
  "faq": ${JSON.stringify(
    fewShotExample.faq
      ? Object.keys(fewShotExample.faq)
          .filter(key => key.startsWith('question'))
          .slice(0, 5)
          .map(questionKey => {
            const answerKey = `answer${questionKey.replace('question', '')}`
            return {
              question: fewShotExample.faq[questionKey],
              answer: fewShotExample.faq[answerKey],
            }
          })
      : [
          {
            question: 'Example question format',
            answer: 'Example answer style and depth',
          },
        ],
    null,
    4,
  )}
}
\`\`\`

**KEY QUALITY INDICATORS TO MATCH:**
- Content depth and detail level from the example above
- Professional, engaging tone and writing style focused on character creation
- Specific, actionable information for OC/character design rather than generic descriptions
- Natural keyword integration without keyword stuffing
- Comprehensive coverage of character creation topics with real value for users`
    } else if (toolType === 'ai-comic-generator') {
      // AI Comic Generator 特定的few-shot结构
      fewShotPrompt = `

**IMPORTANT: Few-Shot Example (Use this as a template for quality and structure)**

Here's a high-quality example of how the original "${toolInfo.name}" content is structured. Use this as a template for style, depth, and quality when creating the "${keyword}" variation:

\`\`\`json
{
  "seo": {
    "title": "${fewShotExample.title || `${toolInfo.name}`}",
    "description": "${fewShotExample.meta?.description || ''}",
    "keywords": "${fewShotExample.meta?.keywords || ''}"
  },
  "placeholderText": "${fewShotExample.placeholderText || 'Describe your comic story...'}",
  "header": {
    "title": "${fewShotExample.header?.title || toolInfo.name}",
    "subtitle": "${fewShotExample.header?.subtitle || ''}"
  },
  "sections": {
    "whatIs": {
      "title": "${fewShotExample.whatIs?.title || 'What is AI Comic Generator'}",
      "description": "${fewShotExample.whatIs?.description || 'Example of detailed, engaging description style to follow...'}"
    },
    "howToUse": {
      "title": "${fewShotExample.howToUse?.title || 'How to Create Comics with AI'}",
      "subtitle": "${fewShotExample.howToUse?.subtitle || ''}",
      "steps": ${JSON.stringify(
        fewShotExample.howToUse?.steps
          ? Object.values(fewShotExample.howToUse.steps).slice(0, 4)
          : [
              {
                title: 'Example Step',
                content: 'Example content style to follow',
              },
            ],
        null,
        8,
      )}
    },
    "whyUse": {
      "title": "${fewShotExample.whyUse?.title || 'Why Use AI Comic Generator'}",
      "subtitle": "${fewShotExample.whyUse?.subtitle || ''}",
      "features": ${JSON.stringify(
        fewShotExample.whyUse?.features
          ? Object.values(fewShotExample.whyUse.features).slice(0, 4)
          : [
              {
                title: 'Example Feature',
                content: 'Example feature description style',
              },
            ],
        null,
        8,
      )}
    },
    "styles": {
      "title": "${fewShotExample.styles?.title || 'Comic Styles Available'}",
      "items": ${JSON.stringify(
        fewShotExample.styles?.items?.slice(0, 4) || [
          {
            title: 'Example Style',
            content: 'Example style description',
          },
        ],
        null,
        8,
      )}
    },
    "applications": {
      "title": "${fewShotExample.applications?.title || 'Comic Generator Use Cases'}",
      "items": ${JSON.stringify(
        fewShotExample.applications?.items?.slice(0, 4) || [
          {
            title: 'Example Application',
            content: 'Example application description',
          },
        ],
        null,
        8,
      )}
    }
  },
  "faq": ${JSON.stringify(
    fewShotExample.faq
      ? Object.keys(fewShotExample.faq)
          .filter(key => key.startsWith('question'))
          .slice(0, 5)
          .map(questionKey => {
            const answerKey = `answer${questionKey.replace('question', '')}`
            return {
              question: fewShotExample.faq[questionKey],
              answer: fewShotExample.faq[answerKey],
            }
          })
      : [
          {
            question: 'Example question format',
            answer: 'Example answer style and depth',
          },
        ],
    null,
    4,
  )}
}
\`\`\`

**KEY QUALITY INDICATORS TO MATCH:**
- Content depth and detail level from the example above
- Professional, engaging tone and writing style focused on comic creation
- Specific, actionable information for comic generation rather than generic descriptions
- Natural keyword integration without keyword stuffing
- Comprehensive coverage of comic creation topics with real value for users`
    } else if (toolType === 'playground') {
      // Playground 特定的few-shot结构
      fewShotPrompt = `

**IMPORTANT: Few-Shot Example (Use this as a template for quality and structure)**

Here's a high-quality example of how the original "${toolInfo.name}" content is structured. Use this as a template for style, depth, and quality when creating the "${keyword}" variation:

\`\`\`json
{
  "seo": {
    "title": "${fewShotExample.meta?.title || fewShotExample.title || `${toolInfo.name}`}",
    "description": "${fewShotExample.meta?.description || fewShotExample.hero?.description || ''}",
    "keywords": "${fewShotExample.meta?.keywords || ''}"
  },
  "placeholderText": "Transform your photo into ${keyword} style",
  "header": {
    "title": "${fewShotExample.hero?.title || toolInfo.name}",
    "subtitle": "${fewShotExample.hero?.description || ''}"
  },
  "sections": {
    "whatIs": {
      "title": "What is ${keyword} Style Transfer?",
      "description": "Example of detailed, engaging description style to follow for style transformation..."
    },
    "howToUse": {
      "title": "${fewShotExample.howItWorks?.title || 'How to Transform Photos'}",
      "subtitle": "${fewShotExample.howItWorks?.description || ''}",
      "steps": ${JSON.stringify(
        fewShotExample.howItWorks?.steps
          ? Object.values(fewShotExample.howItWorks.steps).slice(0, 4)
          : [
              {
                title: 'Upload Your Photo',
                content: 'Example content style for photo upload step',
              },
              {
                title: 'Select Style',
                content: 'Example content style for style selection step',
              },
              {
                title: 'Generate Art',
                content: 'Example content style for generation step',
              },
              {
                title: 'Download Result',
                content: 'Example content style for download step',
              },
            ],
        null,
        8,
      )}
    },
    "whyUse": {
      "title": "${fewShotExample.benefits?.title || 'Why Use AI Style Transfer'}",
      "subtitle": "",
      "features": ${JSON.stringify(
        fewShotExample.benefits?.features
          ? Object.values(fewShotExample.benefits.features).slice(0, 6)
          : [
              {
                title: '🎨 Example Feature 1',
                content: 'Example feature description style',
              },
              {
                title: '⚡ Example Feature 2',
                content: 'Example feature description style',
              },
              {
                title: '🖼️ Example Feature 3',
                content: 'Example feature description style',
              },
              {
                title: '💫 Example Feature 4',
                content: 'Example feature description style',
              },
              {
                title: '🎯 Example Feature 5',
                content: 'Example feature description style',
              },
              {
                title: '🚀 Example Feature 6',
                content: 'Example feature description style',
              },
            ],
        null,
        8,
      )}
    }
  },
  "faq": ${JSON.stringify(
    fewShotExample.faq
      ? Object.keys(fewShotExample.faq)
          .filter(key => key.startsWith('question'))
          .slice(0, 5)
          .map(questionKey => {
            const answerKey = `answer${questionKey.replace('question', '')}`
            return {
              question: fewShotExample.faq[questionKey],
              answer: fewShotExample.faq[answerKey],
            }
          })
      : [
          {
            question: 'Example question format',
            answer: 'Example answer style and depth',
          },
        ],
    null,
    4,
  )}
}
\`\`\`

**KEY QUALITY INDICATORS TO MATCH:**
- Content depth and detail level from the example above
- Professional, engaging tone and writing style focused on style transformation
- Specific, actionable information for photo-to-style conversion rather than generic descriptions
- Natural keyword integration without keyword stuffing
- Comprehensive coverage of style transformation topics with real value for users`
    } else {
      // AI Anime Generator 和其他工具的原有结构
      fewShotPrompt = `

**IMPORTANT: Few-Shot Example (Use this as a template for quality and structure)**

Here's a high-quality example of how the original "${toolInfo.name}" content is structured. Use this as a template for style, depth, and quality when creating the "${keyword}" variation:

\`\`\`json
{
  "seo": {
    "title": "${fewShotExample.title || fewShotExample.meta?.title || `${toolInfo.name}`}",
    "description": "${fewShotExample.meta?.description || fewShotExample.header?.subtitle || ''}",
    "keywords": "${fewShotExample.meta?.keywords || ''}"
  },
  "intro": {
    "description1": "${fewShotExample.intro?.description1 || ''}"
  },
  "header": {
    "title": "${fewShotExample.header?.title || toolInfo.name}",
    "subtitle": "${fewShotExample.header?.subtitle || ''}"
  },
  "sections": {
    "whatIs": {
      "title": "${fewShotExample.headings?.whatIs || ''}",
      "description": "Example of detailed, engaging description style to follow..."
    },
    "howToUse": {
      "title": "${fewShotExample.headings?.howToUse || ''}",
      "subtitle": "${fewShotExample.howToUse?.subtitle || ''}",
      "steps": ${JSON.stringify(
        fewShotExample.howToUse?.steps?.slice(0, 4) || [
          { title: 'Example Step', content: 'Example content style to follow' },
        ],
        null,
        8,
      )}
    },
    "whyUse": {
      "title": "${fewShotExample.headings?.whyUse || ''}",
      "subtitle": "${fewShotExample.whyUse?.subtitle || ''}",
      "features": ${JSON.stringify(
        fewShotExample.whyUse?.features?.slice(0, 4) || [
          {
            title: 'Example Feature',
            content: 'Example feature description style',
          },
        ],
        null,
        8,
      )}
    },
    "applications": {
      "title": "${fewShotExample.headings?.applications || 'Applications'}",
      "items": ${JSON.stringify(
        fewShotExample.applications?.slice(0, 4) || [
          {
            title: 'Example Application',
            content: 'Example application description',
          },
        ],
        null,
        8,
      )}
    },
    "tips": {
      "title": "${fewShotExample.headings?.tips || fewShotExample.tips?.title || 'Tips'}",
      "items": ${JSON.stringify(
        fewShotExample.tips?.items?.slice(0, 3) ||
          fewShotExample.tips?.slice(0, 3) || ['Example tip format and style'],
        null,
        8,
      )}
    }
  },
  "faq": ${JSON.stringify(
    fewShotExample.faq?.items?.slice(0, 5) ||
      fewShotExample.faq?.slice(0, 5) || [
        {
          question: 'Example question format',
          answer: 'Example answer style and depth',
        },
      ],
    null,
    4,
  )},
  "closing": {
    "description": "${fewShotExample.closing?.description || ''}",
    "buttonText": "${fewShotExample.closing?.buttonText || ''}"
  }
}
\`\`\`

**KEY QUALITY INDICATORS TO MATCH:**
- Content depth and detail level from the example above
- Professional, engaging tone and writing style
- Specific, actionable information rather than generic descriptions
- Natural keyword integration without keyword stuffing
- Comprehensive coverage of topics with real value for users`
    }
  }

  // 根据工具类型生成不同的主要prompt
  const toolConfig = getToolTypeConfig(toolType)
  const toolDescription = toolConfig.description
  const targetAudience = toolConfig.targetAudience

  return `
Act as an SEO content strategist and expert copywriter. Your task is to generate the full JSON content for a new webpage on our platform, Komiko.

The new page is a specialized tool page for "${keyword} ${
    toolInfo.name
  }". It's a variation of our main "${toolInfo.name}" tool.

Here is the information you need:

**1. Page Goal:**
- To attract users searching for "${keyword}" related ${toolDescription}.
- To rank for the primary keyword: "${keyword} ${toolInfo.name}".
- To convince users to try our AI tool.

**2. Target Audience:**
- ${targetAudience}.
- They are looking for a powerful, fast, and easy-to-use AI ${toolConfig.contentType === 'character' ? 'character creator' : toolConfig.contentType === 'comic' ? 'comic generator' : 'art generator'}.

**3. Functional Description Template:**
Use this functional description as a guide to ensure accurate content generation that reflects actual functionality:
"${toolConfig.functionalDescription.replace('{keyword}', keyword)}"

This template should inform your content creation to ensure it accurately describes what the tool actually does, rather than relying on AI imagination.

${fewShotPrompt}

**4. Content Requirements:**
- **CRITICAL**: Match the quality, depth, and style shown in the few-shot example above
- **CRITICAL**: Adapt the example content specifically for "${keyword}" while maintaining the same level of detail and professionalism
- **CRITICAL**: Use the functional description template above to ensure content accurately reflects actual tool functionality
- **CRITICAL**: For SEO title, use ONLY the keyword "${keyword}" - do not add any suffix or additional text
- **CRITICAL**: For subtitle, focus ONLY on "${keyword}" capabilities - do NOT mention "${toolInfo.name}" or any other tool names
- **CRITICAL**: For whyUse features titles, start each title with a relevant emoji that matches the feature's purpose
- Use the exact same JSON structure and field names as shown in the example
- Ensure all content is specifically tailored to "${keyword}" rather than generic
- Include natural keyword integration throughout all sections
- Provide actionable, valuable information that users will find helpful

**5. JSON Output Structure:**

Please generate a JSON object with the following structure, using the few-shot example as your quality benchmark:

{
  "seo": {
    "title": "${keyword}",
    "description": "Craft a meta description (150-160 characters) for '${keyword}'. It must be a clear, compelling summary reflecting user search intent. Start by stating what the tool does, highlight a benefit, and end with a strong call-to-action. Integrate the primary keyword naturally. ${toolConfig.seoStandard.replace('{keyword}', keyword)}",
    "keywords": "${toolConfig.keywordFocus}"
  },
  "placeholderText": "${toolConfig.placeholderTemplate.replace('{keyword}', keyword)}",
  "pageStructure": [${pageStructure.structure.map(s => `"${s}"`).join(', ')}],
  "intro": {
    "description1": "Write a compelling introduction (1-2 paragraphs) matching the style and depth of the few-shot example. Hook the reader, introduce the tool, and include the primary keyword naturally."
  },
  "content": {
    "header": {
      "title": "${keyword}",
      "subtitle": "Create a highly unique subtitle (5-15 words) for '${keyword}'.  First, identify the core essence of '${keyword}' (e.g., for Pop Art: 'vibrant colors, iconic style'; for Cyberpunk: 'neon-drenched futures'). Then, craft a compelling subtitle that weaves these specific themes with the power of 'AI' to highlight a key user benefit. The goal is maximum creativity and style, minimum generic phrasing."
    },
    "sections": {
      "whatIs": {
        "title": "${pageStructure.sectionTitles.whatIs}",
        "description": "In 2-3 detailed paragraphs, explain what this tool is and what makes it special for creating '${keyword}' art. Match the depth and style of the few-shot example."
      },
      "howToUse": {
        "title": "${pageStructure.sectionTitles.howToUse}",
        "subtitle": "A short, encouraging subtitle for the steps. Follow the example style.",
        "steps": [
          ${toolConfig.stepTemplates.step1 ? `{"title": "Step 1: ${toolConfig.stepTemplates.step1.title}", "content": "${toolConfig.stepTemplates.step1.content.replace('{keyword}', keyword)}"},` : ''}
          ${toolConfig.stepTemplates.step2 ? `{"title": "Step 2: ${toolConfig.stepTemplates.step2.title}", "content": "${toolConfig.stepTemplates.step2.content.replace('{keyword}', keyword)}"},` : ''}
          ${toolConfig.stepTemplates.step3 ? `{"title": "Step 3: ${toolConfig.stepTemplates.step3.title}", "content": "${toolConfig.stepTemplates.step3.content.replace('{keyword}', keyword)}"},` : ''}
          ${toolConfig.stepTemplates.step4 ? `{"title": "Step 4: ${toolConfig.stepTemplates.step4.title}", "content": "${toolConfig.stepTemplates.step4.content.replace('{keyword}', keyword)}"}` : ''}
        ]
      },
      "whyUse": {
        "title": "${pageStructure.sectionTitles.whyUse}",
        "subtitle": "A short subtitle explaining the benefits. Match example style.",
        "features": [
          {"title": "Feature 1 Title (specific to ${keyword}) - START WITH A RELEVANT EMOJI", "content": "Feature 1 Description - make it specific and valuable like the example"},
          {"title": "Feature 2 Title (specific to ${keyword}) - START WITH A RELEVANT EMOJI", "content": "Feature 2 Description - match the depth of the example"},
          {"title": "Feature 3 Title (specific to ${keyword}) - START WITH A RELEVANT EMOJI", "content": "Feature 3 Description - follow example quality"},
          {"title": "Feature 4 Title (specific to ${keyword}) - START WITH A RELEVANT EMOJI", "content": "Feature 4 Description - maintain example standards"},
          {"title": "Feature 5 Title (specific to ${keyword}) - START WITH A RELEVANT EMOJI", "content": "Feature 5 Description - maintain example standards"},
          {"title": "Feature 6 Title (specific to ${keyword}) - START WITH A RELEVANT EMOJI", "content": "Feature 6 Description - maintain example standards"}
        ]
      },
      "examples": {
        "title": "${pageStructure.sectionTitles.examples}",
        "description": "A short, exciting paragraph to introduce the gallery of examples. Mention that these were all created with the AI tool. Follow the example tone."
      },
      "applications": {
        "title": "${pageStructure.sectionTitles.applications}",
        "items": [
          {"title": "Use Case 1 Title (specific to ${keyword})", "content": "Use Case 1 Description - match example depth"},
          {"title": "Use Case 2 Title (specific to ${keyword})", "content": "Use Case 2 Description - follow example style"},
          {"title": "Use Case 3 Title (specific to ${keyword})", "content": "Use Case 3 Description - maintain example quality"},
          {"title": "Use Case 4 Title (specific to ${keyword})", "content": "Use Case 4 Description - match example standards"}
        ]
      },
      "tips": {
        "title": "${pageStructure.sectionTitles.tips}",
        "items": [
          "${toolConfig.tips.tip1.replace('{keyword}', keyword)}",
          "${toolConfig.tips.tip2.replace('{keyword}', keyword)}",
          "${toolConfig.tips.tip3.replace('{keyword}', keyword)}"
        ]
      }
    },
    "faq": [
      {"question": "${toolConfig.faq.q1.replace('{keyword}', keyword)}", "answer": "${toolConfig.faq.q1.replace('{keyword}', keyword).includes('answer') ? toolConfig.faq.q1.replace('{keyword}', keyword) : 'Comprehensive answer matching the depth and style of the few-shot example FAQ'}"},
      {"question": "${toolConfig.faq.q2.replace('{keyword}', keyword)}", "answer": "Detailed answer with specific tips, following example quality"},
      {"question": "${toolConfig.faq.q3.replace('{keyword}', keyword).replace('{toolName}', toolInfo.name)}", "answer": "Answer about pricing and features, match example style"},
      {"question": "${toolConfig.faq.q4.replace('{keyword}', keyword)}", "answer": "Detailed comparison highlighting advantages, follow example depth"},
      {"question": "${toolConfig.faq.q5.replace('{keyword}', keyword)}", "answer": "Usage rights and applications, match example comprehensiveness"}
    ],
    "cta": {
      "title": "${pageStructure.sectionTitles.cta}",
      "description": "A compelling call-to-action to get the user to try the tool now. Follow the example tone and urgency.",
      "buttonText": "${toolConfig.buttonTemplate.replace('{keyword}', keyword)}"
    }
  },
  "closing": {
    "description": "A final, encouraging paragraph to summarize the tool's value and invite users to start creating. Match the style and enthusiasm of the few-shot example.",
    "buttonText": "${toolConfig.contentType === 'character' ? `Generate Your ${keyword} Character` : toolConfig.contentType === 'comic' ? `Generate Your ${keyword} Comic` : `Generate Your ${keyword} Masterpiece`}"
  }
}

**6. Original Tool Content (Additional Reference):**
${JSON.stringify(baseContent, null, 2)}

**FINAL REMINDER**:
- Use the few-shot example as your primary quality benchmark. Your output should match its depth, professionalism, and user value while being specifically tailored to "${keyword}".
- Use the functional description template to ensure content accurately reflects actual tool functionality rather than AI speculation.
- **IMPORTANT**: For the SEO title field, output EXACTLY "${keyword}" - no additional text, suffixes, or modifications.
- **IMPORTANT**: For the subtitle field, focus only on "${keyword}" - do NOT include "${toolInfo.name}" or any tool references.

Generate the content now:`
}

// 专门用于SEO-only模式的内容生成
async function generateSeoOnlyContent(toolType, keyword, config) {
  const TOOL_TYPES = getSupportedToolTypes()
  const toolInfo = TOOL_TYPES[toolType]
  if (!toolInfo) {
    throw new Error(`不支持的工具类型: ${toolType}`)
  }

  // 加载few-shot example
  let fewShotExample = null
  try {
    const examplePath = path.join(
      __dirname,
      `../../src/i18n/locales/en/${toolType}.json`,
    )
    if (fs.existsSync(examplePath)) {
      fewShotExample = JSON.parse(fs.readFileSync(examplePath, 'utf8'))
    }
  } catch (error) {
    console.warn(`⚠️ 无法加载few-shot示例: ${error.message}`)
  }

  const toolConfig = getToolTypeConfig(toolType)

  const seoOnlyPrompt = `
You are an SEO expert specializing in creating optimized meta content. Your task is to generate ONLY SEO metadata for a "${keyword}" page.

${
  fewShotExample
    ? `
**Few-Shot SEO Example:**
Reference this high-quality SEO structure from the original tool:
- Title: "${fewShotExample.title || fewShotExample.meta?.title || ''}"
- Description: "${fewShotExample.meta?.description || fewShotExample.header?.subtitle || ''}"
- Keywords: "${fewShotExample.meta?.keywords || ''}"
`
    : ''
}

**Requirements:**
- **CRITICAL**: Title must be EXACTLY "${keyword}" - no additional text
- **CRITICAL**: Description should be SEO-optimized meta description, under 160 characters, include "${keyword}" naturally with a call-to-action, focus on search engine ranking
- **CRITICAL**: Keywords should include "${keyword}" and related terms, 10-15 keywords total
- **CRITICAL**: Subtitle should be user-facing, engaging copy that explains the value proposition clearly without mentioning tool names

**Target Keywords:** "${keyword}"
**Tool Context:** This is for a ${toolInfo.name} specialized for "${keyword}" ${toolConfig.description}.

**Functional Description Template:**
Use this to understand what the tool actually does:
"${toolConfig.functionalDescription.replace('{keyword}', keyword)}"

**Output ONLY a JSON object with SEO data and subtitle:**
{
  "title": "${keyword}",
  "description": "Craft a meta description (150-160 characters) for '${keyword}'. It must be a clear, compelling summary reflecting user search intent. Start by stating what the tool does, highlight a benefit, and end with a strong call-to-action. Integrate the primary keyword naturally. ${toolConfig.seoStandard.replace('{keyword}', keyword)}",
  "keywords": "${toolConfig.keywordFocus}",
  "subtitle": "Create a highly unique subtitle (5-15 words) for '${keyword}'. **CRITICAL: Do NOT use the phrase 'Transform your ideas' or any similar variation.** First, identify the core essence of '${keyword}' (e.g., ${
    toolConfig.contentType === 'character'
      ? `for Naruto: 'ninja powers, hidden villages'; for Pokemon: 'creature companions, adventures'`
      : toolConfig.contentType === 'comic'
        ? `for Manga: 'Japanese storytelling, dynamic panels'; for Horror: 'spine-chilling narratives, dark atmosphere'`
        : `for Pop Art: 'vibrant colors, iconic style'; for Cyberpunk: 'neon-drenched futures'`
  }). Then, craft a compelling subtitle that weaves these specific themes with the power of 'AI' to highlight a key user benefit. The goal is maximum creativity and style, minimum generic phrasing."
}

Generate the SEO content now:`

  console.log('🤖 正在生成SEO内容...')

  try {
    const response = await fetch(`${config.apiBaseUrl}/api/generateText`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Cookie: `next-auth.session-token=${config.sessionToken}`,
      },
      body: JSON.stringify({
        prompt: seoOnlyPrompt,
        noNeedLogin: true,
      }),
    })

    if (!response.ok) {
      throw new Error(`SEO内容生成失败: ${response.status}`)
    }

    const seoResponse = await response.json()

    // 解析AI返回的JSON
    const jsonMatch = seoResponse.match(/\{[\s\S]*?\}/)
    if (!jsonMatch) {
      throw new Error('AI返回格式不正确，未找到SEO JSON')
    }

    const seoContent = JSON.parse(jsonMatch[0])

    // 验证SEO内容
    if (
      !seoContent.title ||
      !seoContent.description ||
      !seoContent.keywords ||
      !seoContent.subtitle
    ) {
      throw new Error(
        'SEO内容缺少必需字段（title, description, keywords, subtitle）',
      )
    }

    console.log('✅ SEO内容生成完成')
    return seoContent
  } catch (error) {
    console.error('❌ SEO内容生成失败:', error.message)
    throw error
  }
}

// AI自我反思和矫正功能
async function performSelfCriticism(content, keyword, toolInfo, config) {
  const criticismPrompt = `
You are a quality assurance expert reviewing AI-generated content for a "${keyword}" page. Your task is to perform self-criticism and provide improvements.

**Original Content to Review:**
\`\`\`json
${JSON.stringify(content, null, 2)}
\`\`\`

**Review Criteria:**
1. **Title Issues**: SEO title should be EXACTLY "${keyword}" - no additional text
2. **Description Issues**: Is the meta description 150-160 characters? Does it clearly summarize the page, highlight a key benefit, and include a strong call-to-action? It must be compelling and align with user search intent for "${keyword}".
3. **Subtitle Issues**: Is the subtitle creative and unique? Does it completely avoid the 'Transform ideas' formula? Does it weave the core themes of "${keyword}" with the mention of 'AI' to create a compelling, non-generic message?
4. **Content Quality**: Check for generic content that could be improved with ${keyword}-specific details
5. **Keywords**: Focus on AI-powered search intent. Include 'AI' in 2-3 key terms. Use genuine search terms like 'AI anime generator', 'AI character creator'. Prioritize real search queries over keyword stuffing (5-8 keywords max).
6. **Consistency**: All content should be consistently focused on ${keyword}
7. **Value Proposition**: Each section should provide specific value for ${keyword} users
8. **Using Natural Language**: Use natural language and avoid keyword stuffing

**Self-Criticism Instructions:**
1. First, identify specific issues in the current content
2. Then provide the corrected JSON with improvements
3. Focus on making content more specific to "${keyword}" and removing generic language

**Output Format:**
First provide your criticism analysis, then output the improved JSON:

CRITICISM:
- Issue 1: [describe specific problem]
- Issue 2: [describe specific problem]
- etc.

IMPROVED CONTENT:
\`\`\`json
{corrected json here}
\`\`\`

Begin your review:`

  try {
    const response = await fetch(`${config.apiBaseUrl}/api/generateText`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Cookie: `next-auth.session-token=${config.sessionToken}`,
      },
      body: JSON.stringify({
        prompt: criticismPrompt,
        noNeedLogin: true,
      }),
    })

    if (!response.ok) {
      console.warn('⚠️ 自我反思步骤失败，使用原始内容')
      return content
    }

    const criticismResponse = await response.json()

    // 提取改进后的JSON
    const improvedJsonMatch = criticismResponse.match(
      /```json\s*(\{[\s\S]*?\})\s*```/,
    )
    if (improvedJsonMatch) {
      try {
        const improvedContent = JSON.parse(improvedJsonMatch[1])
        console.log('✅ AI自我反思完成，内容已优化')

        // 保留原始的pageStructure如果新版本没有
        if (!improvedContent.pageStructure && content.pageStructure) {
          improvedContent.pageStructure = content.pageStructure
        }

        return improvedContent
      } catch (parseError) {
        console.warn('⚠️ 无法解析改进后的内容，使用原始内容')
        return content
      }
    } else {
      console.warn('⚠️ 未找到改进后的JSON，使用原始内容')
      return content
    }
  } catch (error) {
    console.warn('⚠️ 自我反思过程出错，使用原始内容:', error.message)
    return content
  }
}

// 调用AI生成页面内容
async function generatePageContent(toolType, keyword, config) {
  const TOOL_TYPES = getSupportedToolTypes()
  const toolInfo = TOOL_TYPES[toolType]
  if (!toolInfo) {
    throw new Error(`不支持的工具类型: ${toolType}`)
  }

  const prompt = generateContentPrompt(toolType, keyword, toolInfo)

  console.log('🤖 正在生成页面内容...')

  let response
  try {
    response = await fetch(`${config.apiBaseUrl}/api/generateText`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Cookie: `next-auth.session-token=${config.sessionToken}`,
      },
      body: JSON.stringify({
        prompt: prompt,
        noNeedLogin: true,
      }),
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('API错误响应:', errorText)
      throw new Error(`内容生成失败: ${response.status} - ${errorText}`)
    }
  } catch (error) {
    if (error.code === 'ENOTFOUND' || error.message.includes('fetch failed')) {
      throw new Error(
        '网络连接失败，请检查网络连接或稍后重试。如果问题持续，请检查sessionToken是否正确设置。',
      )
    }
    throw error
  }

  const aiResponse = await response.json()

  // aiResponse 是直接的文本内容
  if (!aiResponse) {
    throw new Error('AI未返回内容')
  }

  // 解析AI返回的JSON
  const jsonMatch = aiResponse.match(/\{[\s\S]*\}/)
  if (!jsonMatch) {
    throw new Error('AI返回格式不正确，未找到JSON')
  }

  const content = JSON.parse(jsonMatch[0])

  // 确保pageStructure存在
  if (!content.pageStructure) {
    console.warn('⚠️ AI未返回pageStructure，使用默认结构')
    content.pageStructure = [
      'examples',
      'whatIs',
      'howToUse',
      'whyUse',
      'applications',
      'tips',
      'faq',
      'cta',
    ]
  }

  console.log('✅ 页面内容生成完成')
  console.log(`📋 使用页面结构: ${content.pageStructure.join(' → ')}`)

  // 添加自我反思和矫正步骤
  console.log('🔍 开始AI自我反思和矫正...')
  const improvedContent = await performSelfCriticism(
    content,
    keyword,
    toolInfo,
    config,
  )

  return improvedContent
}

// 生成图片prompts
async function generateImagePrompts(
  toolType,
  keyword,
  pageContent,
  config,
  count = 6,
) {
  try {
    // 动态加载AI prompt生成器
    const aiGeneratorPath = path.join(
      __dirname,
      'prompts',
      'universal-ai-generator.js',
    )

    const { default: aiGenerator } = await import(
      `file://${aiGeneratorPath}?t=${Date.now()}`
    )

    const configWithTool = { ...config, currentTool: toolType }

    const prompts = await aiGenerator.generatePrompts(
      keyword,
      { content: pageContent },
      count,
      configWithTool,
      toolType,
    )

    const finalPrompts = aiGenerator.validatePrompts
      ? aiGenerator.validatePrompts(prompts)
      : prompts

    return finalPrompts
  } catch (error) {
    console.warn('⚠️ [Prompt Generator] 生成失败，使用基础prompts作为fallback')
    console.error('🔍 详细错误信息:', error.message)
    console.error('🔍 错误堆栈:', error.stack)

    // 回退到基础prompts
    const title = pageContent.content?.header?.title || keyword

    const fallbackPrompts = [
      `${title} example showcase, high quality anime art, professional demonstration`,
      `${title} character design, detailed illustration, masterpiece quality`,
      `${title} style artwork, vibrant colors, clean composition`,
      `${title} demonstration, before and after comparison, technical excellence`,
      `${title} gallery display, multiple variations, portfolio quality`,
      `${title} professional example, industry standard, high resolution art`,
    ]

    return fallbackPrompts
  }
}

// 判断是否为环境/场景类关键词
function isEnvironmentKeyword(keyword) {
  const envKeywords = [
    'world',
    'map',
    'landscape',
    'village',
    'city',
    'castle',
    'mansion',
    'skybox',
    'island',
    'environment',
    'scenery',
    'background',
  ]
  const keywordLower = keyword.toLowerCase()
  return envKeywords.some(env => keywordLower.includes(env))
}

// 判断是否为PFP/头像类关键词
function isPfpKeyword(keyword) {
  const pfpKeywords = [
    'pfp',
    'avatar',
    'profile picture',
    'headshot',
    'portrait',
  ]
  const keywordLower = keyword.toLowerCase()
  return pfpKeywords.some(pfp => keywordLower.includes(pfp))
}

// 为playground生成图片（使用photo-to-anime API）
async function generatePlaygroundImages(keyword, config) {
  console.log(`🎨 为playground生成${keyword}风格的图片...`)

  // 定义四个输入图片（与playground页面相同）
  const inputImages = [
    {
      input: '/images/examples/photo-to-anime/input2.jpg',
    },
    {
      input: '/images/examples/photo-to-anime/black_guy_photo.webp',
    },
    {
      input: '/images/examples/photo-to-anime/cat_photo.webp',
    },
    {
      input: '/images/examples/photo-to-anime/dog_photo.webp',
    }
  ]

  // 根据keyword映射到对应的AnimeStyle枚举值（使用实际的枚举值）
  const styleMapping = {
    'photo-to-minecraft': 'pixel_art',
    'emote-maker': 'chibi',
    'ai-emoji-generator': 'chibi',
    'ai-emoticon-generator': 'chibi',
    'anime-sticker-maker': 'chibi',
    'ai-sticker-generator': 'chibi',
    'ai-sprite-sheet-generator': 'sprite_sheet',
    'ai-sprite-generator': 'sprite_sheet',
    'ai-character-sheet-generator': 'character_sheet',
    'studio-ghibli-filter': 'ghibli_anime',
    'ai-action-figure-generator': 'action_figure',
    'ai-plush-generator': 'plushie',
    'ai-badge-generator': 'badge',
    'ai-clay-filter': 'claymation',
    'photo-to-pixel-art': 'pixel_art',
    'lego-ai-filter': 'lego',
    'photo-to-line-art': 'line_art',
    'photo-to-simpsons': 'simpsons',
    'naruto-ai-filter': 'naruto',
    'watercolor-ai-filter': 'watercolor',
    'cyberpunk-filter': 'cyberpunk',
    'ai-cosplay-generator': 'cosplay',
    'studio-ghibli-filter': 'ghibli_anime'
  }

  // 定义可用的随机风格（排除VIP风格，使用基础风格）
  const randomStyles = [
    'anime',
    'korean_manhwa',
    'ghibli_anime',
    'manga',
    'cartoon',
    'watercolor',
    'line_art',
    'sticker',
    'action_figure',
    'pixel_art',
    'lego',
    'cyberpunk'
  ]

  const keywordSlug = keywordToSlug(keyword)
  let selectedStyle = styleMapping[keywordSlug]
  let selectedStyles = []

  // 如果没有设定风格，随机选择4种不同的风格
  if (!selectedStyle) {
    // 随机打乱数组并取前4个
    const shuffledStyles = [...randomStyles].sort(() => Math.random() - 0.5)
    selectedStyles = shuffledStyles.slice(0, 4)
    console.log(`🎲 没有预设风格，随机选择4种风格: ${selectedStyles.join(', ')}`)
  } else {
    // 如果有预设风格，4张图片都使用同一种风格
    selectedStyles = [selectedStyle, selectedStyle, selectedStyle, selectedStyle]
    console.log(`🎨 使用预设风格: ${selectedStyle}`)
  }

  // 创建输出目录
  const outputDir = path.join(
    __dirname,
    '../../public/images/examples/playground',
    keywordSlug,
  )
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true })
  }

  const results = []
  let totalCost = 0

  // 为每个输入图片生成对应风格的输出
  for (let i = 0; i < inputImages.length; i++) {
    const inputImage = inputImages[i]
    const inputPath = path.join(__dirname, '../../public', inputImage.input)
    const currentStyle = selectedStyles[i] // 使用对应的风格

    try {
      console.log(`🎨 处理图片 ${i + 1}/${inputImages.length}: ${inputImage.input} (风格: ${currentStyle})`)

      // 跳过API调用模式 - 直接跳过，不生成任何图片
      if (config.skipApiCall) {
        console.log(`⏭️ 跳过API调用，不生成图片`)
        continue
      }

      // 读取输入图片并转换为base64
      const imageBuffer = fs.readFileSync(inputPath)

      // 根据文件扩展名确定MIME类型
      const ext = path.extname(inputPath).toLowerCase()
      let mimeType = 'image/jpeg'
      if (ext === '.png') mimeType = 'image/png'
      else if (ext === '.webp') mimeType = 'image/webp'
      else if (ext === '.gif') mimeType = 'image/gif'

      const base64Image = `data:${mimeType};base64,${imageBuffer.toString('base64')}`
      console.log(`📷 图片格式: ${mimeType}, 大小: ${Math.round(imageBuffer.length / 1024)}KB`)

      // 获取图片尺寸信息
      const imageSize = await getImageSizeFromBuffer(imageBuffer)
      const MAX_WIDTH = 4096
      const MAX_HEIGHT = 4096
      
      const aspectRatio = imageSize.width / (imageSize.height || 1)
      let width = imageSize.width
      let height = imageSize.height
      
      if (width > MAX_WIDTH) {
        width = MAX_WIDTH
        height = width / aspectRatio
      }
      if (height > MAX_HEIGHT) {
        height = MAX_HEIGHT
        width = height * aspectRatio
      }

      console.log(`📐 原始尺寸: ${imageSize.width}x${imageSize.height}, 调整后: ${Math.round(width)}x${Math.round(height)}`)

      // 调用photo-to-anime API
      console.log(`🔗 API调用: ${config.apiBaseUrl}/api/tools/photo-to-anime`)
      console.log(`🎨 使用风格: ${currentStyle}`)

      // 创建一个带超时的fetch请求
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 120000) // 2分钟超时

      const response = await fetch(`${config.apiBaseUrl}/api/tools/photo-to-anime`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Cookie: `next-auth.session-token=${config.sessionToken}`,
        },
        body: JSON.stringify({
          image: base64Image,
          style: currentStyle,
          width: Math.round(width),
          height: Math.round(height),
          model: 'BASIC'
        }),
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      console.log(`📡 API响应状态: ${response.status} ${response.statusText}`)

      if (!response.ok) {
        const errorText = await response.text()
        console.warn(`⚠️ 图片${i + 1}生成失败: ${response.status} - ${errorText}`)
        console.log(`⏭️ 跳过此图片，继续处理下一张`)
        continue
      }

      const result = await response.json()
      console.log(`📦 API返回结果:`, {
        hasOutput: !!result.output,
        hasError: !!result.error,
        keys: Object.keys(result)
      })

      if (result.error) {
        console.warn(`⚠️ 图片${i + 1}生成失败: ${result.error}`)
        console.log(`⏭️ 跳过此图片，继续处理下一张`)
        continue
      }

      if (result.output) {
        // 下载图片
        const imageUrl = result.output
        const imageResponse = await fetch(imageUrl)

        if (!imageResponse.ok) {
          console.warn(
            `⚠️ 图片${i + 1}下载失败: ${imageResponse.statusText}`,
          )
          continue
        }

        const imageBuffer = await imageResponse.arrayBuffer()

        // 保存生成的图片
        const outputFileName = `example_${i + 1}.webp`
        const outputPath = path.join(outputDir, outputFileName)

        fs.writeFileSync(outputPath, Buffer.from(imageBuffer))

        results.push({
          input: inputImage.input,
          image: `/images/examples/playground/${keywordSlug}/${outputFileName}`,
          prompt: `Style: ${currentStyle}`,
        })

        totalCost += 30 // photo-to-anime的基本成本
        console.log(
          `✅ 图片${i + 1}生成成功: ${outputFileName} (${currentStyle} 风格)`,
        )
      } else {
        console.warn(`⚠️ 图片${i + 1}生成失败: 无输出`)
        console.log(`⏭️ 跳过此图片，继续处理下一张`)
      }
    } catch (error) {
      console.warn(`⚠️ 图片${i + 1}生成出错:`, error.message)

      if (error.name === 'AbortError') {
        console.warn(`⏰ 图片${i + 1}生成超时（2分钟）`)
      }
      
      console.log(`⏭️ 跳过此图片，继续处理下一张`)
    }
  }

  console.log(`✅ playground图片生成完成，共生成${results.length}张图片，总成本: ${totalCost} zaps`)
  return results
}

// 生成图片
async function generateImages(prompts, keyword, model, config, toolType) {
  // 对于playground，使用特殊的图片生成逻辑
  if (toolType === 'playground') {
    return await generatePlaygroundImages(keyword, config)
  }

  const modelConfig = MODELS[model]
  if (!modelConfig) {
    throw new Error(`不支持的模型: ${model}`)
  }

  // 根据关键词类型选择比例配置
  const isEnvironment = isEnvironmentKeyword(keyword)
  const isPfp = isPfpKeyword(keyword)

  let ratioConfig
  let ratioDescription = '竖屏'

  if (isPfp) {
    // PFP使用1:1方形比例
    ratioConfig = {
      square: IMAGE_RATIOS.square,
    }
    ratioDescription = 'PFP方形'
  } else if (isEnvironment) {
    ratioConfig = LANDSCAPE_RATIOS
    ratioDescription = '横屏'
  } else {
    ratioConfig = IMAGE_RATIOS
    ratioDescription = '竖屏'
  }

  // 根据工具配置使用特定比例
  const toolConfig = getToolTypeConfig(toolType)
  if (toolConfig.ratioOverride && !isPfp) {
    ratioConfig = {
      [toolConfig.ratioOverride]: ratioConfig[toolConfig.ratioOverride],
    }
    ratioDescription = `${toolConfig.name}专用${toolConfig.ratioOverride}`
  }

  const ratioKeys = Object.keys(ratioConfig)

  if (toolConfig.ratioOverride && !isPfp) {
    console.log(`📐 使用${toolConfig.name}专用${toolConfig.ratioOverride}比例配置`)
  } else if (isPfp) {
    console.log(`📐 使用PFP专用1:1方形比例配置`)
  } else {
    console.log(
      `📐 使用${ratioDescription}比例配置 (检测到${isEnvironment ? '环境' : '角色'}类关键词)`,
    )
  }

  // 计算总图片数量：每个prompt对应一张图片，随机比例
  const totalImages = prompts.length

  console.log(
    `🎨 开始生成 ${totalImages} 张图片 (每个prompt随机选择一种比例，共${ratioKeys.length}种比例可选)...`,
  )

  // 使用规范化的关键字作为文件夹名
  const keywordSlug = keywordToSlug(keyword)
  const outputDir = path.join(
    __dirname,
    '../../public/images/examples/' + toolType,
    keywordSlug,
  )
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true })
  }

  const results = []
  let totalCost = 0
  let imageIndex = 1

  // 为每个prompt生成一张随机比例的图片
  for (let promptIndex = 0; promptIndex < prompts.length; promptIndex++) {
    const basePrompt = prompts[promptIndex]

    // 根据工具类型调整prompt
    const toolConfig = getToolTypeConfig(toolType)
    const enhancement = toolConfig.contentType === 'character' 
      ? ', masterpiece, best quality, detailed, high resolution'
      : ', masterpiece, best quality'
    const enhancedPrompt = `${basePrompt}${enhancement}`

    // 随机选择一种比例
    const randomRatioKey =
      ratioKeys[Math.floor(Math.random() * ratioKeys.length)]
    const ratioConfigItem = ratioConfig[randomRatioKey]

    try {
      console.log(
        `🎨 生成图片 ${imageIndex}/${totalImages} - ${ratioConfigItem.name} (随机选择)...`,
      )

      // 根据OC Maker的实际API调用方式构建请求
      const requestBody = {
        prompt: enhancedPrompt,
        negative_prompt:
          'bad quality, blurry, low resolution, duplicate, extra limbs, deformed, worst quality, low score, bad score, average score, signature, watermark, username',
        size: {
          width: ratioConfigItem.width,
          height: ratioConfigItem.height,
        },
        num_images: 1,
        ip_adapter_images: [],
        init_images: undefined,
        store_supabase: false,
        tool: toolConfig.contentType === 'character' ? 'oc-maker' : 'variant-page-generator',
      }

      const response = await fetch(
        `${config.apiBaseUrl}${modelConfig.endpoint}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Cookie: `next-auth.session-token=${config.sessionToken}`,
          },
          body: JSON.stringify(requestBody),
          credentials: 'include',
        },
      )

      if (!response.ok) {
        const errorText = await response.text()
        console.error(`API错误响应: ${errorText}`)
        throw new Error(`API失败: ${response.status} - ${errorText}`)
      }

      const data = await response.json()

      // 检查API返回的错误
      if (data.error) {
        throw new Error(`API返回错误: ${data.error}`)
      }

      if (!Array.isArray(data) || !data[0]) {
        console.error('API返回数据:', data)
        throw new Error('API未返回有效的图片URL')
      }

      // 下载并保存图片
      const imageUrl = data[0]
      const imageResponse = await fetch(imageUrl)

      if (!imageResponse.ok) {
        throw new Error(`图片下载失败: ${imageResponse.status}`)
      }

      const imageBuffer = await imageResponse.arrayBuffer()

      // 使用新的命名规则：序号_尺寸名_随机数.webp
      const randomId = Math.floor(Math.random() * 9999)
      // 简化尺寸名称，支持横屏和竖屏
      const sizeNameMap = {
        'portrait-tall': 'portrait',
        portrait: 'portrait',
        square: 'square',
        landscape: 'landscape',
        'landscape-wide': 'wide',
      }
      const sizeName = sizeNameMap[randomRatioKey] || randomRatioKey
      const fileName = `${imageIndex}_${sizeName}_${randomId}.webp`
      const outputPath = path.join(outputDir, fileName)
      fs.writeFileSync(outputPath, Buffer.from(imageBuffer))

      results.push({
        image: `/images/examples/${toolType}/${keywordSlug}/${fileName}`,
        alt: `AI generated ${keyword} ${ratioConfigItem.name} style`,
        prompt: basePrompt,
        ratio: randomRatioKey, // 添加比例信息用于统计
      })

      totalCost += modelConfig.cost
      console.log(`✅ 图片 ${imageIndex} 完成 - ${ratioConfigItem.name}`)
      imageIndex++

      // 延迟避免API限制
      if (imageIndex <= totalImages) {
        await new Promise(resolve => setTimeout(resolve, 3000))
      }
    } catch (error) {
      console.error(
        `❌ 图片 ${imageIndex} 失败 (${ratioConfigItem.name}): ${error.message}`,
      )
      imageIndex++
    }
  }
  console.log(`📊 成功生成: ${results.length}/${totalImages} 张图片`)

  // 按比例分组展示结果
  console.log('\n📋 生成结果汇总 (随机比例分布):')
  const ratioStats = {}
  results.forEach(result => {
    const ratio = result.ratio
    if (!ratioStats[ratio]) {
      ratioStats[ratio] = 0
    }
    ratioStats[ratio]++
  })

  Object.keys(ratioStats).forEach(ratioKey => {
    const count = ratioStats[ratioKey]
    const ratioName = ratioConfig[ratioKey]?.name || ratioKey
    console.log(`  ${ratioName}: ${count} 张`)
  })

  // 压缩生成的图片
  if (results.length > 0) {
    try {
      console.log('\n🗜️ 正在压缩图片...')
      const { spawn } = await import('child_process')

      const compressProcess = spawn('mogrify', ['-quality', '80', '*.webp'], {
        cwd: outputDir,
        shell: true,
        stdio: 'inherit',
      })

      await new Promise((resolve, reject) => {
        compressProcess.on('close', code => {
          if (code === 0) {
            console.log('✅ 图片压缩完成')
            resolve()
          } else {
            console.warn(`⚠️ 图片压缩失败，退出码: ${code}`)
            resolve() // 不阻塞主流程
          }
        })
        compressProcess.on('error', error => {
          console.warn(`⚠️ 图片压缩命令执行失败: ${error.message}`)
          resolve() // 不阻塞主流程
        })
      })
    } catch (error) {
      console.warn(`⚠️ 图片压缩过程出错: ${error.message}`)
    }
  }

  return results
}

// 更新variant数据
function updateVariantPages(toolType, keyword, pageContent, examples) {
  console.log('🔄 正在更新variant数据...')
  console.log(`📝 pageContent存在: ${!!pageContent}`)
  console.log(`🖼️ examples数量: ${examples.length}`)

  // 如果没有pageContent，尝试加载现有数据或创建基本结构
  if (!pageContent) {
    console.log('⚠️ pageContent为空，尝试加载现有数据...')

    // 检查是否有现有的variant文件
    const existingPage = checkExistingPage(toolType, keyword)
    if (existingPage.exists && existingPage.data) {
      console.log('📄 使用现有页面数据')
      pageContent = existingPage.data
    } else {
      console.log('📝 创建基本页面结构（仅用于保存图片）')
      // 创建最基本的结构来保存图片
      pageContent = {
        seo: {},
        placeholderText: `Generate ${keyword} style artwork`,
        content: {
          header: {
            title: keyword,
            subtitle: `Generate ${keyword} style artwork with AI`,
          },
        },
        originalKeyword: keyword,
      }
    }
  }

  // 合并新旧examples，保留原有图片
  const existingExamples = pageContent.content?.examples || []
  const mergedExamples = [...existingExamples, ...examples]

  console.log(
    `📊 examples合并: 原有${existingExamples.length}张 + 新增${examples.length}张 = 总计${mergedExamples.length}张`,
  )

  // 检测是否为SEO-only模式（新增examples为0且与原有examples相同）
  const isSeoOnlyMode =
    examples.length > 0 &&
    examples.length === existingExamples.length &&
    JSON.stringify(examples) === JSON.stringify(existingExamples)

  if (isSeoOnlyMode) {
    console.log('🔍 检测到SEO-only模式，保持examples不变')
  }

  // 准备内容（包含生成的图片）
  const contentWithExamples = {
    seo: pageContent.seo || {},
    placeholderText:
      pageContent.placeholderText || `Generate ${keyword} style artwork`,
    content: {
      ...pageContent.content,
      examples: mergedExamples,
    },
    // 保存原始关键字用于显示
    originalKeyword: keyword,
  }

  console.log('📦 准备保存的内容结构:')
  console.log(
    `- seo: ${
      !!contentWithExamples.seo &&
      Object.keys(contentWithExamples.seo).length > 0
    }`,
  )
  console.log(`- placeholderText: ${!!contentWithExamples.placeholderText}`)
  console.log(`- content: ${!!contentWithExamples.content}`)
  console.log(`- content.header: ${!!contentWithExamples.content?.header}`)
  console.log(
    `- examples: ${contentWithExamples.content.examples?.length || 0}张`,
  )

  // 直接使用分离文件结构
  updateVariantFile(toolType, keyword, contentWithExamples)
}

// 更新variant文件
function updateVariantFile(toolType, keyword, contentWithExamples) {
  // 工具文件夹路径
  const toolDir = path.join(VARIANTS_DIR, toolType)

  // 确保工具目录存在
  if (!fs.existsSync(toolDir)) {
    fs.mkdirSync(toolDir, { recursive: true })
  }

  // 使用规范化的关键字作为文件名
  const keywordSlug = keywordToSlug(keyword)
  const variantFilePath = path.join(toolDir, `${keywordSlug}.json`)

  // 创建variant文件内容
  const variantData = {
    seo: contentWithExamples.seo,
    placeholderText: contentWithExamples.placeholderText,
    content: contentWithExamples.content,
    originalKeyword: contentWithExamples.originalKeyword,
  }

  // 保存variant文件
  fs.writeFileSync(variantFilePath, JSON.stringify(variantData, null, 2))
  console.log(`✅ ${toolType}/${keywordSlug}.json 已更新`)
}

// 检查页面是否已存在
function checkExistingPage(toolType, keyword) {
  const keywordSlug = keywordToSlug(keyword)
  const variantFilePath = path.join(
    __dirname,
    `../../src/data/variants/${toolType}/${keywordSlug}.json`,
  )

  if (!fs.existsSync(variantFilePath)) {
    return { exists: false }
  }

  try {
    const existingData = JSON.parse(fs.readFileSync(variantFilePath, 'utf8'))
    const hasContent =
      existingData.content && Object.keys(existingData.content).length > 0
    const hasImages =
      existingData.content?.examples && existingData.content.examples.length > 0

    return {
      exists: true,
      hasContent,
      hasImages,
      imageCount: hasImages ? existingData.content.examples.length : 0,
      data: existingData,
    }
  } catch (error) {
    console.warn(`⚠️  读取现有文件失败: ${error.message}`)
    return { exists: false }
  }
}

// 主函数 - 一键生成衍生页面
async function generateVariantPage(toolType, keyword, options = {}) {
  const startTime = Date.now()

  console.log(`\n🚀 开始生成衍生页面: ${toolType}/${keyword}`)

  // 检查页面是否已存在
  const existingPage = checkExistingPage(toolType, keyword)

  if (existingPage.exists) {
    console.log(`📄 页面已存在`)
    console.log(`📝 内容: ${existingPage.hasContent ? '✅ 已有' : '❌ 缺失'}`)
    console.log(
      `🖼️  图片: ${
        existingPage.hasImages ? `✅ ${existingPage.imageCount}张` : '❌ 缺失'
      }`,
    )

    // SEO-only模式：只重新生成SEO内容
    if (options.seoOnly) {
      if (!existingPage.hasContent) {
        throw new Error('❌ SEO-only模式需要页面已有内容，但当前页面内容缺失')
      }
      console.log('🔍 SEO-only模式：只重新生成SEO内容')
    }
    // 如果内容和图片都存在，直接跳过（除非是强制模式或只生成图片模式或SEO模式）
    else if (
      existingPage.hasContent &&
      existingPage.hasImages &&
      !options.force &&
      !options.imagesOnly
    ) {
      console.log(`✅ 页面完整，跳过生成`)
      console.log(`📄 页面: /tools/${toolType}/${keywordToSlug(keyword)}`)
      console.log(`🖼️  图片: ${existingPage.imageCount} 张`)
      return {
        skipped: true,
        reason: 'Page already exists with content and images',
        path: `/tools/${toolType}/${keywordToSlug(keyword)}`,
        imageCount: existingPage.imageCount,
      }
    }

    // 如果只缺少图片，只生成图片
    if (
      existingPage.hasContent &&
      !existingPage.hasImages &&
      !options.textOnly &&
      !options.seoOnly
    ) {
      console.log(`📝 内容已存在，只生成图片`)
      options.contentOnly = false
      options.imagesOnly = true
    }

    // 如果只缺少内容，只生成内容
    if (
      !existingPage.hasContent &&
      existingPage.hasImages &&
      !options.seoOnly
    ) {
      console.log(`🖼️  图片已存在，只生成内容`)
      options.contentOnly = true
      options.imagesOnly = false
    }
  } else if (options.seoOnly) {
    throw new Error('❌ SEO-only模式需要页面已存在，但未找到现有页面')
  }

  // 加载配置
  const config = {
    ...DEFAULT_CONFIG,
    ...JSON.parse(fs.readFileSync(CONFIG_FILE, 'utf8') || '{}'),
  }

  console.log(
    `📊 配置: 模型=${
      options.model || config.defaultModel || DEFAULT_CONFIG.defaultModel
    }, 图片数量=${
      options.count ||
      Math.ceil(config.imagesPerVariant / Object.keys(IMAGE_RATIOS).length)
    }`,
  )

  if (!config.sessionToken) {
    throw new Error(
      '需要设置 sessionToken，请先运行: node generate-variant-page.mjs setup',
    )
  }

  const model =
    options.model || config.defaultModel || DEFAULT_CONFIG.defaultModel
  const count = parseInt(options.count) || 8 // 默认10个不同的prompts，对应30张图片
  const textOnly = options.textOnly || false

  try {
    let pageContent = null

    // SEO-only模式：只重新生成SEO内容
    if (options.seoOnly) {
      console.log('🔍 SEO-only模式：重新生成SEO内容')

      // 使用现有页面内容作为基础
      pageContent = { ...existingPage.data }

      // 生成新的SEO内容（专门的SEO-only函数）
      const newSeoContent = await generateSeoOnlyContent(
        toolType,
        keyword,
        config,
      )

      // 更新SEO和subtitle，保留其他所有内容（包括examples）
      pageContent.seo = {
        title: newSeoContent.title,
        description: newSeoContent.description,
        keywords: newSeoContent.keywords,
      }

      // 更新header中的subtitle
      if (!pageContent.content) {
        pageContent.content = {}
      }
      if (!pageContent.content.header) {
        pageContent.content.header = {}
      }
      pageContent.content.header.subtitle = newSeoContent.subtitle

      console.log('✅ SEO内容和subtitle已更新')
      console.log(
        `📊 新SEO内容: 标题="${pageContent.seo.title}", 描述="${pageContent.seo.description}"`,
      )
      console.log(`📝 新subtitle: "${pageContent.content.header.subtitle}"`)

      // 保存更新后的内容，明确保留原有examples
      const originalExamples = pageContent.content?.examples || []
      updateVariantPages(toolType, keyword, pageContent, '')

      console.log(`\n🎉 SEO内容和subtitle更新完成!`)
      console.log(`📄 页面: /tools/${toolType}/${keywordToSlug(keyword)}`)
      console.log(`🖼️  保留原有图片: ${originalExamples.length} 张`)
      return {
        success: true,
        mode: 'seo-only',
        path: `/tools/${toolType}/${keywordToSlug(keyword)}`,
        seo: pageContent.seo,
        subtitle: pageContent.content.header.subtitle,
        preservedImages: originalExamples.length,
      }
    }

    // 1. 生成或使用现有页面内容
    if (options.imagesOnly && existingPage.exists && existingPage.hasContent) {
      console.log('📝 使用现有页面内容')
      pageContent = existingPage.data
    } else if (!options.imagesOnly) {
      console.log('📝 生成页面内容')
      pageContent = await generatePageContent(toolType, keyword, config)
    } else if (options.imagesOnly) {
      // images-only模式但没有现有内容，需要生成基础内容结构
      console.log('📝 images-only模式：生成基础页面内容')
      pageContent = await generatePageContent(toolType, keyword, config)
    }

    // 2. 先保存页面内容（不含图片）
    if (pageContent && !options.imagesOnly) {
      console.log('💾 先保存页面内容...')
      updateVariantPages(toolType, keyword, pageContent, [])
      console.log('✅ 页面内容已保存')
    }

    let examples = []
    let prompts = []

    // 3. 处理图片生成
    if (options.contentOnly) {
      console.log('📝 只生成内容模式，跳过图片生成')
      // 使用现有图片
      if (existingPage.exists && existingPage.hasImages) {
        examples = existingPage.data.content?.examples || []
      }
    } else if (getToolTypeConfig(toolType).skipImages) {
      console.log(`📝 ${toolType}类型，跳过图片生成`)
      // 使用现有图片如果存在
      if (existingPage.exists && existingPage.hasImages) {
        examples = existingPage.data.content?.examples || []
      }
    } else if (!textOnly && !options.imagesOnly) {
      // 正常模式：生成图片
      if (toolType === 'playground') {
        // 对于playground类型，直接生成图片，不需要AI生成prompts
        console.log('🖼️  直接生成playground图片（跳过prompt生成）')
        examples = await generateImages([], keyword, model, config, toolType)
      } else {
        console.log('🖼️  生成图片prompts')
        prompts = await generateImagePrompts(
          toolType,
          keyword,
          pageContent,
          config,
          count,
        )

        console.log('🖼️  生成图片')
        examples = await generateImages(prompts, keyword, model, config, toolType)
      }
    } else if (options.imagesOnly) {
      // 只生成图片模式
      if (toolType === 'playground') {
        // 对于playground类型，直接生成图片，不需要AI生成prompts
        console.log('🖼️  只生成playground图片模式（跳过prompt生成）')
        examples = await generateImages([], keyword, model, config, toolType)
      } else {
        console.log('🖼️  只生成图片模式')
        prompts = await generateImagePrompts(
          toolType,
          keyword,
          pageContent,
          config,
          count,
        )

        examples = await generateImages(prompts, keyword, model, config, toolType)
      }
    } else {
      console.log('📝 只生成文案模式，跳过图片生成')
      // 使用现有图片如果存在
      if (existingPage.exists && existingPage.hasImages) {
        examples = existingPage.data.content?.examples || []
      }
    }

    // 4. 更新variant数据（添加图片）
    if (examples.length > 0) {
      console.log('🔄 更新页面数据，添加图片...')
      updateVariantPages(toolType, keyword, pageContent, examples)
      console.log('✅ 图片数据已更新')
    }

    console.log(`\n🎉 衍生页面生成完成!`)
    console.log(`📄 页面: /tools/${toolType}/${keywordToSlug(keyword)}`)
    if (!textOnly) {
      console.log(`🖼️  图片: ${examples.length} 张`)
    } else {
      console.log(`📝 只生成文案，未生成图片`)
    }
  } catch (error) {
    console.error('❌ 生成失败:', error.message)
    throw error
  }
}

// 解析命令行参数
function parseArgs() {
  const args = process.argv.slice(2)

  if (args.length === 0) {
    console.log(
      '用法: node generate-variant-page.mjs <tool-type> <keyword> [options]',
    )
    console.log('选项:')
    console.log(
      '  --count=N        生成N个提示词 (默认: 10, 总图片数 = N×3种比例)',
    )
    console.log('  --model=MODEL    使用指定模型 (默认: AnimagineXL)')
    console.log('  --text-only      只生成文案，不生成图片')
    console.log('  --images-only    只生成图片，跳过内容检测')
    console.log('  --seo-only       只重新生成SEO内容，保留其他内容')
    console.log('  --force          强制重新生成，即使页面已存在')
    console.log('  setup           设置API tokens')
    process.exit(1)
  }

  if (args[0] === 'setup') {
    return { action: 'setup' }
  }

  const toolType = args[0]
  const keyword = args[1]

  if (!toolType || !keyword) {
    console.error('❌ 错误: 需要提供工具类型和关键词')
    process.exit(1)
  }

  let promptCount = 8 // 默认10个提示词，对应30张图片
  let model = null // 改为null，让config.defaultModel生效
  let textOnly = false
  let imagesOnly = false
  let seoOnly = false
  let force = false

  // 解析选项
  for (let i = 2; i < args.length; i++) {
    const arg = args[i]
    if (arg.startsWith('--count=')) {
      promptCount = parseInt(arg.split('=')[1]) || 10
    } else if (arg.startsWith('--model=')) {
      model = arg.split('=')[1] || 'Gemini'
    } else if (arg === '--text-only') {
      textOnly = true
    } else if (arg === '--images-only') {
      imagesOnly = true
    } else if (arg === '--seo-only') {
      seoOnly = true
    } else if (arg === '--force') {
      force = true
    }
  }

  return {
    action: 'generate',
    toolType,
    keyword,
    promptCount,
    model,
    textOnly,
    imagesOnly,
    seoOnly,
    force,
  }
}

// 主执行逻辑
async function main() {
  try {
    const args = parseArgs()

    if (args.action === 'setup') {
      await setupConfig()
      return
    }

    if (args.action === 'generate') {
      // generateVariantPage 函数内部会加载配置，所以这里不需要预先加载
      await generateVariantPage(args.toolType, args.keyword, {
        count: args.promptCount,
        model: args.model, // 直接传递，让 generateVariantPage 处理默认值
        textOnly: args.textOnly,
        imagesOnly: args.imagesOnly,
        seoOnly: args.seoOnly,
        force: args.force,
      })
    }
  } catch (error) {
    console.error('❌ 执行失败:', error.message)
    process.exit(1)
  }
}

// 如果直接运行此脚本，执行主函数
if (import.meta.url === `file://${process.argv[1]}`) {
  main()
}
