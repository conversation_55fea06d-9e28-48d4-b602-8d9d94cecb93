# Functional Description System

## Overview

The variant generation system now includes **functional description templates** that provide structured, accurate descriptions of what each tool actually does. This prevents AI from relying on imagination and ensures generated content accurately reflects the actual functionality.

## Problem Solved

Previously, when generating page content, the AI would only receive a title (e.g., "plush") and had to imagine what the tool does. This led to inaccurate content that didn't match the actual functionality.

Now, the AI receives both:
1. **Title**: The keyword (e.g., "plush")
2. **Functional Description**: A structured template explaining exactly what the tool does

## Functional Description Templates

Each tool type now has a `functionalDescription` template that follows this pattern:

```
AI {keyword} [Tool Type]: an AI tool that [action] [input type] to [output type]. 
First [step 1], then [step 2], then [step 3], and you will get [result].
```

### Current Templates

#### Playground (Style Transfer)
```
AI {keyword} Generator: an AI tool that converts your photos to {keyword} style.
First upload your photo or image, then select the {keyword} template,
then click Convert to {keyword}, and you will get a photo transformed into {keyword} style.
```

#### OC Maker (Character Creation)
```
AI {keyword} Character Creator: an AI tool that generates original {keyword} characters from text descriptions. 
First input your character description including appearance, personality, and traits for your {keyword} character, 
then select the {keyword} character style from various templates, then click Generate Character, 
and you will get a unique {keyword} character design.
```

#### AI Anime Generator (Art Generation)
```
AI {keyword} Generator: an AI tool that creates {keyword} style artwork from text descriptions. 
First input your text prompt describing the {keyword} scene or character you want, 
then select the {keyword} art style from various templates, then click Generate Art, 
and you will get a high-quality {keyword} style artwork.
```

#### AI Comic Generator (Comic Creation)
```
AI {keyword} Comic Generator: an AI tool that creates {keyword} style comics from story descriptions. 
First input your story plot, characters, and dialogue for your {keyword} comic, 
then select the {keyword} comic style from various panel layouts and art styles, 
then click Generate Comic, and you will get a complete {keyword} style comic with panels and speech bubbles.
```

## Example Usage

For the playground's variant keyword 'plush':

**Before**: AI only knew the title "plush" and had to guess what the tool does.

**After**: AI receives:
- **Title**: "plush"
- **Functional Description**: "AI plush Generator: an AI tool that converts your photos to plush style. First upload your photo or image, then select the plush template from a variety of different style templates, then click Convert to plush, and you will get a photo transformed into plush style."

## Implementation

The functional descriptions are:
1. **Defined** in `TOOL_TYPE_CONFIG` in `generate-variant-page.mjs`
2. **Injected** into AI prompts during content generation
3. **Used** by both full content generation and SEO-only generation modes

## Benefits

1. **Accuracy**: Content accurately reflects actual tool functionality
2. **Consistency**: All variants follow the same functional pattern
3. **Reusability**: Templates can be easily adapted for new tools
4. **Quality**: Reduces AI hallucination and improves content quality

## Fixed Section Titles

The system now uses **fixed section title formats** instead of AI-generated titles. This ensures consistency and prevents AI from creating unpredictable titles.

### Title Format Examples

For keyword "plush":
- **How to Use**: "How to Use The Plush AI Generator"
- **Examples**: "Plush AI Generator Examples"
- **Why Use**: "Why Use The Plush AI Generator"
- **FAQ**: "Plush AI Generator FAQ"

For keyword "studio ghibli":
- **How to Use**: "How to Use The Studio Ghibli AI Generator"
- **Examples**: "Studio Ghibli AI Generator Examples"
- **Why Use**: "Why Use The Studio Ghibli AI Generator"
- **FAQ**: "Studio Ghibli AI Generator FAQ"

### Benefits of Fixed Titles

1. **Consistency**: All pages follow the same title pattern
2. **Predictability**: No unexpected AI-generated variations
3. **SEO Optimization**: Consistent keyword placement in titles
4. **Brand Recognition**: Uniform naming convention across all tools

## Testing

The functional description system is automatically integrated into the content generation process. When you run:

```bash
node generate-variant-page.mjs playground [keyword] --seo-only
```

The system will use both the functional descriptions and fixed title formats.
