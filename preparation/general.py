import os
import resend
import pandas as pd

resend.api_key = "re_9Rv4YMZE_4W4ahgXHnfEnGdFzeJoA13RQ"

# Load the CSV file
# file_path = 'preparation/test_users.csv'
file_path = 'preparation/all_users.csv'
# file_path = 'preparation/cpp.csv'
data = pd.read_csv(file_path)

# Select the 'name' and 'email' columns
selected_columns = data[['name', 'email']]
for index, row in selected_columns.iterrows():
    # if index < 37:
    #     continue
    username = row['name']
    email = row['email']
    if pd.isna(username):
        continue
    params: resend.Emails.SendParams = {
        "sender": "<PERSON><PERSON><PERSON><<EMAIL>>",
        "to": [email],
        "subject": "🎥 Submit your AI short anime + win up to $3K and free AI tool subscriptions!",
        "html": f"""<!doctype html>
<html>
<head>
<meta charset='UTF-8'><meta name='viewport' content='width=device-width initial-scale=1'>
<title>Untitled</title>
</head>
<body><p>AniGen - the world&#39;s first AI animation shorts competition co-hosted by Hailuo x Hedra x Komiko x AniShort - just kicked off</p>
<p>You’re just a few steps away from submitting your animation or script. Here&#39;s what you need to know:</p>
<p><strong>🎥 Animation Track</strong></p>
<p>To qualify:</p>
<p>✅ Your video should be vertical (9:16), between 30 seconds and 3 minutes</p>
<p>✅ Use AI tools for over 50% of your process</p>
<p>✅ Submissions must tell a story or be the start of one</p>
<p>✅ Any anime/animation art style is welcome, and feel free to create for all ages, including adult audiences</p>
<p><strong>✏️ Scriptwriting Track</strong></p>
<p>To qualify:</p>
<p>✅ Submit a concept and the scripts of the first 5 short episodes (each episode ~1–2 mins long, designed for fast-paced, attention-grabbing storytelling)</p>
<p>✅ Include:</p>
<ul>
<li>A working title</li>
<li>Plot synopsis</li>
<li>Character overview</li>
<li>Scripts of first 5 episodes</li>

</ul>
<p>✅ Submissions must be original, in English or with translations</p>
<p><strong>All Animation Track submissions must</strong>
 ✅ You <em>must</em> include:</p>
<ul>
<li>Image or character created with <strong>Komiko</strong></li>
<li>At least one character talking scene using <strong>Hedra’s Character-3</strong></li>
<li>Animation generated using <strong>Hailuo</strong> (directly or through Hedra/Komiko)</li>

</ul>
<p>✅ No trailers, music videos, NSFW, or copyrighted content </p>
<p>✅ Post your work on social media (X/Instagram/YouTube/TikTok), and tag  #AniGen @hedra_labs @KomikoAI @Hailuo_AI @AniShortApp so official accounts can repost </p>
<p>✅ You must be 18 or older to qualify</p>
<p><strong>🏆 Prizes</strong></p>
<p><strong>Animation</strong> </p>
<p>🥇 Grand Prize: $3,000 + 12-month free pro subscriptions to Hedra, Komiko, and Hailu</p>
<p>🥈 2nd Prize: $1,500 + 6-month subs</p>
<p>🏅 2 Runners-Up: $500 + 3-month subs</p>
<p>✨ 30 Excellence Awards: 1-month pro subs</p>
<p><strong>Scriptwriting</strong></p>
<p>🥇 Grand Prize: $1,000</p>
<p>✨ 3 Excellence Awards: $100</p>
<p>Winning entries will be promoted across sponsor social channels, released on the AniShort streaming platform, and might even have the chance to further develop their animation with funding or studio support.</p>
<p>⏰ Deadline: May 20, 2025</p>
<p>Competition details: <a href='https://komiko.app/anigen-competition' target='_blank' class='url'>https://komiko.app/anigen-competition</a></p>
<p>Submit your entry here: <a href='https://docs.google.com/forms/u/1/d/e/1FAIpQLSf1mivMBhyYyRuNM4IMun4VxDEt87LhLhqAoa9gheS1-TwonQ/viewform?usp=dialog' target='_blank' class='url'>https://docs.google.com/forms/u/1/d/e/1FAIpQLSf1mivMBhyYyRuNM4IMun4VxDEt87LhLhqAoa9gheS1-TwonQ/viewform?usp=dialog</a></p>
<p>We’ll review submissions after the deadline and announce winners on June 1. Good luck—and thanks for being part of this new wave of anime + AI storytelling!</p>
</body>
</html>""",
        #   "html": "<strong>hello, world!</strong>",
        "reply_to": "<EMAIL>",
        "headers": {
            "X-Entity-Ref-ID": "123456789"
        },
    }

    try:
        email = resend.Emails.send(params)
        print(f"邮件已成功发送给 {username} ({email})")
    except Exception as e:
        print(f"发送邮件给 {username} 时出错: {str(e)}")
        # 可选：打印响应内容以便调试
        try:
            print(f"响应内容: {email}")  # 如果能访问到响应对象
        except:
            pass
        continue  # 继续处理下一个收件人