export enum ImageToVideoModel {
  VEO,
  MINIMAX,
  RAY,
  KLING,
  PIXVERSE,
  RAY_FLASH_V2,
  WAN,
  WAN_PRO,
  KLING_V2,
  FRAME_PACK,
  VIDU,
  MAGI_1,
  ANISORA,
  HEDRA,
  VIDU_Q1,
  MJ_VIDEO,
}

export enum TalkingHeadQuality {
  STANDARD,
  HIGH,
  HEDRA,
}

export const discount = (count: number) => Math.ceil(count * 0.75);

export const dollarToZaps = (dollar: number) => Math.round(dollar / 0.0005);
// 标准汇率：每次生图=$0.0051=10 Zaps
// $0.01 = 20Zaps
// 线稿上色 消费2个积分
export const LINE_ART_COLORIZE = 20;
export const LINE_ART_COLORIZE_TRANSPARENT = 25;
export const LINE_ART_COLORIZE_GPT4O_TRANSPARENT = 385;
export const FRAME_INTERPOLATION = discount(92);

export const VIDEO_UPSCALE = discount(600);

// 每秒消费积分
export const VIDEO_UPSCALE_UNIT = discount(130);

export const VIDEO_INTERPOLATION = discount(100);

export const IN_BETWEEN = discount(124);

export const BACKGROUND_REMOVAL = 10;
export const IMAGE_UPSCALE = 10;
export const IMAGE_TO_VIDEO = 0;
export const IMAGE_TO_ANIMATION_VEO = discount(5000);
export const IMAGE_TO_ANIMATION_MINIMAX = discount(1000);
export const IMAGE_TO_ANIMATION_RAY = discount(1000);
export const IMAGE_TO_ANIMATION_KLING_5 = discount(1000);
export const IMAGE_TO_ANIMATION_KLING_10 = discount(2000);
export const IMAGE_TO_ANIMATION_KLING_V2_5 = discount(2800);
export const IMAGE_TO_ANIMATION_KLING_V2_10 = discount(5600);
export const IMAGE_TO_ANIMATION_RAY_FLASH_V2_5 = discount(600);
export const IMAGE_TO_ANIMATION_RAY_FLASH_V2_9 = discount(1080);
export const IMAGE_TO_ANIMATION_WAN = discount(800);
export const IMAGE_TO_ANIMATION_WAN_PRO = discount(1600);
export const PHOTO_TO_ANIME = 20;
export const IMAGE_TO_ANIMATION_VEO_UNIT = discount(1000);
export const IMAGE_TO_ANIMATION_FRAME_PACK = discount(400);
export const IMAGE_TO_ANIMATION_VIDU = discount(400);
export const IMAGE_TO_ANIMATION_MAGI_1 = discount(640);
export const IMAGE_TO_ANIMATION_ANISORA = discount(800);
export const IMAGE_TO_ANIMATION_VIDU_Q1 = discount(1000);
export const IMAGE_TO_ANIMATION_MJ_VIDEO = discount(1500);

export const VIDEO_TO_VIDEO_STYLE_TRANSFER = discount(2000);

export const calculateImageToAnimationCost = (
  model: ImageToVideoModel,
  duration?: '5' | '6' | '7' | '8' | '9' | '10',
  resolution?: string,
) => {
  if (model === ImageToVideoModel.MINIMAX) {
    return IMAGE_TO_ANIMATION_MINIMAX;
  }
  if (model === ImageToVideoModel.RAY) {
    return IMAGE_TO_ANIMATION_RAY;
  }
  if (model === ImageToVideoModel.KLING) {
    return duration === '5'
      ? IMAGE_TO_ANIMATION_KLING_5
      : IMAGE_TO_ANIMATION_KLING_10;
  }
  if (model === ImageToVideoModel.RAY_FLASH_V2) {
    return duration === '5'
      ? IMAGE_TO_ANIMATION_RAY_FLASH_V2_5
      : IMAGE_TO_ANIMATION_RAY_FLASH_V2_9;
  }
  if (model === ImageToVideoModel.WAN) {
    return IMAGE_TO_ANIMATION_WAN;
  }
  if (model === ImageToVideoModel.WAN_PRO) {
    return IMAGE_TO_ANIMATION_WAN_PRO;
  }
  if (model === ImageToVideoModel.PIXVERSE) {
    const durationSeconds = +(duration || '5');
    if (resolution === '360p' || resolution === '540p') {
      return dollarToZaps(0.3 / 5) * durationSeconds;
    }
    if (resolution === '720p') {
      return dollarToZaps(0.6 / 5) * durationSeconds;
    }
    if (resolution === '1080p') {
      return dollarToZaps(0.8 / 5) * durationSeconds;
    }
    return dollarToZaps(0.3 / 5) * durationSeconds;
  }
  if (model === ImageToVideoModel.KLING_V2) {
    return duration === '5'
      ? IMAGE_TO_ANIMATION_KLING_V2_5
      : IMAGE_TO_ANIMATION_KLING_V2_10;
  }
  if (model === ImageToVideoModel.FRAME_PACK) {
    return IMAGE_TO_ANIMATION_FRAME_PACK;
  }
  if (model === ImageToVideoModel.VIDU) {
    return IMAGE_TO_ANIMATION_VIDU;
  }
  if (model === ImageToVideoModel.ANISORA) {
    return IMAGE_TO_ANIMATION_ANISORA;
  }
  if (model === ImageToVideoModel.MAGI_1) {
    const durationSeconds = +(duration || '5');
    const baseCost = dollarToZaps(0.32);
    const perSecondCost = dollarToZaps(0.08);

    const resolutionMultiplier = resolution === '480p' ? 0.5 : 1;

    return Math.round(
      (baseCost + perSecondCost * durationSeconds) * resolutionMultiplier,
    );
  }
  if (model === ImageToVideoModel.VIDU_Q1) {
    return IMAGE_TO_ANIMATION_VIDU_Q1;
  }
  if (model === ImageToVideoModel.MJ_VIDEO) {
    return IMAGE_TO_ANIMATION_MJ_VIDEO;
  }
  return IMAGE_TO_ANIMATION_VEO_UNIT * (+(duration as string) || 5);
};

export const RELIGHTING = 46;

export const LATENT_SYNC = 400;

export const IMAGE_FLUX_KONTEXT_PRO = 80;
export const IMAGE_FLUX_KONTEXT_PRO_MINI = 50;
export const IMAGE_FLUX_KONTEXT = 50;
export const IMAGE_FLUX = 50;
export const IMAGE_GEMINI = 80;
export const IMAGE_GEMINI_MINI = 50;
export const IMAGE_NETA = 10;
export const IMAGE_NOOBAI_XL = 52;
export const IMAGE_ANIMAGINE_XL_3_1 = 15;
export const IMAGE_ANIMAGINE_XL_4 = 15;
export const IMAGE_GPT4O = 380;
export const IMAGE_GPT4O_MINI = 140;
export const IMAGE_ILLUSTRIOUS = 10;

export const calculateVideoUpscaleCost = (duration: number) => {
  const remain = Math.max(0, duration - 4);
  return Math.ceil(remain * VIDEO_UPSCALE_UNIT) + VIDEO_UPSCALE;
};

export const calculateLineArtColorizeCost = (
  isTransparent: boolean,
  model: ToolsModelType = ToolsModel.BASIC,
) => {
  // eslint-disable-next-line eqeqeq
  if (model == ToolsModel.ADVANCED) {
    return isTransparent ? LINE_ART_COLORIZE_GPT4O_TRANSPARENT : IMAGE_GPT4O;
  }
  return isTransparent ? IMAGE_GEMINI + 5 : IMAGE_GEMINI;
};

export const calculatePhotoToAnimeCost = (
  model: ToolsModelType = ToolsModel.BASIC,
  // eslint-disable-next-line eqeqeq
) => (model == ToolsModel.ADVANCED ? IMAGE_GPT4O : IMAGE_FLUX_KONTEXT_PRO);

export const ToolsModel = {
  /** Model:Gemini */
  BASIC: 1,
  /** Model:GPT */
  ADVANCED: 2,
} as const;

export type ToolsModelType = (typeof ToolsModel)[keyof typeof ToolsModel];

export const TALKING_HEAD_STANDARD = discount(20);
export const TALKING_HEAD_HIGH = discount(50);
// Hedra定价计算 (基于美元价格后转Zaps)
// 我们估计每个Hedra credit值$0.01，因此：
// 540p: 3 credits/sec = $0.03/sec = 60 Zaps/sec (按$0.0005 = 1 Zap计算)
// 720p: 6 credits/sec = $0.06/sec = 120 Zaps/sec (按$0.0005 = 1 Zap计算)
export const HEDRA_CREDIT_TO_DOLLAR = 0.01; // 每个Hedra credit估计为$0.01
export const HEDRA_540P_ZAPS_PER_SEC = Math.round(
  (3 * HEDRA_CREDIT_TO_DOLLAR) / 0.0005,
); // 60 Zaps/sec
export const HEDRA_720P_ZAPS_PER_SEC = Math.round(
  (6 * HEDRA_CREDIT_TO_DOLLAR) / 0.0005,
); // 120 Zaps/sec

export const calculateTalkingHeadCost = (
  quality: TalkingHeadQuality,
  resolution?: string,
  durationInSeconds: number = 10,
) => {
  if (quality === TalkingHeadQuality.HEDRA) {
    // 根据分辨率和时长计算Zaps
    const zapsPerSecond =
      resolution === '540p' ? HEDRA_540P_ZAPS_PER_SEC : HEDRA_720P_ZAPS_PER_SEC;
    return zapsPerSecond * durationInSeconds;
  }
  return quality === TalkingHeadQuality.HIGH
    ? TALKING_HEAD_HIGH
    : TALKING_HEAD_STANDARD;
};

// Video to Video Style Transfer pricing
// FLUX.1 Kontext Pro: $0.04 per image -> 暂定 40zap一次
// story-engine-inc/test-cog-comfyui: $0.001525 per second
// 3s的视频 -> 耗时3min
// 5s的视频 -> 5min
// 10s的视频 -> 7min
export const VIDEO_TO_VIDEO_STYLE_TRANSFER_FLUX = 40;
export const VIDEO_TO_VIDEO_GENERATION_PER_SECOND = dollarToZaps(0.001525); // Cost per second for video generation

export const calculateVideoToVideoStyleTransferCost = (
  mode: 'template' | 'custom' | 'upload',
) => {
  // For upload mode (use reference), no cost for style transfer
  if (mode === 'upload') {
    return 0;
  }

  // FLUX.1 Kontext Pro charges $0.04 per image regardless of size
  return VIDEO_TO_VIDEO_STYLE_TRANSFER_FLUX;
};

export const calculateVideoToVideoGenerationCost = (
  durationInSeconds: number = 5,
) => {
  // Map video duration to processing time with proper scaling
  let processingTimeInSeconds: number;

  if (durationInSeconds <= 3) {
    // Fixed pricing for short videos: 140 zaps per second
    // 1s = 140 zaps, 2s = 280 zaps, 3s = 420 zaps
    return Math.floor(durationInSeconds * 140);
  }
  if (durationInSeconds <= 5) {
    // Medium videos: scale from 3min (for 3s) to 5min (for 5s)
    // Linear interpolation: 3s->3min, 5s->5min
    const baseTime = 3 * 60; // 3分钟 for 3s video
    const maxTime = 5 * 60; // 5分钟 for 5s video
    const progress = (durationInSeconds - 3) / (5 - 3); // 0 to 1
    processingTimeInSeconds = baseTime + (maxTime - baseTime) * progress;
  } else if (durationInSeconds <= 10) {
    // Long videos: scale from 5min (for 5s) to 10min (for 10s)
    // Linear interpolation: 5s->5min, 10s->10min
    const baseTime = 5 * 60; // 5分钟 for 5s video
    const maxTime = 10 * 60; // 10分钟 for 10s video
    const progress = (durationInSeconds - 5) / (10 - 5); // 0 to 1
    processingTimeInSeconds = baseTime + (maxTime - baseTime) * progress;
  } else {
    // Extra long videos: use linear extrapolation based on 10s -> 10min pattern
    const ratio = (10 * 60) / 10; // 60 seconds processing per 1 second video
    processingTimeInSeconds = durationInSeconds * ratio;
  }

  // Cost based on processing time, not video duration
  // 打个85折
  return Math.ceil(dollarToZaps(0.001525) * processingTimeInSeconds * 0.85);
};
