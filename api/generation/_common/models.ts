import { ModelIds, TASK_TYPES, TaskTypes } from '../../_constants.js';
import {
  calculateImageToAnimationCost,
  calculateVideoToVideoGenerationCost,
  calculateVideoUpscaleCost,
  IMAGE_TO_ANIMATION_MJ_VIDEO,
  ImageToVideoModel,
  IN_BETWEEN,
  VIDEO_INTERPOLATION,
} from '../../tools/_zaps.js';

export type GenerationModel = 'replicate' | 'fal' | 'openai' | 'deer';

interface AIModel {
  name: string;
  platform: GenerationModel;
  type: TaskTypes;
  parseParams: (params: any) => any;
  originalCode?: number;
  cost: (params: any) => number;
  alias?: string;
}

export const ModelConfig: Record<number, AIModel> = {
  [ModelIds.MINIMAX]: {
    name: 'minimax/video-01-live',
    platform: 'replicate',
    type: TASK_TYPES.VIDEO,
    parseParams: (params: any) => {
      if (!params?.prompt || !params?.image) {
        throw new Error('Invalid params');
      }
      return {
        prompt: params.prompt,
        first_frame_image: params.image,
      };
    },
    alias: 'Minimax Hailuo I2V',
    originalCode: ImageToVideoModel.MINIMAX,
    cost: (params: any) =>
      calculateImageToAnimationCost(
        ImageToVideoModel.MINIMAX,
        params.duration,
        params.resolution,
      ),
  },
  [ModelIds.RAY]: {
    name: 'fal-ai/luma-dream-machine/ray-2/image-to-video',
    platform: 'fal',
    type: TASK_TYPES.VIDEO,
    parseParams: (params: any) => ({
      prompt: params.prompt,
      image_url: params.image,
      aspect_ratio: params.aspect_ratio || '16:9',
    }),
    alias: 'Luma Ray 2',
    originalCode: ImageToVideoModel.RAY,
    cost: (params: any) =>
      calculateImageToAnimationCost(
        ImageToVideoModel.RAY,
        params.duration,
        params.resolution,
      ),
  },
  [ModelIds.RAY_FLASH_V2]: {
    name: 'luma/ray-flash-2-720p',
    platform: 'replicate',
    type: TASK_TYPES.VIDEO,
    parseParams: (params: any) => ({
      prompt: params.prompt,
      start_image_url: params.image,
      duration: +params.duration || 5,
      aspect_ratio: params.aspect_ratio || '16:9',
    }),
    alias: 'Luma Ray Flash 2',
    originalCode: ImageToVideoModel.RAY_FLASH_V2,
    cost: (params: any) =>
      calculateImageToAnimationCost(
        ImageToVideoModel.RAY_FLASH_V2,
        params.duration,
        params.resolution,
      ),
  },
  [ModelIds.WAN]: {
    name: 'fal-ai/wan-i2v',
    platform: 'fal',
    type: TASK_TYPES.VIDEO,
    parseParams: (params: any) => ({
      prompt: params.prompt,
      image_url: params.image,
      aspect_ratio: params.aspect_ratio || '16:9',
      resolution: '720p',
    }),
    alias: 'Wan 2.1',
    originalCode: ImageToVideoModel.WAN,
    cost: (params: any) =>
      calculateImageToAnimationCost(
        ImageToVideoModel.WAN,
        params.duration,
        params.resolution,
      ),
  },
  [ModelIds.WAN_PRO]: {
    name: 'fal-ai/wan-pro/image-to-video',
    platform: 'fal',
    type: TASK_TYPES.VIDEO,
    parseParams: (params: any) => ({
      prompt: params.prompt,
      image_url: params.image,
    }),
    alias: 'Wan Pro',
    originalCode: ImageToVideoModel.WAN_PRO,
    cost: (params: any) =>
      calculateImageToAnimationCost(
        ImageToVideoModel.WAN_PRO,
        params.duration,
        params.resolution,
      ),
  },
  [ModelIds.KLING]: {
    name: 'kwaivgi/kling-v1.6-pro',
    platform: 'replicate',
    type: TASK_TYPES.VIDEO,
    parseParams: (params: any) => ({
      prompt: params.prompt,
      duration: +params.duration || 5,
      start_image: params.image,
      aspect_ratio: params.aspect_ratio || '16:9',
    }),
    alias: 'Kling v1.6',
    originalCode: ImageToVideoModel.KLING,
    cost: (params: any) =>
      calculateImageToAnimationCost(
        ImageToVideoModel.KLING,
        params.duration,
        params.resolution,
      ),
  },
  [ModelIds.PIXVERSE]: {
    name: 'fal-ai/pixverse/v3.5/image-to-video',
    platform: 'fal',
    type: TASK_TYPES.VIDEO,
    parseParams: (params: any) => ({
      prompt: params.prompt,
      image_url: params.image,
      resolution: params.resolution || '720p',
      duration: +params.duration || 5,
      aspect_ratio: params.aspect_ratio || '16:9',
    }),
    alias: 'Pixverse v3.5',
    originalCode: ImageToVideoModel.PIXVERSE,
    cost: (params: any) =>
      calculateImageToAnimationCost(
        ImageToVideoModel.PIXVERSE,
        params.duration,
        params.resolution,
      ),
  },
  [ModelIds.KLING_V2]: {
    name: 'fal-ai/kling-video/v2.1/master/image-to-video',
    platform: 'fal',
    type: TASK_TYPES.VIDEO,
    parseParams: (params: any) => ({
      prompt: params.prompt,
      image_url: params.image,
      duration: params.duration || '5',
      aspect_ratio: params.aspect_ratio || '16:9',
    }),
    alias: 'Kling 2.1',
    originalCode: ImageToVideoModel.KLING_V2,
    cost: (params: any) =>
      calculateImageToAnimationCost(
        ImageToVideoModel.KLING_V2,
        params.duration,
        params.resolution,
      ),
  },
  [ModelIds.VEO2]: {
    name: 'fal-ai/veo2/image-to-video',
    platform: 'fal',
    type: TASK_TYPES.VIDEO,
    parseParams: (params: any) => ({
      prompt: params.prompt,
      image_url: params.image,
      duration: `${params.duration || 5}s`,
      aspect_ratio: params.aspect_ratio || 'auto',
    }),
    alias: 'Google Veo 2',
    originalCode: ImageToVideoModel.VEO,
    cost: (params: any) =>
      calculateImageToAnimationCost(
        ImageToVideoModel.VEO,
        params.duration,
        params.resolution,
      ),
  },
  [ModelIds.FRAME_PACK]: {
    name: 'fal-ai/framepack',
    platform: 'fal',
    type: TASK_TYPES.VIDEO,
    parseParams: (params: any) => ({
      prompt: params.prompt,
      image_url: params.image,
      aspect_ratio: params.aspect_ratio || '16:9',
      resolution: '720p',
    }),
    alias: 'Frame Pack',
    originalCode: ImageToVideoModel.FRAME_PACK,
    cost: (params: any) =>
      calculateImageToAnimationCost(
        ImageToVideoModel.FRAME_PACK,
        params.duration,
        params.resolution,
      ),
  },
  [ModelIds.VIDU]: {
    name: 'fal-ai/vidu/image-to-video',
    platform: 'fal',
    type: TASK_TYPES.VIDEO,
    parseParams: (params: any) => ({
      prompt: params.prompt,
      image_url: params.image,
    }),
    alias: 'Vidu Base',
    originalCode: ImageToVideoModel.VIDU,
    cost: (params: any) =>
      calculateImageToAnimationCost(
        ImageToVideoModel.VIDU,
        params.duration,
        params.resolution,
      ),
  },
  /** anisora */
  [ModelIds.ANISORA]: {
    name: 'fal-ai/vidu/image-to-video',
    platform: 'fal',
    type: TASK_TYPES.VIDEO,
    parseParams: (params: any) => ({
      prompt: params.prompt,
      image_url: params.image,
    }),
    alias: 'Anisora',
    originalCode: ImageToVideoModel.ANISORA,
    cost: (params: any) =>
      calculateImageToAnimationCost(
        ImageToVideoModel.ANISORA,
        params.duration,
        params.resolution,
      ),
  },
  [ModelIds.VIDU_Q1]: {
    name: 'fal-ai/vidu/q1/image-to-video',
    platform: 'fal',
    type: TASK_TYPES.VIDEO,
    parseParams: (params: any) => ({
      prompt: params.prompt,
      image_url: params.image,
    }),
    alias: 'Vidu Q1',
    originalCode: ImageToVideoModel.VIDU_Q1,
    cost: (params: any) =>
      calculateImageToAnimationCost(
        ImageToVideoModel.VIDU_Q1,
        params.duration,
        params.resolution,
      ),
  },
  /** magi */
  [ModelIds.MAGI_1]: {
    name: 'fal-ai/magi-distilled/image-to-video',
    platform: 'fal',
    type: TASK_TYPES.VIDEO,
    parseParams: (params: any) => ({
      prompt: params.prompt,
      image_url: params.image,
      aspect_ratio: params.aspect_ratio || 'auto',
      resolution: params.resolution || '720p',
      num_frames: params.num_frames || 96,
      frames_per_second: params.frames_per_second
        ? Number(params.frames_per_second)
        : 24,
      seed: params.seed,
      enable_safety_checker: params.enable_safety_checker !== false,
    }),
    alias: 'Magi-1',
    originalCode: ImageToVideoModel.MAGI_1,
    cost: (params: any) =>
      calculateImageToAnimationCost(
        ImageToVideoModel.MAGI_1,
        params.duration,
        params.resolution,
      ),
  },
  [ModelIds.SE_COG]: {
    name: 'story-engine-inc/test-cog-comfyui:f694c54e46d565be7c2140281c70704f26786131b1bdacd36def9ee4e454ef7b',
    platform: 'replicate',
    type: TASK_TYPES.VIDEO,
    parseParams: (params: any) => {
      if (!params.image) {
        // return new Response(JSON.stringify({ error: 'Missing ref_image parameter' }), { status: 400 });
        throw new Error('Missing reference image parameter');
      }
      if (!params.video) {
        // return new Response(JSON.stringify({ error: 'Missing ref_video parameter' }), { status: 400 });
        throw new Error('Missing video parameter');
      }

      return {
        ref_image: params.image,
        ref_video: params.video,
        positive_prompt: params.prompt || '',
        negative_prompt: '',
        output_format: 'webp',
        workflow_json: '',
        output_quality: 95,
        randomise_seeds: true,
        force_reset_cache: false,
        return_temp_files: false,
        overwrite_prompt_node: 16,
      };
    },
    alias: 'V2V',
    cost: (params: any) => {
      let billingDuration = 5; // 默认5秒

      // Use selectedDuration for billing (what user selected and saw)
      // Use actualDuration for validation only
      const selectedDuration = params.selectedDuration;
      const actualDuration = params.actualDuration;

      if (
        typeof selectedDuration === 'number' &&
        selectedDuration > 0 &&
        selectedDuration <= 15
      ) {
        billingDuration = selectedDuration;
      }

      // Validation: actualDuration should not be too far from selectedDuration
      if (actualDuration && typeof actualDuration === 'number') {
        const difference = Math.abs(actualDuration - selectedDuration);
        if (difference > 2) {
          console.warn(
            'SE_COG Duration Mismatch Warning: Actual duration differs significantly from selected duration',
          );
        }
      }

      return calculateVideoToVideoGenerationCost(billingDuration);
    },
  },
  [ModelIds.SE_WAN]: {
    name: 'story-engine-inc/wan2.1fun-depth:68ebd668560213bf142cc99c66c493d85a56883b8269fbe88cfc411b727494ba',
    platform: 'replicate',
    type: TASK_TYPES.VIDEO,
    parseParams: (params: any) => {
      if (!params.image) {
        // return new Response(JSON.stringify({ error: 'Missing ref_image parameter' }), { status: 400 });
        throw new Error('Missing reference image parameter');
      }
      if (!params.video) {
        // return new Response(JSON.stringify({ error: 'Missing ref_video parameter' }), { status: 400 });
        throw new Error('Missing video parameter');
      }

      return {
        ref_image: params.image,
        ref_video: params.video,
        positive_prompt: params.prompt || '',
        negative_prompt: '',
        output_format: 'webp',
        workflow_json: '',
        output_quality: 95,
        randomise_seeds: true,
        force_reset_cache: false,
        return_temp_files: false,
        overwrite_prompt_node: 16,
      };
    },
    alias: 'Story Engine Wan 2.1',
    cost: (params: any) => {
      let billingDuration = 5; // 默认5秒

      // Use selectedDuration for billing (what user selected and saw)
      // Use actualDuration for validation only
      const selectedDuration = params.selectedDuration;
      const actualDuration = params.actualDuration;

      if (
        typeof selectedDuration === 'number' &&
        selectedDuration > 0 &&
        selectedDuration <= 15
      ) {
        billingDuration = selectedDuration;
      }

      // Validation: actualDuration should not be too far from selectedDuration
      if (actualDuration && typeof actualDuration === 'number') {
        const difference = Math.abs(actualDuration - selectedDuration);
        if (difference > 2) {
          console.warn(
            'SE_WAN Duration Mismatch Warning: Actual duration differs significantly from selected duration',
          );
        }
      }

      return calculateVideoToVideoGenerationCost(billingDuration);
    },
  },
  [ModelIds.VIDEO_UPSCALE]: {
    name: 'lucataco/real-esrgan-video:c23768236472c41b7a121ee735c8073e29080c01b32907740cfada61bff75320',
    platform: 'replicate',
    type: TASK_TYPES.VIDEO,
    parseParams: (params: any) => {
      const videoPath = params.video_path;
      if (!videoPath) {
        // return failed('Video path is required');
        throw new Error('Video path is required');
      }

      const inputVideo = videoPath;
      if (
        !videoPath.startsWith('http://') &&
        !videoPath.startsWith('https://')
      ) {
        // return failed('Video path must be a valid URL');
        throw new Error('Video path must be a valid URL');
      }
      return {
        video_path: inputVideo,
        resolution: params.resolution,
        duration: params.duration,
      };
    },
    alias: 'Video Upscale',
    cost: (params: any) => {
      const duration = params.duration || 1;
      return calculateVideoUpscaleCost(duration);
    },
  },
  [ModelIds.VIDEO_INTERPOLATION]: {
    name: 'zsxkib/film-frame-interpolation-for-large-motion:222d67420da179935a68afff47093bab48705fe9e09c3c79268c1eb2ee7c5e91',
    platform: 'replicate',
    type: TASK_TYPES.VIDEO,
    parseParams: (params: any) => ({
      mp4: params.video,
      num_interpolation_steps: params.interpolate_steps,
      playback_frames_per_second: params.frames_per_second,
    }),
    alias: 'Video Interpolation',
    cost: (params: any) => {
      const duration = Number(params.duration) || 1;
      const interpolate_steps = Number(params.interpolate_steps) || 1;
      return VIDEO_INTERPOLATION * duration * interpolate_steps;
    },
  },
  [ModelIds.IN_BETWEEN]: {
    name: 'fofr/tooncrafter:0486ff07368e816ec3d5c69b9581e7a09b55817f567a0d74caad9395c9295c77',
    platform: 'replicate',
    type: TASK_TYPES.VIDEO,
    parseParams: (params: any) => {
      const images = params?.images || [];
      if (images.length < 2) {
        throw new Error('At least 2 images are required');
      }
      const aiInputParams = {
        prompt: params.prompt,
        max_width: 512,
        max_height: 512,
        loop: false,
        interpolate: false,
        negative_prompt: '',
        color_correction: true,
      };
      images.slice(0, 5).forEach((image, index) => {
        aiInputParams[`image_${index + 1}`] = image;
      });
      return aiInputParams;
    },
    alias: 'inbetween',
    cost: (params: any) => {
      const count = Math.max(params?.images?.length || 1, 2);
      return IN_BETWEEN * (count - 1);
    },
  },
  [ModelIds.MJ_VIDEO]: {
    name: 'mj_fast_video',
    platform: 'deer',
    type: TASK_TYPES.VIDEO,
    parseParams: (params: any) => {
      if (!params.image) {
        throw new Error('Image is required');
      }
      return {
        prompt: params.prompt,
        image: params.image,
        videoType: 'vid_1.1_i2v_480',
        mode: 'fast',
        animateMode: 'manual',
        motion: 'low',
      };
    },
    alias: 'Midjourney Video',
    originalCode: ImageToVideoModel.MJ_VIDEO,
    cost(params: any) {
      return IMAGE_TO_ANIMATION_MJ_VIDEO;
    },
  },
} as const;
