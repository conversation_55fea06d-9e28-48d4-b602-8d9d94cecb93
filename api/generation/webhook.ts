import sodium from 'libsodium-wrappers';
import * as crypto from 'crypto';
import {
  createSupabase,
  deleteMediaByUrl,
  uploadMediaFromUrl,
} from '../_utils/index.js';
import { GenerationStatus, TASK_TYPES, TaskTypes } from '../_constants.js';
import { CreditModel } from '../_models/credit.js';
import { waitUntil } from '@vercel/functions';
import { ModelConfig } from './_common/models.js';

const JWKS_URL = 'https://rest.alpha.fal.ai/.well-known/jwks.json';
const JWKS_CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
let jwksCache = null;
let jwksCacheTime = 0;

const WEBHOOK_SECRET = process.env.REPLICATE_WEBHOOK_SECRET || '';
// const MAX_DIFF_IN_SECONDS = 300;

async function fetchJwks() {
  const currentTime = Date.now();
  if (!jwksCache || currentTime - jwksCacheTime > JWKS_CACHE_DURATION) {
    const response = await fetch(JWKS_URL);
    if (!response.ok) {
      throw new Error(`JWKS fetch failed: ${response.status}`);
    }
    jwksCache = (await response.json()).keys || [];
    jwksCacheTime = currentTime;
  }
  return jwksCache;
}

export async function verifyWebhookSignature(
  requestId,
  userId,
  timestamp,
  signatureHex,
  body,
) {
  if (!requestId || !userId || !timestamp || !signatureHex || !body) {
    return false;
  }
  await sodium.ready;

  // Validate timestamp (within ±5 minutes)
  // try {
  //   const timestampInt = parseInt(timestamp, 10);
  //   const currentTime = Math.floor(Date.now() / 1000);
  //   if (Math.abs(currentTime - timestampInt) > 300) {
  //     console.error('Timestamp is too old or in the future.');
  //     return false;
  //   }
  // } catch (e) {
  //   console.error('Invalid timestamp format:', e);
  //   return false;
  // }

  // Construct the message to verify
  try {
    const messageParts = [
      requestId,
      userId,
      timestamp,
      crypto.createHash('sha256').update(body).digest('hex'),
    ];
    if (messageParts.some(part => part === null)) {
      console.error('Missing required header value.');
      return false;
    }
    const messageToVerify = messageParts.join('\n');
    const messageBytes = Buffer.from(messageToVerify, 'utf-8');

    // Decode signature
    let signatureBytes;
    try {
      signatureBytes = Buffer.from(signatureHex, 'hex');
    } catch (e) {
      console.error('Invalid signature format (not hexadecimal).');
      return false;
    }

    // Fetch public keys
    let publicKeysInfo;
    try {
      publicKeysInfo = await fetchJwks();
      if (!publicKeysInfo.length) {
        console.error('No public keys found in JWKS.');
        return false;
      }
    } catch (e) {
      console.error('Error fetching JWKS:', e);
      return false;
    }
    console.log(publicKeysInfo);

    // verify signature with each public key
    for (const keyInfo of publicKeysInfo) {
      try {
        const publicKeyB64Url = keyInfo.x;
        if (typeof publicKeyB64Url !== 'string') {
          continue;
        }
        const publicKeyBytes = Buffer.from(publicKeyB64Url, 'base64url');
        const isValid = sodium.crypto_sign_verify_detached(
          signatureBytes,
          messageBytes,
          publicKeyBytes,
        );
        if (isValid) {
          return true;
        }
      } catch (e) {
        console.error('Verification failed with a key:', e);
        continue;
      }
    }

    console.error('Signature verification failed with all keys.');
    return false;
  } catch (e) {
    console.error('Error constructing message:', e);
    return false;
  }
}

const supabase = createSupabase();
export const verifyReplicateSignature = async (
  webhookId: string | null,
  webhookTimestamp: string | null,
  webhookSignatures: string | null,
  body: string,
) => {
  if (!webhookId || !webhookTimestamp || !webhookSignatures || !body) {
    return false;
  }
  // const timestamp = parseInt(webhookTimestamp);
  // const now = Math.floor(Date.now() / 1000);
  // const diff = Math.abs(now - timestamp);

  // if (diff > MAX_DIFF_IN_SECONDS) {
  //   return false
  // }
  if (!WEBHOOK_SECRET) {
    return false;
  }
  const signedContent = `${webhookId}.${webhookTimestamp}.${body}`;

  // Get the secret key (remove 'whsec_' prefix)
  const secretKey = WEBHOOK_SECRET.split('_')[1];
  const secretBytes = Buffer.from(secretKey, 'base64');

  // Calculate the HMAC signature
  const computedSignature = crypto
    .createHmac('sha256', secretBytes)
    .update(signedContent)
    .digest('base64');

  // Parse the webhook signatures
  const expectedSignatures = webhookSignatures
    .split(' ')
    .map(sig => sig.split(',')[1]);

  // console.log(expectedSignatures, computedSignature)

  const isValid = expectedSignatures.some(expectedSig =>
    crypto.timingSafeEqual(
      Buffer.from(expectedSig),
      Buffer.from(computedSignature),
    ),
  );

  if (!isValid) {
    return false;
  }
  return true;
};
const deleteUploadedFile = async (payload: string | null) => {
  if (!payload) {
    return;
  }
  try {
    const data = JSON.parse(payload);
    // const file = data.video || data.image;
    let images = data.images ?? [];
    if (!Array.isArray(images)) {
      images = [];
    }
    if (!data.should_delete_media) {
      return;
    }

    const files = [data.video, data.image, ...images].filter(file => file);

    for (const file of files) {
      const result = await deleteMediaByUrl(file);
      if (result.success) {
        console.log('delete file success', file);
      } else {
        console.error('delete file failed', file);
      }
    }
  } catch (e) {
    console.error('delete file failed', e);
  }
};

const failedTask = async (taskId: string) => {
  const { error: updateError } = await supabase
    .from('generation_tasks')
    .update({
      status: GenerationStatus.FAILED,
    })
    .eq('task_id', taskId);
  if (updateError) {
    console.error('update error', updateError);
    return new Response('Internal Server Error', { status: 200 });
  }
  return new Response('OK');
};

// 添加接口定义
interface ImageGeneration {
  id?: number;
  created_at?: string;
  prompt?: string;
  model?: string;
  url_path: string;
  tool: string;
  user_id: string;
  meta_data?: string;
}

interface VideoGeneration {
  id?: number;
  created_at?: string;
  video_url: string;
  tool: string;
  user_id: string;
  prompt: string;
  model?: string;
  meta_data?: string;
}

export function parseModelName(modelId: number): string {
  if (!modelId) {
    return '';
  }

  const modelConfig = ModelConfig[modelId];
  if (modelConfig) {
    const modelName = modelConfig.alias || modelConfig.name;

    return modelName;
  }

  return '';
  // return `Model ${modelId}`;
}

// 创建图片生成记录
async function createImageGeneration(
  imageData: Omit<ImageGeneration, 'id' | 'created_at'>,
) {
  const { data, error } = await supabase
    .from('ImageGeneration')
    .insert([
      {
        url_path: imageData.url_path,
        tool: imageData.tool,
        user_id: imageData.user_id,
        prompt: imageData.prompt,
        model: imageData.model,
        meta_data: imageData.meta_data,
      },
    ])
    .select();

  if (error) {
    console.error('Error inserting image generation:', error);
    return null;
  }
  return data;
}

// 创建视频生成记录
async function createVideoGeneration(
  videoData: Omit<VideoGeneration, 'id' | 'created_at'>,
) {
  const { data, error } = await supabase
    .from('VideoGeneration')
    .insert([
      {
        video_url: videoData.video_url,
        tool: videoData.tool,
        user_id: videoData.user_id,
        prompt: videoData.prompt,
        model: videoData.model,
        meta_data: videoData.meta_data,
      },
    ])
    .select();

  if (error) {
    console.error('Error inserting video generation:', error);
    return null;
  }
  return data;
}

// 保存生成结果到相应表中
async function saveGenerationResult(
  output: string,
  userId: string,
  payload: string,
  modelId: number,
  type: TaskTypes,
) {
  if (!payload) {
    return;
  }

  try {
    const payloadData = JSON.parse(payload);
    const tool = payloadData.tool || '';
    const prompt = payloadData.originalPrompt || payloadData.prompt || '';
    const modelName = parseModelName(modelId);

    if (type === TASK_TYPES.VIDEO) {
      // 保存视频生成记录
      await createVideoGeneration({
        video_url: output,
        tool,
        user_id: userId,
        prompt,
        model: modelName,
        meta_data: payloadData.meta_data
          ? JSON.stringify(payloadData.meta_data)
          : undefined,
      });
      console.log('Video generation record saved successfully');
    } else if (type === TASK_TYPES.IMAGE) {
      // 保存图片生成记录
      await createImageGeneration({
        url_path: output,
        tool,
        user_id: userId,
        prompt,
        model: modelName,
        meta_data: payloadData.meta_data
          ? JSON.stringify(payloadData.meta_data)
          : undefined,
      });
      console.log('Image generation record saved successfully');
    }
  } catch (e) {
    console.error('Error saving generation result:', e);
  }
}

const handler = async (request: Request) => {
  let platform: 'fal' | 'replicate' | 'deer' | 'unknown' = 'deer';
  let output = '';
  let taskId = '';
  // console.log('headers', request.headers)
  if (request.headers.get('x-fal-webhook-signature')) {
    platform = 'fal';
    const signature = request.headers.get('x-fal-webhook-signature');
    const timestamp = request.headers.get('x-fal-webhook-timestamp');
    const userId = request.headers.get('x-fal-webhook-user-id');
    const requestId = request.headers.get('x-fal-webhook-request-id');
    const text = await request.text();
    console.log('body', text);
    const body = Buffer.from(text);
    const isValid = await verifyWebhookSignature(
      requestId,
      userId,
      timestamp,
      signature,
      body,
    );
    if (!isValid) {
      console.log(
        'invalid signature',
        requestId,
        userId,
        timestamp,
        signature,
        body,
      );
      return new Response('Invalid signature', { status: 200 });
    }
    const data = JSON.parse(text);
    taskId = data.requestId || data.request_id;

    if (data.status?.toLowerCase() !== 'ok' || data.error) {
      return failedTask(taskId);
    }

    output =
      data?.output || data?.payload?.video?.url || data?.payload?.image?.url;
    if (Array.isArray(output)) {
      output = output[0];
    }
  }

  if (request.headers.get('webhook-signature')) {
    platform = 'replicate';
    const signature = request.headers.get('webhook-signature');
    const timestamp = request.headers.get('webhook-timestamp');
    const webhookId = request.headers.get('webhook-id');
    const text = await request.text();
    // console.log('body', text);
    const isValid = await verifyReplicateSignature(
      webhookId,
      timestamp,
      signature,
      text,
    );
    if (!isValid) {
      console.log(
        'webhook invalid signature',
        webhookId,
        timestamp,
        signature,
        text,
      );
      return new Response('Invalid signature', { status: 200 });
    }
    const data = JSON.parse(text);
    taskId = data.id;

    if (data.status?.toLowerCase() !== 'succeeded' || data.error) {
      console.log('failed task', data);
      return failedTask(taskId);
    }

    output =
      data?.output || data?.payload?.video?.url || data?.payload?.image?.url;
    if (Array.isArray(output)) {
      output = output[0];
    }
  }

  if (platform === 'deer') {
    const data = await request.json();
    if (!data.id) {
      platform = 'unknown';
      return new Response('Unknown platform', { status: 200 });
    }

    console.log('deer data', data);

    const status = data?.status as string;
    taskId = data.id;
    if (status?.toLowerCase() === 'success') {
      output = data.videoUrls?.[0]?.url;
      console.log('deer output', output);
    } else if (data.failReason) {
      console.log('failed task', data);
      return failedTask(taskId);
    } else if (status?.toLowerCase() === 'in_progress') {
      return new Response('In progress', { status: 200 });
    }
  }

  // if (platform === 'unknown') {
  //   console.log('unknown platform', request.headers);
  //   console.log('body', await request.text());
  //   return new Response('Unknown platform', { status: 200 });
  // }

  if (!taskId) {
    return new Response('Task not found', { status: 200 });
  }

  const { data: tasks, error } = await supabase
    .from('generation_tasks')
    .select('cost, user_id, payload, model_id, type, status')
    .eq('task_id', taskId);

  // console.log('tasks', tasks)
  const taskData = tasks?.[0];
  if (!taskData?.user_id || error) {
    console.log('taskId', taskId);
    console.error('select task error', error);
    return new Response('Task not found', { status: 200 });
  }

  if (
    taskData.status === GenerationStatus.SUCCEEDED ||
    taskData.status === GenerationStatus.FINISHED
  ) {
    console.log('Task already processed successfully:', taskId);
    return new Response('OK');
  }

  if (taskData.status === GenerationStatus.FAILED) {
    console.log('Task already marked as failed:', taskId);
    return new Response('OK');
  }

  if (!output) {
    console.log('No output found for task:', taskId);
    // return new Response('No output found', { status: 400 });
    const { error: updateError } = await supabase
      .from('generation_tasks')
      .update({
        status: GenerationStatus.FAILED,
      })
      .eq('task_id', taskId);
    if (updateError) {
      console.error('update error', updateError);
      return new Response('Internal Server Error', { status: 200 });
    }
    return new Response('OK');
  }

  try {
    const result = await uploadMediaFromUrl(output, taskData.user_id);
    if (result) {
      output = result;
    }
  } catch (e) {
    console.error('upload error', e);
  }

  const { error: updateError } = await supabase
    .from('generation_tasks')
    .update({
      status: GenerationStatus.SUCCEEDED,
      output,
    })
    .eq('task_id', taskId);
  if (updateError) {
    console.error('update error', updateError);
    return new Response('Internal Server Error', { status: 200 });
  }
  const creditModel = new CreditModel(taskData.user_id);
  await creditModel.deductCredit(taskData.cost);
  waitUntil(deleteUploadedFile(taskData.payload));

  const finalModelId = taskData.model_id || 0;
  await saveGenerationResult(
    output,
    taskData.user_id,
    taskData.payload,
    finalModelId,
    taskData.type,
  );
  return new Response('OK');
};

export const POST = async (request: Request) => {
  try {
    return handler(request);
  } catch (error) {
    console.error(error);
    return new Response('Internal Server Error', { status: 200 });
  }
};
