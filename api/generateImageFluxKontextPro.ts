import { v4 as uuidv4 } from 'uuid';
import Replicate from 'replicate';
import { CreditModel } from './_models/credit.js';
import { IMAGE_FLUX_KONTEXT_PRO } from './tools/_zaps.js';
import { GenerationStatus, TASK_TYPES } from './_constants.js';
import { authMiddleware } from './_utils/middlewares/auth.js';
import with<PERSON>and<PERSON> from './_utils/withHandler.js';
import {
  bindGenerationLogData,
  bindParamsMiddleware,
} from './_utils/middlewares/index.js';
import {
  createSupabase,
  failed,
  getUserId,
  replaceCharacterId,
} from './_utils/index.js';
import { translateMiddleware } from './_utils/middlewares/translate.js';
import { tryGenerateHandler } from './_utils/middlewares/tryGenerate.js';
import { getCharacterIds } from './generateCanvasImageGeminiNew.js';

const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN,
});

const supabase = createSupabase();

export async function URLToBlob(url: string) {
  const response = await fetch(url);
  const blob = await response.blob();
  return blob;
}

export interface GenerateImageFluxKontextProParams {
  prompt: string;
  image: string;
  aspectRatio: string;
}

export const generateImageFluxKontextPro = async (
  params: GenerateImageFluxKontextProParams,
) => {
  const { prompt, image, aspectRatio } = params;
  if (!prompt) {
    console.error('Prompt is required');
    return '';
  }
  const finalPrompt = replaceCharacterId(prompt);
  // console.log('finalPrompt', finalPrompt);
  const output = await replicate.run('black-forest-labs/flux-kontext-pro', {
    input: {
      prompt: finalPrompt,
      input_image: image,
      aspect_ratio: aspectRatio || 'match_input_image',
      safety_tolerance: 6,
    },
  });
  return output;
};

export const generateHandler = (modelName: string, imageCost: number) =>
  async function handler(request: RequestImproved) {
    try {
      const {
        prompt,
        originalPrompt,
        init_images = [],
        size,
        num_images = 1,
        store_supabase,
        tool,
      } = (request as any).params;
      bindGenerationLogData(request, {
        model: modelName,
        generationResult: GenerationStatus.FAILED,
        tool,
        cost: 0,
      });
      if (!prompt) {
        return new Response(JSON.stringify({ error: 'Prompt is required' }), {
          status: 400,
        });
      }

      const userId = getUserId(request);

      const cost = num_images * imageCost;
      // Check if user has enough credits
      const creditModel = new CreditModel(userId as string);
      const success = await creditModel.canConsume(cost);
      if (!success) {
        return new Response(
          JSON.stringify({
            error:
              'Insufficient Zaps! Please visit the Profile page → click More Zaps',
          }),
          { status: 402 },
        );
      }

      let aspect_ratio;
      if (!size) {
        aspect_ratio = undefined;
      } else if (size === 'square') {
        aspect_ratio = '1:1';
      } else if (size === 'landscape') {
        aspect_ratio = '3:2';
      } else if (size === 'portrait') {
        aspect_ratio = '2:3';
      } else {
        const width = size.width;
        const height = size.height;
        const ratios = [
          21 / 9,
          16 / 9,
          3 / 2,
          4 / 3,
          5 / 4,
          1 / 1,
          4 / 5,
          3 / 4,
          2 / 3,
          9 / 16,
          9 / 21,
        ];
        const ratio_strs = [
          '21:9',
          '16:9',
          '3:2',
          '4:3',
          '5:4',
          '1:1',
          '4:5',
          '3:4',
          '2:3',
          '9:16',
          '9:21',
        ];
        console.assert(
          ratios.length === ratio_strs.length,
          'Ratios and ratio_strs must have the same length',
        );
        const closestRatio = ratios.reduce(
          // eslint-disable-next-line no-confusing-arrow
          (closest, ratio) =>
            Math.abs(ratio - width / height) <
            Math.abs(closest - width / height)
              ? ratio
              : closest,
          ratios[0],
        );
        aspect_ratio = ratio_strs[ratios.indexOf(closestRatio)];
      }

      const imageUrls: string[] = [];
      const character_ids: string[] = getCharacterIds(prompt);

      let characterRefPrompt = '';
      if (character_ids.length) {
        const characterId = character_ids[0];
        characterRefPrompt = `Input image is the portrait of character <${characterId}>.`;
        if (characterRefPrompt) {
          characterRefPrompt = `${characterRefPrompt}
    The generated image should keep the same identity, appearance, facial features, clothing of the input character images.`;
        }
      }

      const promptImproved = `${characterRefPrompt}
      ${init_images?.length && !characterRefPrompt ? 'Use the input image as a reference image, smartly reference it when generating the image.' : ''}
  Generate a high quality image with the following prompt: ${prompt}`;
      // 该模型只能使用一张图片。没有参考图，有自定义角色，则使用自定义角色图作为参考图；有参考图，直接使用参考图
      if (!init_images?.length && character_ids?.length) {
        const { data: characterData, error } = await supabase
          .from('CustomCharacters')
          .select('character_pfp,character_uniqid')
          .eq('character_uniqid', character_ids[0]);
        if (error) {
          console.error(error);
          // return { error: 'Error fetching characters' }
        }
        const data = characterData;
        if (!data) {
          console.error('No data');
          // return { error: 'Error fetching characters' }
        } else {
          const imageUrl = data[0].character_pfp;
          init_images.push(imageUrl);
        }
      }

      const promises = new Array(num_images).fill(0).map(() =>
        generateImageFluxKontextPro({
          prompt: promptImproved,
          image: init_images?.[0],
          aspectRatio: aspect_ratio,
        }).catch(),
      );

      const results = await Promise.all(promises);

      for (const imageUri of results as any) {
        console.log('imageUri', imageUri);
        // const imageData = result.images[0];
        // const imageUri = `data:image/jpeg;base64,${imageData}`;
        let imageUrl = '';
        let imageUrlPath = '';

        if (store_supabase) {
          const imagePath = `image_generation/${userId}/${new Date().toISOString().split('T')[0]}-${uuidv4()}.webp`;
          const blob = await URLToBlob(imageUri);
          const supaResult = await supabase.storage
            .from('husbando-land')
            .upload(imagePath, blob);
          if (!supaResult.error) {
            imageUrl = supabase.storage
              .from('husbando-land')
              .getPublicUrl(imagePath).data.publicUrl;
            imageUrls.push(imageUrl);
            imageUrlPath = imageUrl.replace(
              `${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/husbando-land/image_generation/`,
              '',
            );
          }
        }
        if (imageUrl === '') {
          imageUrls.push(imageUri);
        }
        const { error } = await supabase
          .from('ImageGeneration')
          .insert([
            {
              prompt: originalPrompt ?? prompt,
              model: modelName,
              url_path: imageUrlPath,
              user_id: userId,
              tool,
            },
          ])
          .select();
        if (error) {
          console.error(error);
        }
      }

      const validImageCount = imageUrls.length;
      const realCost = validImageCount * imageCost;
      const result = await creditModel.deductCredit(realCost);
      if (!result) {
        return failed('no zaps');
      }
      bindGenerationLogData(request, {
        model: modelName,
        generationResult: GenerationStatus.SUCCEEDED,
        tool,
        cost: realCost,
      });
      return new Response(JSON.stringify(imageUrls), { status: 200 });
    } catch (error) {
      console.error('Error generating image:', error);
      return new Response(
        JSON.stringify({
          error: (error as any)?.message || 'Failed to generate image',
        }),
        { status: 500 },
      );
    }
  };

export const POST = withHandler(
  generateHandler('Flux', IMAGE_FLUX_KONTEXT_PRO),
  [
    authMiddleware,
    bindParamsMiddleware,
    translateMiddleware,
    tryGenerateHandler(TASK_TYPES.IMAGE),
  ],
);
