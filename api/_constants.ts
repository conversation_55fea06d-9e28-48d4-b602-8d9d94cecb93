export const ERROR_CODES = {
  // POST删除错误
  POST_DELETE_ERROR: 1001,
  POST_NOT_FOUND: 1002,
  POST_ID_NEEDED: 1003,

  GENERATION_FAILED: 1004,
  GENERATION_MODEL_NOT_FOUND: 1005,
  GENERATION_TASK_NOT_FOUND: 1006,
  INVALID_PARAMS: 1007,
  // photo to anime vip style requires subscription
  VIP_STYLE_REQUIRES_SUBSCRIPTION: 1008,

  // 速率限制错误
  RATE_LIMIT_EXCEEDED: 1010,
  // no zaps
  NOT_ENOUGH_ZAPS: 1011,
  CHARACTER_NOT_ENOUGH: 1012,
};

export const LANGUAGES = [
  'en', // 英语
  'ja', // 日语
  'id', // 印尼语
  'hi', // 印地语
  'zh-CN', // 简体中文
  'zh-TW', // 繁体中文
  'ko', // 韩语
  'es', // 西班牙语
  'fr', // 法语
  'de', // 德语
  'pt', // 葡萄牙语
  'ru', // 俄语
  'th', // 泰语
  'vi', // 越南语
];

// 生图类型
export const TASK_TYPES = {
  IMAGE: 1,
  VIDEO: 2,
  CHARACTER: 11,
} as const;

export type TaskTypes = (typeof TASK_TYPES)[keyof typeof TASK_TYPES];

export const GenerationStatus = {
  FAILED: 0,
  PROCESSING: 1,
  SUCCEEDED: 2,
  PENDING: 3,
  FINISHED: 4, // 在cleanup中只能判断结束，不知道成功或失败
} as const;

export type GenerationStatusType =
  (typeof GenerationStatus)[keyof typeof GenerationStatus];

export const ModelIds = {
  MINIMAX: 1,
  RAY: 2,
  RAY_FLASH_V2: 3,
  WAN: 4,
  WAN_PRO: 5,
  KLING: 6,
  PIXVERSE: 7,
  KLING_V2: 8,
  VEO2: 9,
  FRAME_PACK: 10,
  VIDU: 11,
  ANISORA: 12,
  VIDU_Q1: 13,
  /**
   * @deprecated
   */
  MAGI_1: 14,
  HEDRA: 15,
  SE_COG: 16,
  SE_WAN: 17,
  VIDEO_UPSCALE: 18,
  VIDEO_INTERPOLATION: 19,
  IN_BETWEEN: 20,
  MJ_VIDEO: 21,
} as const;
