import { createClient } from '@supabase/supabase-js';
import { parse } from 'cookie';
import { decode } from 'next-auth/jwt';
import { CreditModel } from './_models/credit.js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

interface Profile {
  id: string;
  authUserId: string;
  user_uniqid: string;
  created_at: string;
  user_name: string;
  image: string;
  user_desc: string;
  num_followers: number;
  num_following: number;
  num_posts?: number; // 添加posts数量字段
  credit: number;
  date_checkin: string;
  date_post: string;
  date_like: string;
  tour_completed: boolean;
  followed: boolean;
  is_cpp: boolean;
  plan?: string; // 添加VIP计划字段
  email: string;
}

interface VideoItem {
  id: number;
  url_path: string;
  thumbnail_path?: string;
  title?: string;
  description?: string;
  created_at: string;
  user_uniqid?: string;
}

async function updateRefCode(
  authUserId: string,
  newRefCode: string,
): Promise<void> {
  supabase
    .rpc('update_ref_code', {
      authuserid: authUserId,
      new_ref_code: newRefCode,
    })
    .then(({ error }) =>
      error
        ? console.error('Error updating ref_code:', error)
        : console.log('Ref_code update function called successfully'),
    );
}

const fetchNumFollowers = async (authUserId: string) => {
  const { data, error } = await supabase.rpc('update_follow_counts', {
    authuserid: authUserId,
  });
  if (error) {
    console.error('Error fetching follower count:', error);
    return null;
  }
  return data;
};

const fetchNumFollowersPublic = async (user_uniqid: string) => {
  const { data, error } = await supabase.rpc('update_follow_counts_by_uniqid', {
    user_uniqid,
  });
  if (error) {
    console.error('Error fetching follower count:', error);
    return null;
  }
  return data;
};

export async function POST(request: Request) {
  try {
    let {
      method,
      page,
      authUserId,
      new_username,
      new_bio,
      new_image,
      user_uniqid,
    } = await request.json();

    // ! FETCH COOKIE IF USER ID NOT PROVIDED
    if (!authUserId) {
      // Parse cookies from the request headers
      const cookies = parse(request.headers.get('cookie') || '');
      const sessionToken = cookies['next-auth.session-token'];
      if (!sessionToken) {
        if (method == 'profile' || method == 'edit-profile') {
          return new Response(JSON.stringify({ error: 'Log in to continue' }), {
            status: 401,
          });
        }
        authUserId = '';
      } else {
        let token: any = null;
        try {
          token = await decode({
            token: sessionToken,
            secret: process.env.NEXTAUTH_SECRET!,
          });
        } catch (error) {
          console.error('Error fetching user:', error);
        }
        if (!token) {
          if (method === 'profile' || method === 'edit-profile') {
            return new Response(
              JSON.stringify({ error: 'Invalid login status' }),
              { status: 401 },
            );
          }
          authUserId = '';
        } else {
          authUserId = token.id;
        }
      }

      // ! SAVE REF CODE
      const refCode = cookies['ref'];
      if (refCode) {
        console.log('found ref code:', refCode);
        updateRefCode(authUserId, refCode);
      }

      // ! UPDATE FOLLOWER FOUNT
      fetchNumFollowers(authUserId);
    }

    console.log('running profile request, auth user:', authUserId);

    const creditModel = new CreditModel(authUserId);

    // ! FETCH USER VIDEOS
    if (method === 'fetch-videos') {
      // Default to page 1 if not provided
      const pageNumber = page || 1;
      const pageSize = 12; // Number of videos per page
      const offset = (pageNumber - 1) * pageSize;

      // Get the user_uniqid from the database using authUserId
      const { data: userData, error: userError } = await supabase
        .from('User')
        .select('user_uniqid')
        .eq('id', authUserId)
        .single();

      if (userError) {
        return new Response(JSON.stringify({ error: userError.message }), {
          status: 400,
        });
      }

      const userUniqid = userData.user_uniqid;

      // Query videos from UserVideos table
      const { data: videos, error } = await supabase
        .from('UserVideos')
        .select(
          'id, url_path, thumbnail_path, title, description, created_at, user_uniqid',
        )
        .eq('user_uniqid', userUniqid)
        .order('created_at', { ascending: false })
        .range(offset, offset + pageSize - 1);

      if (error) {
        return new Response(JSON.stringify({ error: error.message }), {
          status: 400,
        });
      }

      return new Response(JSON.stringify(videos || []), {
        status: 200,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // ! FETCH AUTHED USER INFO
    if (method === 'profile') {
      const { data, error } = await supabase
        .from('User')
        .select(
          'id, created_at, user_name, image, user_desc, num_followers, num_following, credit, date_checkin, date_post, date_like, tour_completed, user_uniqid, invite_code, is_cpp, email',
        )
        .eq('id', authUserId)
        .single(); // Since we expect only one user to match the ID

      if (error) {
        return new Response(JSON.stringify({ error: error.message }), {
          status: 400,
        });
      }
      // 创建profile对象并添加必要字段
      const profile: Profile = {
        id: data.id || '',
        authUserId: data.id || '', // 确保不是 undefined
        user_uniqid: data.user_uniqid || '',
        created_at: data.created_at || '',
        user_name: data.user_name || '',
        image: data.image || '',
        user_desc: data.user_desc || '',
        num_followers: data.num_followers || 0,
        num_following: data.num_following || 0,
        credit: data.credit || 0,
        date_checkin: data.date_checkin || '',
        date_post: data.date_post || '',
        date_like: data.date_like || '',
        tour_completed: data.tour_completed || false,
        followed: false, // 自己的profile不需要这个字段
        is_cpp: data.is_cpp || false,
        email: data.email || '',
        num_posts: 0, // 初始值，下面会更新
        plan: 'Free', // 初始值，下面会更新
      };

      if (profile.credit === null || profile.credit === undefined) {
        await supabase
          .from('User')
          .update({ credit: 500 }) // initial credits
          .eq('id', authUserId);
        profile.credit = 500;
        profile.plan = 'Free';
      } else {
        profile.credit = await creditModel.get();
        const freeCredit = await creditModel.getFreeCredit();

        // 检查CPP状态
        // if (profile.is_cpp) {
        //   profile.plan = 'CPP';
        // } else {
        // 获取Stripe订阅计划
        const allSubscriptions = creditModel.subscriptions || [];
        profile.plan =
          allSubscriptions.filter(s => s.plan !== 'CPP')[0]?.plan || 'Free';
        // }

        // 添加额外字段到data对象中，用于返回
        (profile as any).free_credit = freeCredit;
        (profile as any).plan_codes =
          creditModel.subscriptions.map(s => s.plan_code) || [];
        (profile as any).subscription_status =
          creditModel.subscriptions?.[0]?.status || 0;
        (profile as any).invite_code = data.invite_code;
      }

      // 获取用户的posts数量
      const { count: postsCount, error: postsError } = await supabase
        .from('AppPosts')
        .select('id', { count: 'exact', head: true })
        .eq('authUserId', authUserId);

      if (postsError) {
        console.error('Error fetching posts count:', postsError);
        profile.num_posts = 0;
      } else {
        profile.num_posts = postsCount || 0;
      }

      return new Response(JSON.stringify(profile), {
        status: 200,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // ! FETCH PUBLIC USER PROFILE
    if (method == 'public-profile') {
      console.log('fetching public profile, user uniqid:', user_uniqid);

      fetchNumFollowersPublic(user_uniqid);

      const { data, error } = await supabase
        .from('User')
        .select(
          'id, created_at, user_name, image, user_desc, num_followers, num_following, user_uniqid, is_cpp',
        )
        .eq('user_uniqid', user_uniqid)
        .single(); // Since we expect only one user to match the ID

      if (error) {
        return new Response(JSON.stringify({ error: error.message }), {
          status: 400,
        });
      }

      const { data: followStatus, error: followStatusError } = await supabase
        .rpc('check_follow_status', {
          authuserid: authUserId,
          following_user_uniqid: user_uniqid,
        })
        .returns<boolean>();

      if (followStatusError) {
        console.log('error', followStatusError);
        return new Response(
          JSON.stringify({
            error: followStatusError.message,
            trigger: 'count_votes',
          }),
          { status: 500 },
        );
      }

      // 创建公开profile对象
      const profile: Profile = {
        id: data.id || '',
        authUserId: data.id || '', // 确保不是 undefined
        user_uniqid: data.user_uniqid || '',
        created_at: data.created_at || '',
        user_name: data.user_name || '',
        image: data.image || '',
        user_desc: data.user_desc || '',
        num_followers: data.num_followers || 0,
        num_following: data.num_following || 0,
        followed: (followStatus as boolean) || false,

        credit: 0, // 不暴露用户积分
        date_checkin: '',
        date_post: '',
        date_like: '',
        tour_completed: false,
        is_cpp: data.is_cpp || false,
        num_posts: 0, // 初始值，下面会更新
        plan: 'Free', // 初始值，下面会更新
        email: '',
      };

      // 获取用户的posts数量
      const { count: postsCount, error: postsError } = await supabase
        .from('AppPosts')
        .select('id', { count: 'exact', head: true })
        .eq('authUserId', data.id);

      if (postsError) {
        console.error('Error fetching posts count:', postsError);
        profile.num_posts = 0;
      } else {
        profile.num_posts = postsCount || 0;
      }

      // 添加VIP计划信息 - 直接查询Subscriptions表
      // const tableName = process.env.MODE === 'development' ? 'Subscriptions_test' : 'Subscriptions';
      const now = Math.floor(Date.now() / 1000);

      const { data: subscriptionData, error: subError } = await supabase
        .from('Subscriptions')
        .select('plan, plan_code, status, expires, period_expires')
        .eq('user_id', data.id)
        .gt('expires', now)
        .order('expires', { ascending: false });
      if (subError) {
        console.error('Error fetching subscription:', subError);
        profile.plan = 'Free';
      } else if (subscriptionData && subscriptionData.length > 0) {
        // 检查订阅是否在有效期内（expires > now 已经在查询中过滤了）
        // 对于有 period_expires 的订阅，还需要检查 period_expires
        const activeSubscriptions = subscriptionData.filter(
          sub =>
            // 如果没有 period_expires 或者 period_expires > now，则认为有效
            !sub.period_expires || sub.period_expires > now,
        );
        // 优先显示非CPP的VIP计划，如果没有则显示CPP，最后才是Free
        const vipPlan = activeSubscriptions.find(s => s.plan !== 'CPP')?.plan;
        // const cppPlan = activeSubscriptions.find(s => s.plan === 'CPP')?.plan;
        // profile.plan = vipPlan || cppPlan || 'Free';
        profile.plan = vipPlan || 'Free';
      } else {
        profile.plan = 'Free';
      }

      // 为public profile也添加plan_codes字段
      (profile as any).plan_codes =
        subscriptionData?.map(s => s.plan_code) || [];

      return new Response(JSON.stringify(profile), {
        status: 200,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // ! EDIT PROFILE
    if (method == 'edit-profile') {
      console.log('editing profile');
      console.log(authUserId, new_username, new_bio);
      const { data, error } = await supabase
        .from('User')
        .update({
          user_name: new_username,
          user_desc: new_bio,
          image: new_image,
        })
        .eq('id', authUserId);

      if (error) {
        return new Response(JSON.stringify({ error: error.message }), {
          status: 400,
        });
      }
      console.log('edit success');
      return new Response(
        JSON.stringify({ message: 'Profile updated successfully', data }),
        { status: 200, headers: { 'Content-Type': 'application/json' } },
      );
    }

    return new Response(JSON.stringify({ message: 'success' }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error(error);
    if (error instanceof Error) {
      return new Response(JSON.stringify({ error: error.message }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return new Response(JSON.stringify({ error }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}
